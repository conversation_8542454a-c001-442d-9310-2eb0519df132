{"Rules": [{"Conditions": [{"$type": "DD.ProfileManager.Conditions.StatusEffectCondition, DD", "buffId": "demon_form_spell_gem_buff", "checkType": "层数", "operator": "不包含", "threshold": 0.0, "component": null}], "delayBetweenRuns": 2.0, "Enabled": true, "Name": "变身", "SkillName": "惡魔變身_808000C3", "Key": 0, "IsHold": false, "IsEvade": false, "EvadeRange": 0, "HoldTime": 1.0, "IsUserCooldown": true, "IsWalkable": true, "IsMove": false, "Rarity": []}, {"Conditions": [{"$type": "DD.ProfileManager.Conditions.StatusEffectCondition, DD", "buffId": "demon_form_spell_gem_buff", "checkType": "层数", "operator": "包含", "threshold": 0.0, "component": null}, {"$type": "DD.ProfileManager.Conditions.StatusEffectCondition, DD", "buffId": "屏障祈願", "checkType": "层数", "operator": "小于", "threshold": 34.0, "component": {"$type": "DD.ProfileManager.Component.Wait, DD", "duration": 0.5}}], "delayBetweenRuns": 0.2, "Enabled": true, "Name": "导电", "SkillName": "導電_80C200C3", "Key": 0, "IsHold": false, "IsEvade": false, "EvadeRange": 30, "HoldTime": 1.0, "IsUserCooldown": true, "IsWalkable": true, "IsMove": false, "Rarity": []}, {"Conditions": [{"$type": "DD.ProfileManager.Conditions.StatusEffectCondition, DD", "buffId": "屏障祈願", "checkType": "层数", "operator": "大于", "threshold": 2.0, "component": null}, {"$type": "DD.ProfileManager.Conditions.VitalsCondition, DD", "operator": "大于", "vitalType": "能量护盾", "threshold": 4000, "component": null}], "delayBetweenRuns": 0.231, "Enabled": true, "Name": "祈願", "SkillName": "屏障祈願_80C00001", "Key": 0, "IsHold": false, "IsEvade": true, "EvadeRange": 20, "HoldTime": 1.0, "IsUserCooldown": true, "IsWalkable": true, "IsMove": false, "Rarity": [0, 1, 2, 3]}, {"Conditions": [{"$type": "DD.ProfileManager.Conditions.StatusEffectCondition, DD", "buffId": "屏障祈願", "checkType": "层数", "operator": "大于", "threshold": 2.0, "component": null}, {"$type": "DD.ProfileManager.Conditions.VitalsCondition, DD", "operator": "大于", "vitalType": "能量护盾", "threshold": 3000, "component": null}], "delayBetweenRuns": 0.35, "Enabled": false, "Name": "祈願2", "SkillName": "屏障祈願_80C10001", "Key": 0, "IsHold": false, "IsEvade": false, "EvadeRange": 20, "HoldTime": 1.0, "IsUserCooldown": true, "IsWalkable": false, "IsMove": false, "Rarity": [0, 2, 1, 3]}, {"Conditions": [], "delayBetweenRuns": 5.0, "Enabled": true, "Name": "导电2", "SkillName": "導電_80C200C3", "Key": 0, "IsHold": false, "IsEvade": false, "EvadeRange": 40, "HoldTime": 1.0, "IsUserCooldown": true, "IsWalkable": false, "IsMove": false, "Rarity": [3, 2, 1]}, {"Conditions": [], "delayBetweenRuns": 0.0, "Enabled": false, "Name": "主动闪现", "SkillName": "主动闪现_80C51001", "Key": 0, "IsHold": false, "IsEvade": false, "EvadeRange": 0, "HoldTime": 1.0, "IsUserCooldown": true, "IsWalkable": false, "IsMove": false, "Rarity": [0, 1, 2, 3]}]}