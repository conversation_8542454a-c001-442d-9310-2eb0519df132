﻿# @RF_MODE_HIDE
# Poe2文子过滤V0.02
# 更新时间：2025.4.3
# 1 通货

# 1.1 通货 - 超级通货

Show #  通货 - 超级通货 - 卡兰德的魔镜
    Class "Stackable Currency"
    BaseType "Mirror of Kalandra"
    SetTextColor 255 255 255 # 魔镜色
    SetBackgroundColor 255 40 0 220 # 魔镜色
    SetBorderColor 255 40 0 220 # 魔镜色
    SetFontSize 70 # 魔镜色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\卡德兰的魔镜.mp3"   
    MinimapIcon 0 Red Star
    PlayEffect Red 

Show #  通货 - 超级通货 - 破碎石（0.2添加）
    Class "Stackable Currency"
    BaseType "Fracturing Orb"
    SetTextColor 255 255 255 255 #超级价值色
    SetBackgroundColor 255 40 0 220 #超级价值色
    SetBorderColor 175 96 37 #超级价值色
    SetFontSize 60 #超级价值色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\破碎石.mp3"
    MinimapIcon 0 Red Cross
    PlayEffect Red

Show #  通货 - 超级通货 - 神圣石
    Class "Stackable Currency"
    BaseType "Divine Orb"
    SetTextColor 255 0 0 #崇高价值色
    SetBackgroundColor 255 255 255 #崇高价值色
    SetBorderColor 255 0 0 #崇高价值色
    SetFontSize 60 #崇高价值色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\神圣石.mp3"
    MinimapIcon 0 Red Circle
    PlayEffect Red

Hide # 通货 - 超级通货 - 完美工匠石
    Class "Stackable Currency"
    BaseType "Perfect Jeweller's Orb"
    SetTextColor 255 0 0 #崇高价值色
    SetBackgroundColor 255 255 255 #崇高价值色
    SetBorderColor 255 0 0 #崇高价值色
    SetFontSize 60 #崇高价值色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\恭喜发财.mp3"
    MinimapIcon 0 Red Circle
    PlayEffect Red


# 1.2 通货 - 高级通货

Show # 通货 - 高级通货 - 剥离石
    Class "Stackable Currency"
    BaseType "Orb of Annulment" 
    SetTextColor 0 0 255 #剥离色
    SetBackgroundColor 255 255 255 #剥离色
    SetBorderColor 0 0 255 #剥离色
    SetFontSize 50 #剥离色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\剥离石.mp3"
    MinimapIcon 0 Pink Circle
    PlayEffect Pink

Hide # 通货 - 高级通货 - 高阶工匠石
    Class "Stackable Currency"
    BaseType "Greater Jeweller's Orb
    SetTextColor 255 255 255 #高阶六分仪色
    SetBackgroundColor 255 165 0 #高阶六分仪色
    SetBorderColor 255 255 255 #高阶六分仪色
    SetFontSize 50 #高阶六分仪色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\财源滚滚.mp3"
    MinimapIcon 1 Red Circle
    PlayEffect Red

Show # 通货 - 高级通货 - 白化羽毛
    Class "Stackable Currency"
    BaseType "Albino Rhoa Feather"
    SetTextColor 255 0 0 #崇高价值色
    SetBackgroundColor 255 255 255 #崇高价值色
    SetBorderColor 255 0 0 #崇高价值色
    SetFontSize 60 #崇高价值色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\财源滚滚.mp3"
    MinimapIcon 0 Red Star
    PlayEffect Red


# 1.3 通货 - 价值通货

Hide # 通货 - 价值通货 - 崇高石
    Class "Stackable Currency"
    BaseType "Exalted Orb"
    SetTextColor 255 128 0 #神圣价值色
    SetBackgroundColor 255 255 255 #神圣价值色
    SetBorderColor 255 128 0 #神圣价值色
    SetFontSize 55 #神圣价值色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\崇高石.mp3"
    MinimapIcon 0 Orange Circle
    PlayEffect Orange

Show # 通货 - 价值通货 - 重组器
    Class "Stackable Currency"
    BaseType " Recombinator" 
    SetTextColor 255 255 255 #重组器色
    SetBackgroundColor 255 165 0 #重组器色
    SetBorderColor 255 255 0 #重组器色
    SetFontSize 48
    DisableDropSound True
    CustomAlertSound "文子过滤音效\重组器.mp3"
    MinimapIcon 0 Orange Star
    PlayEffect Orange

Hide # 通货 - 价值通货 - 机会石
    Class "Stackable Currency"
    BaseType "Orb of Chance"
    SetTextColor 255 255 255 #重组器色
    SetBackgroundColor 255 165 0 #重组器色
    SetBorderColor 255 255 0 #重组器色
    SetFontSize 48
    DisableDropSound True
    CustomAlertSound "文子过滤音效\666.mp3"
    MinimapIcon 1 Blue Circle
    PlayEffect Blue

Show # 通货 - 价值通货 -  混沌
    Class "Stackable Currency"
    BaseType "Chaos Orb"
    SetTextColor 255 0 255 #混沌色
    SetBorderColor 255 0 255 #混沌色
    SetFontSize 45 #混沌色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\混沌石.mp3"
    MinimapIcon 1 Pink Circle
    PlayEffect Pink

Hide # 通货 - 价值通货 -  瓦尔
    Class "Stackable Currency"
    BaseType "Vaal Orb"
    SetTextColor 255 0 255 #混沌色
    SetBorderColor 255 0 255 #混沌色
    SetFontSize 45 #混沌色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\瓦尔珠宝.mp3"
    MinimapIcon 2 Cyan Circle
    PlayEffect Cyan

Hide # 通货 - 价值通货 -  宝石匠棱镜
    Class "Stackable Currency"
    BaseType "Gemcutter's Prism"
    SetTextColor 255 0 255 #混沌色
    SetBorderColor 255 0 255 #混沌色
    SetFontSize 45 #混沌色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\宝石匠.mp3"
    MinimapIcon 1 Blue Circle
    PlayEffect Blue

Hide # 通货 - 价值通货 - 迷宫钥匙
    Class "Stackable Currency"
    BaseType "Silver Key" "Bronze Key" "Gold Key"
    SetTextColor 210 0 0 #迷宫钥匙色
    SetBorderColor 210 0 0 #迷宫钥匙色
    SetFontSize 50 #迷宫钥匙色
    DisableDropSound True
    MinimapIcon 0 Cyan Triangle
    PlayEffect Cyan

Hide # 通货 - 价值通货 - 待雕刻的宝石
    Class "Stackable Currency"
    BaseType "Uncarved Gemstone"
    SetTextColor 255 255 255 #低阶六分仪色
    SetBackgroundColor 128 128 0 #低阶六分仪色
    SetBorderColor 255 255 255 #低阶六分仪色
    SetFontSize 43 #低阶六分仪色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\技能宝石.mp3"
    MinimapIcon 1 Blue Star
    PlayEffect Blue


# 1.4  通货 - 普通通货

Hide # 通货 - 普通通货 -  富豪
    Class "Stackable Currency"
    BaseType "Regal Orb"
    SetTextColor 0 0 0 #点金色
    SetBackgroundColor 255 165 0 #点金色
    SetBorderColor 0 0 0 #点金色
    SetFontSize 43 #点金色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\富豪石.mp3"
    MinimapIcon 1 Yellow Circle
    PlayEffect Yellow

Hide # 通货 - 普通通货 -  点金
    Class "Stackable Currency"
    BaseType "Orb of Alchemy"
    SetTextColor 0 0 0 #点金色
    SetBackgroundColor 255 165 0 #点金色
    SetBorderColor 0 0 0 #点金色
    SetFontSize 43 #点金色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\点金石.mp3"
    MinimapIcon 1 Yellow Circle

Hide # 通货 - 低价通货 - 护甲片
    Class "Stackable Currency"
    BaseType "Armourer's Scrap"
    SetTextColor 0 0 0 #污秽改造色
    SetBackgroundColor 127 235 48 #污秽改造色
    SetBorderColor 128 0 0 #污秽改造色
    SetFontSize 41 #污秽改造色
    DisableDropSound True

Hide # 通货 - 普通通货 - 工匠石
    Class "Stackable Currency"
    BaseType "Artificer's Orb"
    SetTextColor 0 0 0 #改造色
    SetBackgroundColor 127 235 48 #改造色
    SetBorderColor 0 0 0 #改造色
    SetFontSize 41 #改造色
    DisableDropSound True

Hide # 通货 - 普通通货 - 玻璃弹珠
    Class "Stackable Currency"
    BaseType "Glassblower's Bauble"
    SetTextColor 0 0 0 #改造色
    SetBackgroundColor 127 235 48 #改造色
    SetBorderColor 0 0 0 #改造色
    SetFontSize 41 #改造色
    DisableDropSound True

Hide # 通货 - 普通通货 - 奥术师的铭刻
    Class "Stackable Currency"
    BaseType "Arcanist's Etcher"
    SetTextColor 0 0 0 #改造色
    SetBackgroundColor 127 235 48 #改造色
    SetBorderColor 0 0 0 #改造色
    SetFontSize 41 #改造色
    DisableDropSound True


# 1.5  通货 - 低价通货

Hide # 通货 - 低价通货 - 低价工匠石
    Class "Stackable Currency"
    BaseType "Lesser Jeweller's Orb"
    SetTextColor 0 0 0 #改造色
    SetBackgroundColor 127 235 48 #改造色
    SetBorderColor 0 0 0 #改造色
    SetFontSize 41 #改造色
    DisableDropSound True

Hide # 通货 - 低价通货 - 蜕变石
    Class "Stackable Currency"
    BaseType "Orb of Transmutation"
    SetTextColor 0 0 0 #改造色
    SetBackgroundColor 127 235 48 #改造色
    SetBorderColor 0 0 0 #改造色
    SetFontSize 41 #改造色
    DisableDropSound True

Hide # 通货 - 低价通货 - 增幅石
    Class "Stackable Currency"
    BaseType "Orb of Augmentation"
    SetTextColor 0 0 0 #改造色
    SetBackgroundColor 127 235 48 #改造色
    SetBorderColor 0 0 0 #改造色
    SetFontSize 41 #改造色
    DisableDropSound True

Hide # 通货 - 低价通货 - 磨刀石
    Class "Stackable Currency"
    BaseType "Blacksmith's Whetstone"
    SetTextColor 0 0 0 #改造色
    SetBackgroundColor 127 235 48 #改造色
    SetBorderColor 0 0 0 #改造色
    SetFontSize 41 #改造色
    DisableDropSound True

Hide # 通货 - 通货碎片类 - 机会碎片
    Class "Stackable Currency"
    BaseType "Chance Shard"
    SetTextColor 255 0 255 #混沌碎片色
    SetFontSize 38 #混沌碎片色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\加油.mp3"
    MinimapIcon 1 Cyan Circle
    PlayEffect Cyan

Hide # 通货 - 低价通货 - 堆叠的鉴定大于2
    Class "Stackable Currency"
    BaseType "Scroll of Wisdom"
    StackSize >= 2
    SetFontSize 38
    DisableDropSound True
   
Hide # 通货 - 低价通货 - 堆叠的鉴定小于2
    Class "Stackable Currency"
    BaseType "Scroll of Wisdom"
    StackSize < 2
    SetFontSize 38
    DisableDropSound True
    
Hide # 通货 - 低价通货 - 鉴定
    Class "Stackable Currency"
    BaseType "Scroll of Wisdom"
    SetFontSize 38
    DisableDropSound True

Hide # 通货 - 低价通货 - 金币1000+
    Class "Stackable Currency"
    BaseType "Gold"
    StackSize >= 1000
    SetTextColor 225 165 0 180 #点金碎片色
    SetFontSize 35 #点金碎片色
    DisableDropSound True

Hide # 通货 - 低价通货 - 金币
    Class "Stackable Currency"
    BaseType "Gold"
    StackSize >= 1
    SetTextColor 225 165 0 180 #点金碎片色
    SetFontSize 35 #点金碎片色
    DisableDropSound True

Hide # 通货 - 低价通货 - 富豪碎片，工匠石碎片
    Class "Stackable Currency"
    BaseType "Regal Shard" "Artificer's Shard"
    SetTextColor 127 235 48 160 #改造碎片色
    SetFontSize 33 #改造碎片色
    DisableDropSound True

Hide # 通货 - 通货碎片类 - 改造碎片 ，蜕变碎片
    Class "Stackable Currency"
    BaseType "Alteration Shard" "Transmutation Shard"
    SetFontSize 33
    DisableDropSound True



# 2 特殊通货


# 2.1 特殊通货 - 精华

Show # 特殊通货- 精华 - 精华超级(原8级精华)
    Class "Stackable Currency"
    BaseType "Essence of Hysteria" "Essence of Horror" "Essence of Insanity" "Essence of Delirium"
    SetTextColor 255 255 255 #价值精华色
    SetBackgroundColor 255 165 0 #价值精华色
    SetBorderColor 255 0 0 #价值精华色
    SetFontSize 48 #价值精华色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\精华.mp3"
    MinimapIcon 0 Orange Circle
    PlayEffect Orange

Show # 特殊通货 - 精华 - 高级精华
    Class "Stackable Currency"
    BaseType "Greater Essence of"
    SetTextColor 255 255 255 #中阶六分仪色
    SetBackgroundColor 198 99 0 #中阶六分仪色
    SetBorderColor 206 103 0 #中阶六分仪色
    SetFontSize 45 #中阶六分仪色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\精华.mp3"
    MinimapIcon 1 Pink Circle
    PlayEffect Pink

Hide # 特殊通货 - 精华 - 普通精华
    Class "Stackable Currency"
    BaseType "Essence of"
    SetTextColor 0 0 0 #点金色
    SetBackgroundColor 255 165 0 #点金色
    SetBorderColor 0 0 0 #点金色
    SetFontSize 43 #点金色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\精华.mp3"
    MinimapIcon 1 Blue Circle
    PlayEffect Blue


# 2.2 特殊通货 - 符文

Hide # 特殊通货 - 符文 - 价值符文
    Class "Socketable" 
    BaseType "Iron Rune"
    SetTextColor 200 200 0 # 低阶价值色
    SetBackgroundColor 73 56 42 255 # 低阶价值色
    SetBorderColor 255 128 0 # 低阶价值色
    SetFontSize 45
    DisableDropSound True
    MinimapIcon 1 Yellow Star
    PlayEffect Yellow

Hide # 特殊通货 - 符文 - 特殊符文
    Class "Socketable" 
    BaseType "Greater Rune of "
    SetTextColor 255 0 255 #混沌色
    SetBorderColor 255 0 255 #混沌色
    SetFontSize 45 #混沌色
    DisableDropSound True
    MinimapIcon 1 Pink Star
    PlayEffect Pink

Hide # 特殊通货 - 符文 - 高阶符文
    Class "Socketable" 
    BaseType "Greater "
    SetTextColor 200 200 0 # 低阶价值色
    SetBackgroundColor 73 56 42 255 # 低阶价值色
    SetBorderColor 255 128 0 # 低阶价值色
    SetFontSize 45
    DisableDropSound True
    MinimapIcon 1 Yellow Star
    PlayEffect Yellow

Show # 特殊通货 - 符文 - 中阶符文
    Class "Socketable" 
    BaseType " Rune"
    SetTextColor 0 0 0 #点金色
    SetBackgroundColor 255 165 0 #点金色
    SetBorderColor 0 0 0 #点金色
    SetFontSize 43 #点金色
    DisableDropSound True
    MinimapIcon 1 Yellow Star
    PlayEffect Yellow

Hide # 特殊通货 - 符文 - 低阶符文
    Class "Socketable" 
    BaseType "Lesser "
    SetBorderColor 180 96 0 160 #蜕变色
    SetFontSize 39 #蜕变色
    DisableDropSound True

Hide # 特殊通货 - 符文 - 全部符文
    Class "Socketable" 
    BaseType " Rune"
    SetBorderColor 180 96 0 160 #蜕变色
    SetFontSize 39 #蜕变色
    DisableDropSound True


# 2.3 特殊通货 - 灵魂核心

Show # 特殊通货 - 灵魂核心 - 全部核心
    Class "Socketable" 
    BaseType "Soul Core of "
    SetTextColor 200 200 0 # 低阶价值色
    SetBackgroundColor 73 56 42 255 # 低阶价值色
    SetBorderColor 255 128 0 # 低阶价值色
    SetFontSize 45
    DisableDropSound True
    MinimapIcon 1 Yellow Star
    PlayEffect Yellow


# 2.4 特殊通货 - 魔符

Show # 特殊通货 - 魔符 - 全部魔符
    Class "Socketable" 
    BaseType " Talisman"
    SetTextColor 200 200 0 # 低阶价值色
    SetBackgroundColor 73 56 42 255 # 低阶价值色
    SetBorderColor 255 128 0 # 低阶价值色
    SetFontSize 45
    DisableDropSound True
    MinimapIcon 1 Yellow Star
    PlayEffect Yellow


# 2.5 特殊通货 - 圣物厅钥匙

Show # 特殊通货 - 圣物厅钥匙 - 全部圣物厅钥匙
    Class "Vault Keys"
    SetTextColor 255 0 0 #崇高价值色
    SetBackgroundColor 255 255 255 #崇高价值色
    SetBorderColor 255 0 0 #崇高价值色
    SetFontSize 60 #崇高价值色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\遗物厅钥匙.mp3"
    MinimapIcon 0 Orange Star
    PlayEffect Orange


# 3 赛季通货

# 3.1  赛季通货 - 梦魇联盟

Show # 赛季通货- 梦魇联盟 - 梦魇拟像
    Class "Map Fragment"
    BaseType "Simulacrum"
    SetTextColor 255 255 255 255 #超级价值色
    SetBackgroundColor 255 40 0 220 #超级价值色
    SetBorderColor 175 96 37 #超级价值色
    SetFontSize 60 #超级价值色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\梦魇拟像.mp3"
    MinimapIcon 0 Red Hexagon
    PlayEffect Red

Show # 赛季通货- 梦魇联盟 - 幻像断片 堆叠 >5
    Class "Stackable Currency"
    BaseType "Simulacrum Splinter"
    StackSize >= 5
    SetTextColor 255 150 184 #高阶裂片色
    SetBackgroundColor 132 28 31 255 #高阶裂片色
    SetBorderColor 240 240 240 180 #高阶裂片色
    SetFontSize 45 #高阶裂片色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\拟像裂片.mp3"
    MinimapIcon 1 Pink Diamond
    PlayEffect Pink

Hide # 赛季通货 - 梦魇联盟 - 幻像断片 堆叠 >2
    Class "Stackable Currency"
    BaseType "Simulacrum Splinter"
    StackSize >= 2
    SetTextColor 255 150 184 #高阶裂片色
    SetBackgroundColor 132 28 31 255 #高阶裂片色
    SetBorderColor 240 240 240 180 #高阶裂片色
    SetFontSize 45 #高阶裂片色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\拟像裂片.mp3"
    MinimapIcon 1 Pink Diamond
    PlayEffect Pink

Hide # 赛季通货 - 梦魇联盟 - 迷雾碎片
    Class "Stackable Currency"
    BaseType "Simulacrum Splinter"
    SetTextColor 255 150 184 #高阶裂片色
    SetBackgroundColor 132 28 31 255 #高阶裂片色
    SetBorderColor 240 240 240 180 #高阶裂片色
    SetFontSize 45 #高阶裂片色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\拟像裂片.mp3"
    MinimapIcon 1 Pink Diamond
    PlayEffect Pink

Show # 赛季通货 - 梦魇联盟 - 超价精炼
    Class "Stackable Currency"
    BaseType "Distilled Isolation" "Distilled Suffering"
    SetTextColor 255 69 0 225 #高阶价值色
    SetBackgroundColor 73 56 42 255 #高阶价值色
    SetBorderColor 255 186 51 255 #高阶价值色
    SetFontSize 50 #高阶价值色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\恭喜发财.mp3"
    MinimapIcon 0 Red Cross
    PlayEffect Red

Show # 赛季通货 - 梦魇联盟 - 高价精炼
    Class "Stackable Currency"
    BaseType "Distilled Fear" "Distilled Despair"
    SetTextColor 255 69 0 225 #高阶价值色
    SetBackgroundColor 73 56 42 255 #高阶价值色
    SetBorderColor 255 186 51 255 #高阶价值色
    SetFontSize 50 #高阶价值色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\高级圣油.mp3"
    MinimapIcon 0 Orange Cross
    PlayEffect Orange

Show # 赛季通货 - 梦魇联盟 - 中价精炼
    Class "Stackable Currency"
    BaseType "Distilled Disgust" "Distilled Paranoia"
    SetTextColor 255 186 51 255 #中阶价值色
    SetBackgroundColor 73 56 42 255 #中阶价值色
    SetBorderColor 250 250 250 255 #中阶价值色
    SetFontSize 50 #中阶价值色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\高级圣油.mp3"
    MinimapIcon 1 Pink Cross
    PlayEffect Pink

Show # 赛季通货 - 梦魇联盟 - 全部精炼
    Class "Stackable Currency"
    BaseType "Distilled "
    SetTextColor 200 200 0 #低阶价值色
    SetBackgroundColor 73 56 42 255 #低阶价值色
    SetBorderColor 255 128 0 #低阶价值色
    SetFontSize 43 #低阶价值色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\圣油.mp3"
    MinimapIcon 1 Cyan Cross
    PlayEffect Cyan


# 3.2  赛季通货 - 裂隙联盟

Show # 赛季通货 - 裂隙联盟 - 裂隙石
    Class "Breachstone"
    BaseType "Breachstone"
    SetTextColor 255 0 255 #价值地图色
    SetBackgroundColor 255 255 255 #价值地图色
    SetBorderColor 255 0 255 #价值地图色
    SetFontSize 60 #价值地图色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\裂隙石.mp3"
    MinimapIcon 0 Orange Star
    PlayEffect Orange

Hide # 赛季通货 - 裂隙联盟 - 裂隙碎片>5
    Class "Stackable Currency"
    BaseType "Breach Splinter"
    StackSize >= 5
    SetTextColor 0 0 0 #低阶碎片色
    SetBackgroundColor 14 247 235 195 #低阶碎片色
    SetBorderColor 255 255 255 160 #低阶价值色
    SetFontSize 42 #低阶碎片色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\裂隙碎片.mp3"
    MinimapIcon 2 Blue Diamond
    PlayEffect Blue

Hide # 赛季通货 - 裂隙联盟 - 裂隙碎片>2
    Class "Stackable Currency"
    BaseType "Breach Splinter"
    StackSize >= 2
    SetTextColor 0 0 0 #低阶碎片色
    SetBackgroundColor 14 247 235 195 #低阶碎片色
    SetBorderColor 255 255 255 160 #低阶价值色
    SetFontSize 42 #低阶碎片色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\裂隙碎片.mp3"
    MinimapIcon 2 Blue Diamond
    PlayEffect Blue

Hide # 赛季通货 - 裂隙联盟 - 裂隙碎片
    Class "Stackable Currency"
    BaseType "Breach Splinter"
    SetTextColor 0 0 0 #低阶碎片色
    SetBackgroundColor 14 247 235 195 #低阶碎片色
    SetBorderColor 255 255 255 160 #低阶价值色
    SetFontSize 42 #低阶碎片色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\裂隙碎片.mp3"
    MinimapIcon 2 Blue Diamond
    PlayEffect Blue

Show # 赛季通货 - 裂隙联盟 - 高价催化剂
    Class "Stackable Currency"
    BaseType "Neural Catalyst" "Adaptive Catalyst" "Tul's Catalyst" "Esh's Catalyst" "Reaver Catalyst"
    SetTextColor 255 186 51 255 #中阶价值色
    SetBackgroundColor 73 56 42 255 #中阶价值色
    SetBorderColor 250 250 250 255 #中阶价值色
    SetFontSize 46 #中阶价值色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\高级催化剂.mp3"
    MinimapIcon 1 Pink Triangle
    PlayEffect Pink

Hide # 赛季通货 - 裂隙联盟 - 全部催化剂
    Class "Stackable Currency"
    BaseType " Catalyst"
    SetTextColor 0 255 255 200 #低阶价值色
    SetBackgroundColor 73 56 42 255 #低阶价值色
    SetFontSize 41 #低阶价值色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\催化剂.mp3"



# 3.3  赛季通货 - 探险联盟

Hide # 赛季通货 - 探险联盟 - 异域铸币
    Class "Stackable Currency"
    BaseType "Exotic Coinage"
    SetTextColor 200 200 0 # 低阶价值色
    SetBackgroundColor 73 56 42 255 # 低阶价值色
    SetBorderColor 255 128 0 # 低阶价值色
    SetFontSize 43
    MinimapIcon 2 Cyan Triangle
    PlayEffect Cyan
    DisableDropSound True
    CustomAlertSound "文子过滤音效\异域铸币.mp3" 

Hide # 赛季通货 - 探险联盟 - 异域文物
    Class "Stackable Currency"
    BaseType "Sun Artifact"
    SetTextColor 0 0 0 #低阶碎片色
    SetBackgroundColor 14 247 235 195 #低阶碎片色
    SetBorderColor 255 255 255 160 #低阶价值色
    SetFontSize 41 #低阶碎片色
    MinimapIcon 2 Cyan Triangle
    PlayEffect Cyan
    DisableDropSound True
    CustomAlertSound "文子过滤音效\神器.mp3" 

Show # 赛季通货 - 探险联盟 - 黒廉文物
    Class "Stackable Currency"
    BaseType "Order Artifact"
    SetTextColor 0 0 0 #低阶碎片色
    SetBackgroundColor 14 247 235 195 #低阶碎片色
    SetBorderColor 255 255 255 160 #低阶价值色
    SetFontSize 41 #低阶碎片色
    MinimapIcon 2 Cyan Triangle
    PlayEffect Cyan
    DisableDropSound True
    CustomAlertSound "文子过滤音效\神器.mp3" 

Show # 赛季通货 - 探险联盟 - 先祖文物
    Class "Stackable Currency"
    BaseType "Black Scythe Artifact"
    SetTextColor 0 0 0 #低阶碎片色
    SetBackgroundColor 14 247 235 195 #低阶碎片色
    SetBorderColor 255 255 255 160 #低阶价值色
    SetFontSize 41 #低阶碎片色
    MinimapIcon 2 Cyan Triangle
    PlayEffect Cyan
    DisableDropSound True
    CustomAlertSound "文子过滤音效\神器.mp3" 

Show # 赛季通货 - 探险联盟 - 黄芪文物
    Class "Stackable Currency"
    BaseType "Broken Circle Artifact"
    SetTextColor 0 0 0 #低阶碎片色
    SetBackgroundColor 14 247 235 195 #低阶碎片色
    SetBorderColor 255 255 255 160 #低阶价值色
    SetFontSize 41 #低阶碎片色
    MinimapIcon 2 Cyan Triangle
    PlayEffect Cyan
    DisableDropSound True
    CustomAlertSound "文子过滤音效\神器.mp3" 

Show # 赛季通货 - 探险联盟 - 传奇探险日志
    Class "Expedition Logbook"
    Rarity = Unique
    SetTextColor 255 0 255 #价值地图色
    SetBackgroundColor 255 255 255 #价值地图色
    SetBorderColor 255 0 255 #价值地图色
    SetFontSize 60 #价值地图色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\传奇日志.mp3"
    MinimapIcon 0 Orange Star
    PlayEffect Orange

Show # 赛季通货 - 探险联盟 - 高等探险日志79+
    Class "Expedition Logbook"
    ItemLevel >= 79
    SetTextColor 255 0 255 #价值地图色
    SetBackgroundColor 255 255 255 #价值地图色
    SetBorderColor 255 0 255 #价值地图色
    SetFontSize 60 #价值地图色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\高级日志.mp3"
    MinimapIcon 0 Orange Star
    PlayEffect Orange

Show # 赛季通货 - 探险联盟 - 探险日志
    Class "Expedition Logbook"
    SetTextColor 255 0 255 #价值地图色
    SetBackgroundColor 255 255 255 #价值地图色
    SetBorderColor 255 0 255 #价值地图色
    SetFontSize 60 #价值地图色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\日志.mp3"
    MinimapIcon 0 Orange Star
    PlayEffect Orange


# 3.4  赛季通货 - 驱灵联盟

Show # 赛季通货 - 驱灵联盟 - 高价预兆
    Class "Stackable Currency"
    BaseType "Omen of Dextral Annulment" "Omen of Sinistral Annulment" "Omen of Dextral Erasure" "Omen of Sinistral Erasure" "Omen of Whittling"
    SetTextColor 255 0 0 #崇高价值色
    SetBackgroundColor 255 255 255 #崇高价值色
    SetBorderColor 255 0 0 #崇高价值色
    SetFontSize 60 #崇高价值色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\恭喜发财.mp3"
    MinimapIcon 0 Orange Cross
    PlayEffect Orange

Show # 赛季通货 - 驱灵联盟 - 中价预兆
    Class "Stackable Currency"
    BaseType "Omen of Amelioration" "Omen of Corruption" "Omen of Resurgence"
    SetTextColor 255 69 0 225 #高阶价值色 
    SetBackgroundColor 73 56 42 255 #高阶价值色
    SetBorderColor 255 186 51 255 #高阶价值色
    SetFontSize 50 #高阶价值色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\预兆.mp3"
    MinimapIcon 1 Yellow Star
    PlayEffect Yellow

Show # 赛季通货 - 驱灵联盟 - 全部预兆
    Class "Stackable Currency"
    BaseType "Omen of "
    SetTextColor 255 186 51 255 #预兆色
    SetBackgroundColor 73 56 42 255 #预兆色
    SetBorderColor 255 0 0 #预兆色
    SetFontSize 40 #预兆色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\预兆.mp3"
    MinimapIcon 1 Yellow Star
    PlayEffect Yellow

Show # 赛季通货 - 驱灵联盟 - 晋见帝王
    Class "Map Fragments"
    BaseType "An Audience With The King"
    SetTextColor 255 0 0 #崇高价值色
    SetBackgroundColor 255 255 255 #崇高价值色
    SetBorderColor 255 0 0 #崇高价值色
    SetFontSize 60 #崇高价值色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\地图碎片.mp3"
    MinimapIcon 0 Orange Diamond
    PlayEffect Orange





# 4 地图类



# 4.1 地图类 - 升华试炼物品

Hide # 地图类 - 升华试炼物品 - 巨灵之币80级+
    Class "Trial Coins"
    ItemLevel >= 80
    SetTextColor 255 255 0 #绿门碎片色
    SetBorderColor 0 255 0 #绿门碎片色
    SetFontSize 50 #绿门碎片色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\圣殿探查.mp3"
    MinimapIcon 1 Yellow Star
    PlayEffect Yellow

Hide # 地图类 - 升华试炼物品 - 巨灵之币75级
    Class "Trial Coins"
    ItemLevel = 75
    SetTextColor 255 255 0 #绿门碎片色
    SetBorderColor 0 255 0 #绿门碎片色
    SetFontSize 50 #绿门碎片色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\圣殿探查.mp3"
    MinimapIcon 1 Yellow Star
    PlayEffect Yellow

Hide # 地图类 - 升华试炼物品 - 巨灵之币
    Class "Trial Coins"
    SetTextColor 100 255 155 #红门碎片色
    SetBorderColor 100 255 155 180 #红门碎片色
    SetFontSize 45 #红门碎片色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\圣殿探查.mp3"

Hide # 地图类 - 升华试炼物品 - 最后通牒雕刻80级
    Class "Inscribed Ultimatum"
    ItemLevel >= 80
    SetTextColor 255 255 0 #绿门碎片色
    SetBorderColor 0 255 0 #绿门碎片色
    SetFontSize 50 #绿门碎片色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\贪婪战书.mp3"
    MinimapIcon 1 Yellow Star
    PlayEffect Yellow

Hide # 地图类 - 升华试炼物品 - 最后通牒雕刻75级
    Class "Inscribed Ultimatum"
    ItemLevel = 75
    SetTextColor 255 255 0 #绿门碎片色
    SetBorderColor 0 255 0 #绿门碎片色
    SetFontSize 50 #绿门碎片色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\贪婪战书.mp3"
    MinimapIcon 1 Yellow Star
    PlayEffect Yellow

Hide # 地图类 - 升华试炼物品 - 最后通牒雕刻
    Class "Inscribed Ultimatum"
    SetTextColor 100 255 155 #红门碎片色
    SetBorderColor 100 255 155 180 #红门碎片色
    SetFontSize 45 #红门碎片色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\贪婪战书.mp3"

Show # 地图类 - 升华试炼物品 - 高价暗金遗物
    Class "Relic"
    BaseType "Incense Relic" "Vase Relic"
    Rarity = Unique
    SetTextColor 255 255 255 #超级价值色
    SetBackgroundColor 255 40 0 220 #超级价值色
    SetBorderColor 175 96 37 #超级价值色
    SetFontSize 60 #超级价值色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\恭喜发财.mp3"
    MinimapIcon 0 Red Star
    PlayEffect Red

Show # 地图类 - 升华试炼物品 - 暗金遗物
    Class "Relic"
    Rarity = Unique
    SetTextColor 255 255 255 #仿品暗金色
    SetBackgroundColor 255 160 115 #仿品暗金色
    SetBorderColor 164 0 82 #仿品暗金色
    SetFontSize 50 #仿品暗金色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\遗物.mp3"
    MinimapIcon 0 Red Star
    PlayEffect Red

Show # 地图类 - 升华试炼物品 - 全部遗物
    Class "Relic"
    SetBackgroundColor 73 56 42 205 #遗物色
    SetBorderColor 0 255 0 150 #遗物色
    SetFontSize 43 #遗物色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\遗物.mp3"
    PlayEffect Blue


	
# 4.2 地图类 - 碎片类

Show # 地图类 - 碎片类 - 高价巅峰钥匙
    Class "Pinnacle Keys"
    BaseType "Ancient Crisis Fragment"
    SetTextColor 255 0 0 #崇高价值色
    SetBackgroundColor 255 255 255 #崇高价值色
    SetBorderColor 255 0 0 #崇高价值色
    SetFontSize 60 #崇高价值色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\遗物厅钥匙.mp3"
    MinimapIcon 0 Red Star
    PlayEffect Red

Show # 地图类 - 碎片类 - 巅峰钥匙
    Class "Pinnacle Keys"
    SetTextColor 0 240 240 #镜像碎块色
    SetBackgroundColor 73 56 42 255 #镜像碎块色
    SetBorderColor 255 0 0 #镜像碎块色
    SetFontSize 45 #镜像碎块色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\遗物厅钥匙.mp3"
    MinimapIcon 1 Orange Star
    PlayEffect Orange

Show # 地图碎片 - 碎片类 - 混沌试炼碎片
    Class "Map Fragments"
    BaseType "Cowardly Fate" "Deadly Fate" "Victorious Fate"
    SetTextColor 255 128 192 #高级地图碎片色
    SetBorderColor 255 128 192 #高级地图碎片色
    SetFontSize 50 #高级地图碎片色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\地图碎片.mp3"
    MinimapIcon 0 Orange Diamond
    PlayEffect Orange



# 4.3 地图类 - 碑牌

Show # 地图类 - 碑牌 -  高价碑牌
    Class "Tablet"
    BaseType "Ritual Precursor Tablet"
    SetTextColor 255 0 255 #价值地图色
    SetBackgroundColor 255 255 255 #价值地图色
    SetBorderColor 255 0 255 #价值地图色
    SetFontSize 60 #价值地图色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\大师六分仪.mp3"
    MinimapIcon 0 Red Star
    PlayEffect Red

Hide # 地图类 - 碑牌 - 玩法碑牌
    Class "Tablet"
    BaseType "Expedition Precursor Tablet" "Delirium Precursor Tablet" "Overseer Precursor Tablet"
    SetTextColor 255 0 255 #价值地图色
    SetBackgroundColor 255 255 255 #价值地图色
    SetBorderColor 255 0 255 #价值地图色
    SetFontSize 60 #价值地图色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\大师六分仪.mp3"
    MinimapIcon 1 Pink Star
    PlayEffect Pink

Show # 地图类 - 碑牌 - 先行者碑牌
    Class "Tablet"
    BaseType "Precursor Tablet"
    SetTextColor 0 0 255 #剥离色
    SetBackgroundColor 255 255 255 #剥离色
    SetBorderColor 0 0 255 #剥离色
    SetFontSize 45 #剥离色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\大师六分仪.mp3"
    MinimapIcon 1 Yellow Star
    PlayEffect Yellow

Hide # 地图类 - 碑牌 - 所有碑牌
    Class "Tablet"
    SetTextColor 0 0 255 #剥离色
    SetBackgroundColor 255 255 255 #剥离色
    SetBorderColor 0 0 255 #剥离色
    SetFontSize 45 #剥离色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\大师六分仪.mp3"
    MinimapIcon 1 Yellow Star
    PlayEffect Yellow



# 4.4 地图类 - 特殊异界地图

Show # 地图类 - 特殊异界地图 - 污秽地图
    Class "Waystones"
    Scourged True
    SetTextColor 133 41 77 255 #特殊地图色
    SetBackgroundColor 255 255 255 255 #特殊地图色
    SetFontSize 50 #特殊地图色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\特殊地图.mp3"
    MinimapIcon 0 Orange Hexagon
    PlayEffect Orange

Show # 地图类 - 特殊异界地图 - 破碎地图（破碎词缀地图）
    Class "Waystones"
    FracturedItem True
    SetTextColor 133 41 77 255 #特殊地图色
    SetBackgroundColor 255 255 255 255 #特殊地图色
    SetFontSize 50 #特殊地图色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\特殊地图.mp3"
    MinimapIcon 0 Orange Hexagon
    PlayEffect Orange



# 4.5 地图类 - 附魔地图

Show # 地图类 - 附魔地图 - T15-16迷雾地图（亢奋，遭遇战等）
    Class "Waystones"
    AnyEnchantment True
    BaseType "Waystone (Tier 15)" "Waystone (Tier 16)"
    SetTextColor 133 41 77 255 #特殊地图色
    SetBackgroundColor 255 255 255 255 #特殊地图色
    SetFontSize 50 #特殊地图色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\特殊地图.mp3"
    MinimapIcon 0 Orange Hexagon
    PlayEffect Orange

Hide # 地图类 - 附魔地图 - T14迷雾地图（亢奋，遭遇战等）
    Class "Waystones"
    AnyEnchantment True
    BaseType "Waystone (Tier 14)"
    SetTextColor 133 41 77 255 #特殊地图色
    SetBackgroundColor 255 255 255 255 #特殊地图色
    SetFontSize 50 #特殊地图色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\特殊地图.mp3"
    MinimapIcon 0 Orange Hexagon
    PlayEffect Orange

Hide # 地图类 - 附魔地图 - T13迷雾地图（亢奋，遭遇战等）
    Class "Waystones"
    AnyEnchantment True
    BaseType "Waystone (Tier 13)"
    SetTextColor 133 41 77 255 #特殊地图色
    SetBackgroundColor 255 255 255 255 #特殊地图色
    SetFontSize 50 #特殊地图色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\特殊地图.mp3"
    MinimapIcon 0 Orange Hexagon
    PlayEffect Orange

Hide # 地图类 - 附魔地图 - T12迷雾地图（亢奋，遭遇战等）
    Class "Waystones"
    AnyEnchantment True
    BaseType "Waystone (Tier 12)"
    SetTextColor 133 41 77 255 #特殊地图色
    SetBackgroundColor 255 255 255 255 #特殊地图色
    SetFontSize 50 #特殊地图色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\特殊地图.mp3"
    MinimapIcon 0 Orange Hexagon
    PlayEffect Orange

Hide # 地图类 - 附魔地图 - T11迷雾地图（亢奋，遭遇战等）
    Class "Waystones"
    AnyEnchantment True
    BaseType "Waystone (Tier 11)"
    SetTextColor 133 41 77 255 #特殊地图色
    SetBackgroundColor 255 255 255 255 #特殊地图色
    SetFontSize 50 #特殊地图色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\特殊地图.mp3"
    MinimapIcon 0 Orange Hexagon
    PlayEffect Orange

Hide # 地图类 - 附魔地图 - T10迷雾地图（亢奋，遭遇战等）
    Class "Waystones"
    AnyEnchantment True
    BaseType "Waystone (Tier 10)"
    SetTextColor 133 41 77 255 #特殊地图色
    SetBackgroundColor 255 255 255 255 #特殊地图色
    SetFontSize 50 #特殊地图色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\特殊地图.mp3"
    MinimapIcon 0 Orange Hexagon
    PlayEffect Orange

Hide # 地图类 - 附魔地图 - T9迷雾地图（亢奋，遭遇战等）
    Class "Waystones"
    AnyEnchantment True
    BaseType "Waystone (Tier 9)"
    SetTextColor 133 41 77 255 #特殊地图色
    SetBackgroundColor 255 255 255 255 #特殊地图色
    SetFontSize 50 #特殊地图色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\特殊地图.mp3"
    MinimapIcon 0 Orange Hexagon
    PlayEffect Orange

Hide # 地图类 - 附魔地图 - T8迷雾地图（亢奋，遭遇战等）
    Class "Waystones"
    AnyEnchantment True
    BaseType "Waystone (Tier 8)"
    SetTextColor 133 41 77 255 #特殊地图色
    SetBackgroundColor 255 255 255 255 #特殊地图色
    SetFontSize 50 #特殊地图色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\特殊地图.mp3"
    MinimapIcon 0 Orange Hexagon
    PlayEffect Orange

Hide # 地图类 - 附魔地图 - T7迷雾地图（亢奋，遭遇战等）
    Class "Waystones"
    AnyEnchantment True
    BaseType "Waystone (Tier 7)"
    SetTextColor 133 41 77 255 #特殊地图色
    SetBackgroundColor 255 255 255 255 #特殊地图色
    SetFontSize 50 #特殊地图色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\特殊地图.mp3"
    MinimapIcon 0 Orange Hexagon
    PlayEffect Orange

Hide # 地图类 - 附魔地图 - T6迷雾地图（亢奋，遭遇战等）
    Class "Waystones"
    AnyEnchantment True
    BaseType "Waystone (Tier 6)"
    SetTextColor 133 41 77 255 #特殊地图色
    SetBackgroundColor 255 255 255 255 #特殊地图色
    SetFontSize 50 #特殊地图色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\特殊地图.mp3"
    MinimapIcon 0 Orange Hexagon
    PlayEffect Orange

Show # 地图类 - 附魔地图 - T5迷雾地图（亢奋，遭遇战等）
    Class "Waystones"
    AnyEnchantment True
    BaseType "Waystone (Tier 5)"
    SetTextColor 133 41 77 255 #特殊地图色
    SetBackgroundColor 255 255 255 255 #特殊地图色
    SetFontSize 50 #特殊地图色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\特殊地图.mp3"
    MinimapIcon 0 Orange Hexagon
    PlayEffect Orange

Show # 地图类 - 附魔地图 - T4迷雾地图（亢奋，遭遇战等）
    Class "Waystones"
    AnyEnchantment True
    BaseType "Waystone (Tier 4)"
    SetTextColor 133 41 77 255 #特殊地图色
    SetBackgroundColor 255 255 255 255 #特殊地图色
    SetFontSize 50 #特殊地图色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\特殊地图.mp3"
    MinimapIcon 0 Orange Hexagon
    PlayEffect Orange

Show # 地图类 - 附魔地图 - T3迷雾地图（亢奋，遭遇战等）
    Class "Waystones"
    AnyEnchantment True
    BaseType "Waystone (Tier 3)"
    SetTextColor 133 41 77 255 #特殊地图色
    SetBackgroundColor 255 255 255 255 #特殊地图色
    SetFontSize 50 #特殊地图色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\特殊地图.mp3"
    MinimapIcon 0 Orange Hexagon
    PlayEffect Orange

Show # 地图类 - 附魔地图 - T2迷雾地图（亢奋，遭遇战等）
    Class "Waystones"
    AnyEnchantment True
    BaseType "Waystone (Tier 2)"
    SetTextColor 133 41 77 255 #特殊地图色
    SetBackgroundColor 255 255 255 255 #特殊地图色
    SetFontSize 50 #特殊地图色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\特殊地图.mp3"
    MinimapIcon 0 Orange Hexagon
    PlayEffect Orange

Show # 地图类 - 附魔地图 - T1迷雾地图（亢奋，遭遇战等）
    Class "Waystones"
    AnyEnchantment True
    BaseType "Waystone (Tier 1)"
    SetTextColor 133 41 77 255 #特殊地图色
    SetBackgroundColor 255 255 255 255 #特殊地图色
    SetFontSize 50 #特殊地图色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\特殊地图.mp3"
    MinimapIcon 0 Orange Hexagon
    PlayEffect Orange


# 4.6 地图类 - 传奇地图

Show # 地图类 - 传奇地图 - 传奇地图
    Class "Waystones"
    Rarity = Unique
    SetTextColor 180 96 0 #传奇色
    SetBorderColor 255 0 0 #传奇色
    SetFontSize 43 #传奇色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\传奇地图.mp3"
    MinimapIcon 0 Brown Hexagon
    PlayEffect Brown


# 4.7 地图类 - 普通异界地图


Show # 地图类 - 普通异界地图 - T16 （腐化黄色T16）
    Class "Waystones"
    BaseType "Waystone (Tier 16)"    
    Corrupted True
    Rarity = Rare
    SetTextColor 255 128 192 #T16地图色
    SetBackgroundColor 255 255 255 #T16地图色
    SetBorderColor 255 128 192 #T16地图色
    SetFontSize 50 #T16地图色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\高级地图.mp3"
    MinimapIcon 0 Orange Hexagon
    PlayEffect Orange

Show # 地图类 - 普通异界地图 - T16 （腐化白蓝T16）
    Class "Waystones"
    BaseType "Waystone (Tier 16)"
    Corrupted True
    Rarity <= Magic
    SetBorderColor 255 128 192 #T16地图色
    SetFontSize 50 #T16地图色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\高级地图.mp3"
    MinimapIcon 0 Orange Hexagon
    PlayEffect Orange

Show # 地图类 - 普通异界地图 - T16地图 红字
    Class "Waystones"
    BaseType "Waystone (Tier 16)"
    SetTextColor 255 128 192 #T16地图色
    SetBackgroundColor 255 255 255 #T16地图色
    SetBorderColor 255 128 192 #T16地图色
    SetFontSize 50 #T16地图色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\高级地图.mp3"
    MinimapIcon 0 Orange Hexagon
    PlayEffect Orange

Show # 地图类 - 普通异界地图 - T15 （腐化黄色T15图）
    Class "Waystones"
    BaseType "Waystone (Tier 15)"
    Corrupted True
    Rarity = Rare
    SetTextColor 0 0 0 #T15地图色
    SetBackgroundColor 255 255 255 #T15地图色
    SetBorderColor 128 128 128 #T15地图色
    SetFontSize 50 #T15地图色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\高级地图.mp3"
    MinimapIcon 0 Pink Hexagon
    PlayEffect Pink

Show # 地图类 - 普通异界地图 - T15 （腐化白蓝T15图）
    Class "Waystones"
    BaseType "Waystone (Tier 15)"
    Corrupted True
    Rarity <= Magic
    SetBorderColor 128 128 128 #T15地图色
    SetFontSize 50 #T15地图色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\高级地图.mp3"
    MinimapIcon 0 Pink Hexagon
    PlayEffect Pink

Show # 地图类 - 普通异界地图 - T15地图 白底
    Class "Waystones"
    BaseType "Waystone (Tier 15)"
    SetTextColor 0 0 0 #T15地图色
    SetBackgroundColor 255 255 255 #T15地图色
    SetBorderColor 128 128 128 #T15地图色
    SetFontSize 50 #T15地图色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\高级地图.mp3"
    MinimapIcon 0 Pink Hexagon
    PlayEffect Pink

Hide # 地图类 - 普通异界地图 - T14（腐化黄色T14图）
    BaseType "Waystone (Tier 14)"
    Class "Waystones"
    Corrupted True
    Rarity = Rare
    SetTextColor 0 255 3 220 #T14地图色
    SetBorderColor 74 230 58 #T14地图色
    SetFontSize 45 #T14地图色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\高级地图.mp3"
    MinimapIcon 1 Purple Hexagon
    PlayEffect Purple

Hide # 地图类 - 普通异界地图 - T14（腐化白蓝T14图）
    BaseType "Waystone (Tier 14)"
    Class "Waystones"
    Corrupted True
    Rarity <= Magic
    SetBorderColor 74 230 58 #T14地图色
    SetFontSize 45 #T14地图色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\高级地图.mp3"
    MinimapIcon 1 Purple Hexagon
    PlayEffect Purple
    
Hide # 地图类 - 普通异界地图 - T14 绿色
    BaseType "Waystone (Tier 14)"
    Class "Waystones"
    SetTextColor 0 255 3 220 #T14地图色
    SetBorderColor 74 230 58 #T14地图色
    SetFontSize 45 #T14地图色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\高级地图.mp3"
    MinimapIcon 1 Purple Hexagon
    PlayEffect Purple

Hide # 地图类 - 普通异界地图 - T11到T13 红图（黄色腐化图）
    BaseType "Waystone (Tier 11)" "Waystone (Tier 12)" "Waystone (Tier 13)"
    Class "Waystones"
    Corrupted True
    Rarity = Rare
    SetTextColor 255 128 128 #红图色
    SetBorderColor 255 128 128 #红图色
    SetFontSize 45 #红图色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\中级地图.mp3"
    MinimapIcon 1 Yellow Hexagon
    PlayEffect Yellow

Hide # 地图类 - 普通异界地图 - T11到T13 红图（白蓝腐化图）
    BaseType "Waystone (Tier 11)" "Waystone (Tier 12)" "Waystone (Tier 13)"
    Class "Waystones"
    Corrupted True
    Rarity <= Magic
    SetBorderColor 255 128 128 #红图色
    SetFontSize 45 #红图色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\中级地图.mp3"
    MinimapIcon 1 Yellow Hexagon
    PlayEffect Yellow

Hide # 地图类 - 普通异界地图 - T13 红图
    BaseType "Waystone (Tier 13)"
    Class "Waystones"
    SetTextColor 255 128 128 #红图色
    SetBorderColor 255 128 128 #红图色
    SetFontSize 45 #红图色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\中级地图.mp3"
    MinimapIcon 1 Yellow Hexagon
    PlayEffect Yellow

Show # 地图类 - 普通异界地图 - T12 红图
    BaseType "Waystone (Tier 12)"
    Class "Waystones"
    SetTextColor 255 128 128 #红图色
    SetBorderColor 255 128 128 #红图色
    SetFontSize 45 #红图色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\中级地图.mp3"
    MinimapIcon 1 Yellow Hexagon
    PlayEffect Yellow

Show # 地图类 - 普通异界地图 - T11 红图
    BaseType "Waystone (Tier 11)"
    Class "Waystones"
    SetTextColor 255 128 128 #红图色
    SetBorderColor 255 128 128 #红图色
    SetFontSize 45 #红图色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\中级地图.mp3"
    MinimapIcon 1 Yellow Hexagon
    PlayEffect Yellow

Show # 地图类 - 普通异界地图 - T6到T10 （白蓝腐化图）
    BaseType "Waystone (Tier 6)" "Waystone (Tier 7)" "Waystone (Tier 8)" "Waystone (Tier 9)" "Waystone (Tier 10)"
    Class "Waystones"
    Corrupted True
    Rarity <= Magic
    SetBorderColor 231 180 120 #黄图色
    SetFontSize 44 #黄图色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\地图.mp3"
    MinimapIcon 1 Yellow Hexagon
    PlayEffect Yellow

Show # 地图类 - 普通异界地图 - T10 
    BaseType "Waystone (Tier 10)"
    Class "Waystones"
    SetTextColor 231 180 120 #黄图色
    SetBorderColor 231 180 120 #黄图色
    SetFontSize 44 #黄图色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\地图.mp3"
    MinimapIcon 1 Yellow Hexagon
    PlayEffect Yellow

Show # 地图类 - 普通异界地图 - T9
    BaseType "Waystone (Tier 9)"
    Class "Waystones"
    SetTextColor 231 180 120 #黄图色
    SetBorderColor 231 180 120 #黄图色
    SetFontSize 44 #黄图色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\地图.mp3"
    MinimapIcon 1 Yellow Hexagon
    PlayEffect Yellow

Show # 地图类 - 普通异界地图 - T8
    BaseType "Waystone (Tier 8)"
    Class "Waystones"
    SetTextColor 231 180 120 #黄图色
    SetBorderColor 231 180 120 #黄图色
    SetFontSize 44 #黄图色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\地图.mp3"
    MinimapIcon 1 Yellow Hexagon
    PlayEffect Yellow

Show # 地图类 - 普通异界地图 - T7
    BaseType "Waystone (Tier 7)"
    Class "Waystones"
    SetTextColor 231 180 120 #黄图色
    SetBorderColor 231 180 120 #黄图色
    SetFontSize 44 #黄图色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\地图.mp3"
    MinimapIcon 1 Yellow Hexagon
    PlayEffect Yellow

Show # 地图类 - 普通异界地图 - T6
    BaseType "Waystone (Tier 6)"
    Class "Waystones"
    SetTextColor 231 180 120 #黄图色
    SetBorderColor 231 180 120 #黄图色
    SetFontSize 44 #黄图色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\地图.mp3"
    MinimapIcon 1 Yellow Hexagon
    PlayEffect Yellow

Show # 地图类 - 普通异界地图 - T1到T5 （腐化白蓝图）
    BaseType "Waystone (Tier 1)" "Waystone (Tier 2)" "Waystone (Tier 3)" "Waystone (Tier 4)" "Waystone (Tier 5)"
    Class "Waystones"
    Corrupted True
    Rarity <= Magic
    SetBorderColor 0 255 255 #白图色
    SetFontSize 42 #白图色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\低级地图.mp3"
    MinimapIcon 1 Cyan Hexagon
    PlayEffect Cyan

Show # 地图类 - 普通异界地图 - T5
    BaseType "Waystone (Tier 5)"
    Class "Waystones"
    SetBorderColor 0 255 255 #白图色
    SetFontSize 42 #白图色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\低级地图.mp3"
    MinimapIcon 1 Cyan Hexagon
    PlayEffect Cyan

Show # 地图类 - 普通异界地图 - T4
    BaseType "Waystone (Tier 4)"
    Class "Waystones"
    SetBorderColor 0 255 255 #白图色
    SetFontSize 42 #白图色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\低级地图.mp3"
    MinimapIcon 1 Cyan Hexagon
    PlayEffect Cyan

Show # 地图类 - 普通异界地图 - T3
    BaseType "Waystone (Tier 3)"
    Class "Waystones"
    SetBorderColor 0 255 255 #白图色
    SetFontSize 42 #白图色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\低级地图.mp3"
    MinimapIcon 1 Cyan Hexagon
    PlayEffect Cyan

Show # 地图类 - 普通异界地图 - T2
    BaseType "Waystone (Tier 2)"
    Class "Waystones"
    SetBorderColor 0 255 255 #白图色
    SetFontSize 42 #白图色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\低级地图.mp3"
    MinimapIcon 1 Cyan Hexagon
    PlayEffect Cyan

Show # 地图类 - 普通异界地图 - T1
    BaseType "Waystone (Tier 1)"
    Class "Waystones"
    SetBorderColor 0 255 255 #白图色
    SetFontSize 42 #白图色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\低级地图.mp3"
    MinimapIcon 1 Cyan Hexagon
    PlayEffect Cyan



# 4.8 地图类 - 圣甲虫

Show # 地图类 - 圣甲虫 - 全部圣甲虫(占位用，忽改)
    Class "Map Fragments"
    BaseType "Scarab"
    SetTextColor 89 172 255 #圣甲虫色
    SetBorderColor 0 220 220 #圣甲虫色
    SetFontSize 41 #圣甲虫色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\圣甲虫.mp3"
    MinimapIcon 1 Yellow Star
    PlayEffect Yellow



# 5 命运卡类

Show # 命运卡类 - 新赛季命运卡(占位用，忽改)
    Class "Divination Card"
    SetTextColor 255 128 255 #T3命运卡色
    SetBorderColor 255 128 255 #T3命运卡色
    SetFontSize 42 #T3命运卡色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\命运卡.mp3"
    MinimapIcon 1 Cyan Square
    PlayEffect Cyan

Show # 命运卡类 - 堆叠大于2的全部卡（可屏蔽）
    Class "Divination Card"
    StackSize >= 2
    SetFontSize 38
    DisableDropSound True

Show # 命运卡类 - 其他命运卡（屏蔽命运卡前先屏蔽这里）
    Class "Divination Card"
    SetFontSize 38
    DisableDropSound True




# 6 技能类

# 6.1 技能类 - 技能宝石

Hide # 技能类 - 技能宝石 - 20级技能宝石
    BaseType "Uncut Skill Gem"
    ItemLevel >= 20
    SetTextColor 0 255 255 #20级技能宝石
    SetBackgroundColor 70 28 66 #20级技能宝石
    SetBorderColor 241 148 180 #20级技能宝石
    SetFontSize 50 #20级技能宝石
    DisableDropSound True
    CustomAlertSound "文子过滤音效\技能宝石.mp3"
    MinimapIcon 0 Red Diamond
    PlayEffect Red

Hide # 技能类 - 技能宝石 - 19级技能宝石
    BaseType "Uncut Skill Gem"
    ItemLevel >= 19
    SetTextColor 0 255 255 #19级技能宝石
    SetBackgroundColor 70 28 66 #19级技能宝石
    SetBorderColor 241 148 180 #19级技能宝石
    SetFontSize 43 #19级技能宝石
    DisableDropSound True
    CustomAlertSound "文子过滤音效\技能宝石.mp3"
    MinimapIcon 0 Cyan Diamond
    PlayEffect Cyan

Hide # 技能类 - 技能宝石 - 全部技能宝石
    BaseType "Uncut Skill Gem"
    SetTextColor 162 186 2 #低阶碎片色
    SetBackgroundColor 0 0 0 #低阶碎片色
    SetBorderColor 48 89 10 #低阶碎片色
    SetFontSize 41 #低阶碎片色
    DisableDropSound True


# 6.2 技能类 - 辅助宝石

Hide # 技能类 - 辅助宝石 - 3级辅助宝石
    BaseType "Uncut Support Gem"
    ItemLevel >= 3
    SetTextColor 162 186 2 #低阶碎片色
    SetBackgroundColor 0 0 0 #低阶碎片色
    SetBorderColor 138 89 238 220 #低阶碎片色
    SetFontSize 43 #低阶碎片色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\技能宝石.mp3"
    MinimapIcon 1 Yellow Diamond
    PlayEffect Yellow

Hide # 技能类 - 辅助宝石 - 全部辅助宝石
    BaseType "Uncut Support Gem"
    SetTextColor 162 186 2 #低阶碎片色
    SetBackgroundColor 0 0 0 #低阶碎片色
    SetBorderColor 48 89 10 #低阶碎片色
    SetFontSize 41 #低阶碎片色
    DisableDropSound True


# 6.3 技能类 - 精魄宝石

Hide # 技能类 - 精魄宝石 - 20级精魄技能石
    BaseType "Uncut Spirit Gem"
    ItemLevel >= 20
    SetTextColor 255 177 152 255 #中阶碎片色
    SetBackgroundColor 146 67 31 190 #中阶碎片色
    SetBorderColor 255 0 0 #中阶碎片色
    SetFontSize 50 #中阶碎片色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\技能宝石.mp3"
    MinimapIcon 0 Red Diamond
    PlayEffect Red

Hide # 技能类 - 精魄宝石 - 19级精魄技能石
    BaseType "Uncut Spirit Gem"
    ItemLevel >= 19
    SetTextColor 255 177 152 255 #中阶碎片色
    SetBackgroundColor 146 67 31 190 #中阶碎片色
    SetBorderColor 255 0 0 #中阶碎片色
    SetFontSize 43 #中阶碎片色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\技能宝石.mp3"
    MinimapIcon 0 Pink Diamond
    PlayEffect Pink

Hide # 技能类 - 精魄宝石 - 8级精魄技能石
    BaseType "Uncut Spirit Gem"
    ItemLevel = 8
    SetTextColor 255 177 152 255 #中阶碎片色
    SetBackgroundColor 146 67 31 190 #中阶碎片色
    SetBorderColor 255 0 0 #中阶碎片色
    SetFontSize 45 #中阶碎片色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\技能宝石.mp3"
    MinimapIcon 0 Pink Diamond
    PlayEffect Pink

Hide # 技能类 - 精魄宝石 - 全部魄技能石
    BaseType "Uncut Spirit Gem"
    SetTextColor 255 177 152 255 #中阶碎片色
    SetBackgroundColor 146 67 31 190 #中阶碎片色
    SetBorderColor 240 240 240 110 #中阶碎片色
    SetFontSize 43 #中阶碎片色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\技能宝石.mp3"
    MinimapIcon 0 Pink Diamond
    PlayEffect Pink


# 6.4 技能类 - 带孔技能

Show # 技能类 - 带孔技能 - 6连5孔技能
    Class "Gems"
    Sockets 5
    SetTextColor 128 0 255 #特殊宝石色
    SetBackgroundColor 128 255 255 #特殊宝石色
    SetBorderColor 255 0 255 #特殊宝石色
    SetFontSize 47 #特殊宝石色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\特殊技能石.mp3"
    MinimapIcon 0 Red Moon
    PlayEffect Red

Show # 技能类 - 带孔技能 - 5L4洞技能
    Class "Gems"
    Sockets 4
    SetTextColor 210 0 0 #21级23品色
    SetBackgroundColor 14 247 235 #21级23品色
    SetBorderColor 210 0 0 #21级23品色
    SetFontSize 50 #21级23品色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\特殊技能石.mp3"
    MinimapIcon 0 Red Moon
    PlayEffect Red

Show # 技能类 - 带孔技能 - 4L3洞技能
    Class "Gems"
    Sockets 3
    SetTextColor 0 255 255 #高等技能色
    SetBackgroundColor 0 100 150 200 #高等技能色
    SetFontSize 44 #高等技能色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\特殊技能石.mp3"
    MinimapIcon 1 Cyan Moon
    PlayEffect Cyan



# 6.5 技能类 - 高级高品技能

Show # 技能类 - 高级高品技能 - 技能21级，23品的
    GemLevel >= 21
    Quality >= 23
    Class "Gems"
    SetTextColor 210 0 0 #21级23品色
    SetBackgroundColor 14 247 235 #21级23品色
    SetBorderColor 210 0 0 #21级23品色
    SetFontSize 50 #21级23品色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\技能宝石.mp3"
    MinimapIcon 0 Red Moon
    PlayEffect Red

Show # 技能类 - 高级高品技能 - 技能21级
    GemLevel >= 21
    Class "Gems"
    SetTextColor 210 0 0 #21级技能色
    SetBackgroundColor 0 100 150 200 #21级技能色
    SetBorderColor 0 0 0 #21级技能色
    SetFontSize 50 #21级技能色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\技能宝石.mp3"
    MinimapIcon 1 Orange Moon
    PlayEffect Orange

Show # 技能类 - 高级高品技能 - 技能20级，23品的
    GemLevel >= 20
    Quality >= 23
    Class "Gems"
    SetTextColor 0 128 255 #20级23品色
    SetBorderColor 210 0 0 #20级23品色
    SetBackgroundColor 14 247 235 #20级23品色
    SetFontSize 50 #20级23品色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\技能宝石.mp3"
    MinimapIcon 1 Orange Moon
    PlayEffect Orange

Show # 技能类 - 高级高品技能 - 技能23品的
    Quality >= 23
    Class "Gems"
    SetTextColor 0 0 0 #23品技能色
    SetBorderColor 210 0 0 #23品技能色
    SetBackgroundColor 14 247 235 #23品技能色
    SetFontSize 50 #23品技能色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\技能宝石.mp3"
    MinimapIcon 1 Orange Moon
    PlayEffect Orange

Show # 技能类 - 高级高品技能 - 技能20级，20品的
    GemLevel >= 20
    Quality >= 20
    Class "Gems"
    SetTextColor 0 128 255 #20级20品色
    SetBackgroundColor 128 255 255 #20级20品色
    SetFontSize 50 #20级20品色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\技能宝石.mp3"
    MinimapIcon 1 Orange Moon
    PlayEffect Orange

Show # 技能类 - 高级高品技能 - 技能20品的
    Quality >= 20
    Class "Gems"
    SetTextColor 0 0 0 #20品色
    SetBorderColor 0 0 255 #20品色
    SetBackgroundColor 14 247 235 #20品色
    SetFontSize 50 #20品色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\技能宝石.mp3"
    MinimapIcon 1 Yellow Moon
    PlayEffect Yellow

Hide # 技能类 - 高级高品技能 - 大于19级的技能
    GemLevel >= 19
    Class "Gems"
    SetTextColor 0 255 255 #高等技能色
    SetBackgroundColor 0 100 150 200 #高等技能色
    SetFontSize 44 #高等技能色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\技能宝石.mp3"
    MinimapIcon 1 Cyan Moon
    PlayEffect Cyan
    
Hide # 技能类 - 高级高品技能 - 大于15品的技能
    Quality >= 15
    Class "Gems"
    SetTextColor 0 0 0 #高品技能色
    SetBackgroundColor 14 247 235 #高品技能色
    SetFontSize 44 #高品技能色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\技能宝石.mp3"
    MinimapIcon 1 Orange Moon
    PlayEffect Orange
    
Hide # 技能类 - 高级高品技能 - 大于5品的技能
    Quality >= 5
    Class "Gems"
    SetTextColor 0 0 0 #低品技能色
    SetBackgroundColor 0 100 150 200 #低品技能色
    SetFontSize 43 #低品技能色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\技能宝石.mp3"
    MinimapIcon 2 Green Moon
    PlayEffect Green

Hide # 技能类 - 高级高品技能 - 大于15级的技能
    GemLevel >= 15
    Class "Gems"
    SetTextColor 0 0 0 #低等技能色
    SetBackgroundColor 54 107 107 #低等技能色
    SetFontSize 43 #低等技能色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\技能宝石.mp3"
    MinimapIcon 2 White Moon
    PlayEffect White

Hide # 技能类 - 高级高品技能 - 低于5品的技能
    Quality < 5
    Quality >= 1
    Class "Gems"
    SetTextColor 0 0 0 #低品技能色
    SetBackgroundColor 0 100 150 200 #低品技能色
    SetFontSize 43 #低品技能色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\技能宝石.mp3"
    MinimapIcon 2 Blue Moon
    PlayEffect Blue

Hide # 技能类 - 高级高品技能 - 其他技能（屏蔽技能前先屏蔽这里）
    Class "Gems"
    SetBackgroundColor 0 0 0 0 #技能色
    SetFontSize 38
    DisableDropSound True





# 7 暗金类


# 7.1 暗金类 - 高价值暗金

Show # 暗金类 - 高价值暗金 - 卡德兰戒指（不可用盒子修改）
    Class "Rings"
    BaseType == "Ring"
    Rarity = Unique
    SetTextColor 255 255 255 #超级价值色
    SetBackgroundColor 255 40 0 220 #超级价值色
    SetBorderColor 175 96 37 #超级价值色
    SetFontSize 60 #超级价值色
    DisableDropSound True   
    CustomAlertSound "文子过滤音效\恭喜发财.mp3"
    MinimapIcon 0 Orange Pentagon
    PlayEffect Orange

Show # 暗金类 - 高价值暗金 - 暗金鱼竿
    Class "Fishing Rods"
    Rarity = Unique
    SetTextColor 255 255 255 #超级价值色
    SetBackgroundColor 255 40 0 220 #超级价值色
    SetBorderColor 175 96 37 #超级价值色
    SetFontSize 60 #超级价值
    DisableDropSound True
    CustomAlertSound "文子过滤音效\财源滚滚.mp3"
    MinimapIcon 0 Orange UpsideDownHouse
    PlayEffect Orange

Show # 暗金类 - 高价值暗金 - 暗金 6洞
    Sockets "6"
    Rarity = Unique
    SetTextColor 255 255 255 #6L暗金色
    SetBackgroundColor 223 112 0 #6L暗金色
    SetBorderColor 255 0 0 #6L暗金色
    SetFontSize 50 #6L暗金色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\哇6连装备.mp3"
    MinimapIcon 0 Orange UpsideDownHouse
    PlayEffect Orange

Show # 暗金类 - 高价值暗金 - 暗金 5洞
    Sockets "5"
    Rarity = Unique
    SetTextColor 128 64 0 #特殊暗金色
    SetBackgroundColor 184 184 184 #特殊暗金色
    SetBorderColor 255 83 83 #特殊暗金色
    SetFontSize 45 #特殊暗金色
    CustomAlertSound "文子过滤音效\5连装备.mp3"
    DisableDropSound True
    MinimapIcon 1 Orange UpsideDownHouse
    PlayEffect Orange

Show # 暗金类 - 高价值暗金 - 高品暗金药剂
    Class "Flasks"
    Quality > 25
    Corrupted False
    Rarity = Unique
    SetTextColor 128 64 0 #特殊暗金色
    SetBackgroundColor 184 184 184 #特殊暗金色
    SetBorderColor 255 83 83 #特殊暗金色
    SetFontSize 45 #特殊暗金色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\传说.mp3"
    MinimapIcon 0 Orange UpsideDownHouse
    PlayEffect Orange

Show # 暗金类 - 高价值暗金 - 高品暗金
    Quality >= 30
    Rarity = Unique
    SetTextColor 128 64 0 #特殊暗金色
    SetBackgroundColor 184 184 184 #特殊暗金色
    SetBorderColor 255 83 83 #特殊暗金色
    SetFontSize 45 #特殊暗金色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\传说.mp3"
    MinimapIcon 0 Orange UpsideDownHouse
    PlayEffect Orange


# 7.2 暗金类 - 价值暗金

Show # 暗金类 - 价值暗金 - 触发杖
    BaseType == "Wyrm Quarterstaff"
    Rarity = Unique
    SetTextColor 175 96 37 #T2暗金色
    SetBackgroundColor 192 192 192 #T2暗金色
    SetBorderColor 175 96 37 #T2暗金色
    SetFontSize 47 #T2暗金色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\金色稀有.mp3"
    MinimapIcon 1 Brown UpsideDownHouse
    PlayEffect Brown

Show # 暗金类 - 价值暗金 - 召唤锤
    BaseType == "Spiked Club"
    Rarity = Unique
    SetTextColor 175 96 37 #T2暗金色
    SetBackgroundColor 192 192 192 #T2暗金色
    SetBorderColor 175 96 37 #T2暗金色
    SetFontSize 47 #T2暗金色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\金色稀有.mp3"
    MinimapIcon 1 Brown UpsideDownHouse
    PlayEffect Brown

Show # 暗金类 - 价值暗金 - 禁锢手
    BaseType == "Grand Manchettes"
    Rarity = Unique
    SetTextColor 175 96 37 #T2暗金色
    SetBackgroundColor 192 192 192 #T2暗金色
    SetBorderColor 175 96 37 #T2暗金色
    SetFontSize 47 #T2暗金色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\金色稀有.mp3"
    MinimapIcon 1 Brown UpsideDownHouse
    PlayEffect Brown

Show # 暗金类 - 价值暗金 - AB脚
    BaseType == "Grand Manchettes"
    Rarity = Unique
    SetTextColor 175 96 37 #T2暗金色
    SetBackgroundColor 192 192 192 #T2暗金色
    SetBorderColor 175 96 37 #T2暗金色
    SetFontSize 47 #T2暗金色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\金色稀有.mp3"
    MinimapIcon 1 Brown UpsideDownHouse
    PlayEffect Brown

Show # 暗金类 - 价值暗金 - 多辅助头
    BaseType == "Grand Visage"
    Rarity = Unique
    SetTextColor 175 96 37 #T2暗金色
    SetBackgroundColor 192 192 192 #T2暗金色
    SetBorderColor 175 96 37 #T2暗金色
    SetFontSize 47 #T2暗金色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\金色稀有.mp3"
    MinimapIcon 1 Brown UpsideDownHouse
    PlayEffect Brown

Show # 暗金类 - 价值暗金 - 4孔衣
    BaseType == "Grand Regalia"
    Rarity = Unique
    SetTextColor 175 96 37 #T2暗金色
    SetBackgroundColor 192 192 192 #T2暗金色
    SetBorderColor 175 96 37 #T2暗金色
    SetFontSize 47 #T2暗金色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\金色稀有.mp3"
    MinimapIcon 1 Brown UpsideDownHouse
    PlayEffect Brown

Show # 暗金类 - 价值暗金 - 白袍Or忠诚之肤
    BaseType == "Garment"
    Rarity = Unique
    SetTextColor 175 96 37 #T2暗金色
    SetBackgroundColor 192 192 192 #T2暗金色
    SetBorderColor 175 96 37 #T2暗金色
    SetFontSize 47 #T2暗金色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\金色稀有.mp3"
    MinimapIcon 1 Brown UpsideDownHouse
    PlayEffect Brown

Show # 暗金类 - 价值暗金 - 马盾Or烈焰之翼
    BaseType == "Omen Crest Shield"
    Rarity = Unique
    SetTextColor 175 96 37 #T2暗金色
    SetBackgroundColor 192 192 192 #T2暗金色
    SetBorderColor 175 96 37 #T2暗金色
    SetFontSize 47 #T2暗金色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\金色稀有.mp3"
    MinimapIcon 1 Brown UpsideDownHouse
    PlayEffect Brown

Show # 暗金类 - 价值暗金 - 精魄盾
    BaseType == "Sigil Crest Shield"
    Rarity = Unique
    SetTextColor 175 96 37 #T2暗金色
    SetBackgroundColor 192 192 192 #T2暗金色
    SetBorderColor 175 96 37 #T2暗金色
    SetFontSize 47 #T2暗金色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\金色稀有.mp3"
    MinimapIcon 1 Brown UpsideDownHouse
    PlayEffect Brown

Show # 暗金类 - 价值暗金 - 幸运格挡盾
    BaseType == "Crucible Tower Shield"
    Rarity = Unique
    SetTextColor 175 96 37 #T2暗金色
    SetBackgroundColor 192 192 192 #T2暗金色
    SetBorderColor 175 96 37 #T2暗金色
    SetFontSize 47 #T2暗金色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\金色稀有.mp3"
    MinimapIcon 1 Brown UpsideDownHouse
    PlayEffect Brown

Show # 暗金类 - 价值暗金 - 降伏盾
    BaseType == "Stone Tower Shield"
    Rarity = Unique
    SetTextColor 175 96 37 #T2暗金色
    SetBackgroundColor 192 192 192 #T2暗金色
    SetBorderColor 175 96 37 #T2暗金色
    SetFontSize 47 #T2暗金色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\金色稀有.mp3"
    MinimapIcon 1 Brown UpsideDownHouse
    PlayEffect Brown

Show # 暗金类 - 价值暗金 - 翻滚脚
    BaseType == "Threaded Shoes"
    Rarity = Unique
    SetTextColor 175 96 37 #T2暗金色
    SetBackgroundColor 192 192 192 #T2暗金色
    SetBorderColor 175 96 37 #T2暗金色
    SetFontSize 47 #T2暗金色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\金色稀有.mp3"
    MinimapIcon 1 Brown UpsideDownHouse
    PlayEffect Brown

Show # 暗金类 - 价值暗金 - 灵骸脚
    BaseType == "Lattice Sandals"
    Rarity = Unique
    SetTextColor 175 96 37 #T2暗金色
    SetBackgroundColor 192 192 192 #T2暗金色
    SetBorderColor 175 96 37 #T2暗金色
    SetFontSize 47 #T2暗金色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\金色稀有.mp3"
    MinimapIcon 1 Brown UpsideDownHouse
    PlayEffect Brown

Show # 暗金类 - 价值暗金 - 智行手
    BaseType == "Furtive Wraps"
    Rarity = Unique
    SetTextColor 175 96 37 #T1暗金色
    SetBackgroundColor 255 255 255 #T1暗金色
    SetBorderColor 255 0 0 #T1暗金色
    SetFontSize 50 #T1暗金色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\金色传说.mp3"
    MinimapIcon 0 Orange UpsideDownHouse
    PlayEffect Orange

Show # 暗金类 - 价值暗金 - 腐化头
    BaseType == "Tribal Mask"
    Rarity = Unique
    SetTextColor 175 96 37 #T2暗金色
    SetBackgroundColor 192 192 192 #T2暗金色
    SetBorderColor 175 96 37 #T2暗金色
    SetFontSize 47 #T2暗金色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\金色稀有.mp3"
    MinimapIcon 1 Brown UpsideDownHouse
    PlayEffect Brown

Show # 暗金类 - 价值暗金 - 禁域头
    BaseType == "Chain Tiara"
    Rarity = Unique
    SetTextColor 175 96 37 #T2暗金色
    SetBackgroundColor 192 192 192 #T2暗金色
    SetBorderColor 175 96 37 #T2暗金色
    SetFontSize 47 #T2暗金色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\金色稀有.mp3"
    MinimapIcon 1 Brown UpsideDownHouse
    PlayEffect Brown

Show # 暗金类 - 价值暗金 - 千血衣
    BaseType == "Conqueror Plate"
    Rarity = Unique
    SetTextColor 175 96 37 #T2暗金色
    SetBackgroundColor 192 192 192 #T2暗金色
    SetBorderColor 175 96 37 #T2暗金色
    SetFontSize 47 #T2暗金色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\金色稀有.mp3"
    MinimapIcon 1 Brown UpsideDownHouse
    PlayEffect Brown

Show # 暗金类 - 价值暗金 - 玫红暴击手
    BaseType == "Fine Bracers"
    Rarity = Unique
    SetTextColor 175 96 37 #T2暗金色
    SetBackgroundColor 192 192 192 #T2暗金色
    SetBorderColor 175 96 37 #T2暗金色
    SetFontSize 47 #T2暗金色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\金色稀有.mp3"
    MinimapIcon 1 Brown UpsideDownHouse
    PlayEffect Brown

Show # 暗金类 - 价值暗金 - 时空衣Or烈炎衣
    BaseType == "Silk Robe"
    Rarity = Unique
    SetTextColor 175 96 37 #T2暗金色
    SetBackgroundColor 192 192 192 #T2暗金色
    SetBorderColor 175 96 37 #T2暗金色
    SetFontSize 47 #T2暗金色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\金色稀有.mp3"
    MinimapIcon 1 Brown UpsideDownHouse
    PlayEffect Brown

Show # 暗金类 - 价值暗金 - 全+1链
    BaseType == "Crimson Amulet"
    Rarity = Unique
    SetTextColor 175 96 37 #T2暗金色
    SetBackgroundColor 192 192 192 #T2暗金色
    SetBorderColor 175 96 37 #T2暗金色
    SetFontSize 47 #T2暗金色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\金色稀有.mp3"
    MinimapIcon 1 Brown UpsideDownHouse
    PlayEffect Brown

Show # 暗金类 - 价值暗金 - 全属链or召唤暴击链
    BaseType == "Stellar Amulet"
    Rarity = Unique
    SetTextColor 175 96 37 #T2暗金色
    SetBackgroundColor 192 192 192 #T2暗金色
    SetBorderColor 175 96 37 #T2暗金色
    SetFontSize 47 #T2暗金色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\金色稀有.mp3"
    MinimapIcon 1 Brown UpsideDownHouse
    PlayEffect Brown

Show # 暗金类 - 价值暗金 - 寻宝链Or蛇卵链
    BaseType == "Gold Amulet"
    Rarity = Unique
    SetTextColor 175 96 37 #T2暗金色
    SetBackgroundColor 192 192 192 #T2暗金色
    SetBorderColor 175 96 37 #T2暗金色
    SetFontSize 47 #T2暗金色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\金色稀有.mp3"
    MinimapIcon 1 Brown UpsideDownHouse
    PlayEffect Brown

Show # 暗金类 - 价值暗金 - 明恩戒Or原初戒
    BaseType == "Amethyst Ring"
    Rarity = Unique
    SetTextColor 175 96 37 #T2暗金色
    SetBackgroundColor 192 192 192 #T2暗金色
    SetBorderColor 175 96 37 #T2暗金色
    SetFontSize 47 #T2暗金色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\金色稀有.mp3"
    MinimapIcon 1 Brown UpsideDownHouse
    PlayEffect Brown

Show # 暗金类 - 价值暗金 - 窃罪Or亡者
    BaseType == "Emerald Ring"
    Rarity = Unique
    SetTextColor 175 96 37 #T2暗金色
    SetBackgroundColor 192 192 192 #T2暗金色
    SetBorderColor 175 96 37 #T2暗金色
    SetFontSize 47 #T2暗金色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\金色稀有.mp3"
    MinimapIcon 1 Brown UpsideDownHouse
    PlayEffect Brown

Show # 暗金类 - 价值暗金 - 窃罪Or亡者
    BaseType == "Emerald Ring"
    Rarity = Unique
    SetTextColor 175 96 37 #T2暗金色
    SetBackgroundColor 192 192 192 #T2暗金色
    SetBorderColor 175 96 37 #T2暗金色
    SetFontSize 47 #T2暗金色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\金色稀有.mp3"
    MinimapIcon 1 Brown UpsideDownHouse
    PlayEffect Brown

Show # 暗金类 - 价值暗金 - 蛇穴戒Or劫魂环
    BaseType == "Pearl Ring"
    Rarity = Unique
    SetTextColor 175 96 37 #T2暗金色
    SetBackgroundColor 192 192 192 #T2暗金色
    SetBorderColor 175 96 37 #T2暗金色
    SetFontSize 47 #T2暗金色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\金色稀有.mp3"
    MinimapIcon 1 Brown UpsideDownHouse
    PlayEffect Brown

Show # 暗金类 - 价值暗金 - 赌神戒基底
    BaseType == "Gold Ring"
    Rarity = Unique
    SetTextColor 175 96 37 #T2暗金色
    SetBackgroundColor 192 192 192 #T2暗金色
    SetBorderColor 175 96 37 #T2暗金色
    SetFontSize 47 #T2暗金色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\金色稀有.mp3"
    MinimapIcon 1 Brown UpsideDownHouse
    PlayEffect Brown

Show # 暗金类 - 价值暗金 - 线圈腰
    BaseType == "Ornate Belt"
    Rarity = Unique
    SetTextColor 175 96 37 #T2暗金色
    SetBackgroundColor 192 192 192 #T2暗金色
    SetBorderColor 175 96 37 #T2暗金色
    SetFontSize 47 #T2暗金色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\金色稀有.mp3"
    MinimapIcon 1 Brown UpsideDownHouse
    PlayEffect Brown

Show # 暗金类 - 价值暗金 - 独创戒指腰
    BaseType == "Utility Belt"
    Rarity = Unique
    SetTextColor 175 96 37 #T1暗金色
    SetBackgroundColor 255 255 255 #T1暗金色
    SetBorderColor 255 0 0 #T1暗金色
    SetFontSize 50 #T1暗金色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\金色传说.mp3"
    MinimapIcon 0 Orange UpsideDownHouse
    PlayEffect Orange

Show # 暗金类 - 价值暗金 - 溶解药
    BaseType == "Ultimate Mana Flask"
    Rarity = Unique
    SetTextColor 175 96 37 #T1暗金色
    SetBackgroundColor 255 255 255 #T1暗金色
    SetBorderColor 255 0 0 #T1暗金色
    SetFontSize 50 #T1暗金色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\金色传说.mp3"
    MinimapIcon 0 Orange UpsideDownHouse
    PlayEffect Orange


# 7.3 暗金类 - 高价暗金饰品类

Show # 暗金类 - 高价暗金饰品类 - 重革腰带基底（猎首）
    BaseType "Heavy Belt"
    Rarity = Unique
    SetTextColor 175 96 37 #T1暗金色
    SetBackgroundColor 255 255 255 #T1暗金色
    SetBorderColor 255 0 0 #T1暗金色
    SetFontSize 50 #T1暗金色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\金色传说.mp3"
    MinimapIcon 0 Orange UpsideDownHouse
    PlayEffect Orange


# 7.4 暗金类 - 暗金珠宝类

Show # 暗金类 - 暗金珠宝类 - 暗金永恒珠宝
    Class "Jewel"
    BaseType "Timeless Jewel"
    Rarity = Unique
    SetTextColor 255 255 255 #超级价值色
    SetBackgroundColor 255 40 0 220 #超级价值色
    SetBorderColor 175 96 37 #超级价值色
    SetFontSize 60 #超级价值色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\永恒珠宝.mp3"
    MinimapIcon 0 Red Pentagon
    PlayEffect Red

Show # 暗金类 - 暗金珠宝类 - 暗金钻石珠宝
    Class "Jewel"
    BaseType "Diamond"
    Rarity = Unique
    SetTextColor 255 255 255 #超级价值色
    SetBackgroundColor 255 40 0 220 #超级价值色
    SetBorderColor 175 96 37 #超级价值色
    SetFontSize 60 #超级价值色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\财源滚滚.mp3"
    MinimapIcon 0 Red Pentagon
    PlayEffect Red

Show # 暗金类 - 暗金珠宝类 - 暗金力抗珠宝
    Class "Jewel"
    BaseType "Time-Lost Emerald"
    Rarity = Unique
    SetTextColor 255 255 255 #超级价值色
    SetBackgroundColor 255 40 0 220 #超级价值色
    SetBorderColor 175 96 37 #超级价值色
    SetFontSize 60 #超级价值色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\恭喜发财.mp3"
    MinimapIcon 0 Red Pentagon
    PlayEffect Red

Show # 暗金类 - 暗金珠宝类 - 其他暗金珠宝
    Class "Jewel"
    Rarity = Unique
    SetTextColor 175 96 37 #T2暗金色
    SetBackgroundColor 192 192 192 #T2暗金色
    SetBorderColor 175 96 37 #T2暗金色
    SetFontSize 47 #T2暗金色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\传奇珠宝.mp3"
    MinimapIcon 1 Pink Pentagon
    PlayEffect Pink

# 7.5 暗金类 - 暗金项链

Show # 暗金类 - 暗金项链 - 全部暗金项链
    Rarity = Unique
    Class "Amulets"
    SetTextColor 180 96 0 #暗金项链腰带类
    SetBorderColor 255 128 0 #暗金项链腰带类
    SetFontSize 44 #暗金项链腰带类
    DisableDropSound True   
    CustomAlertSound "文子过滤音效\传说.mp3"
    MinimapIcon 1 Yellow UpsideDownHouse
    PlayEffect Yellow

# 7.6 暗金类 - 暗金腰带

Show # 暗金类 - 暗金腰带 - 全部暗金腰带
    Rarity = Unique
    Class "Belts"
    SetTextColor 180 96 0 #暗金项链腰带类
    SetBorderColor 255 128 0 #暗金项链腰带类
    SetFontSize 44 #暗金项链腰带类
    DisableDropSound True
    CustomAlertSound "文子过滤音效\传说.mp3"
    MinimapIcon 1 Yellow UpsideDownHouse
    PlayEffect Yellow

# 7.7 暗金类 - 暗金戒指

Show # 暗金类 - 暗金戒指 - 全部暗金戒指
    Class "Rings"
    Rarity = Unique
    SetTextColor 180 96 0 #暗金戒指类
    SetBorderColor 255 255 0 #暗金戒指类
    SetFontSize 44 #暗金戒指类
    DisableDropSound True
    CustomAlertSound "文子过滤音效\传奇戒指.mp3"
    MinimapIcon 1 Pink Kite
    PlayEffect Pink

# 7.8 暗金类 - 暗金戒指

Show # 暗金类 - 暗金戒指 - 全部暗金药剂
    Class "Flasks"
    Rarity = Unique
    SetTextColor 180 96 0 #暗金药剂类
    SetBorderColor 0 128 255 #暗金药剂类
    SetFontSize 44 #暗金药剂类
    DisableDropSound True
    CustomAlertSound "文子过滤音效\传说.mp3"
    MinimapIcon 1 Pink Raindrop
    PlayEffect Pink


# 7.9 暗金类 - 暗金价值分类

Show # 暗金类 - 暗金价值分类 - 高价值暗金基底
    BaseType "Stellar Amulet" "Sapphire Ring" "Silk Robe" "Utility Belt" "Heavy Belt" "Smuggler Coat" "Furtive Wraps" "Lazuli Ring" "Shrine Sceptre" "Grand Regalia"
    Rarity = Unique
    SetTextColor 128 64 0 #特殊暗金色
    SetBackgroundColor 184 184 184 #特殊暗金色
    SetBorderColor 255 83 83 #特殊暗金色
    SetFontSize 45 #特殊暗金色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\传说.mp3"
    MinimapIcon 0 Orange UpsideDownHouse
    PlayEffect Orange

Show # 暗金类 - 暗金价值分类 - 有价值暗金基底
    BaseType "Trimmed Greaves" "Stone Tower Shield" "Azure Amulet" "Gold Ring" "Armoured Cap" "Emerald Ring" "Spiked Club" "Crude Bow" "Fine Bracers" "Gold Amulet" "Dualstring Bow" "Conqueror Plate" "Tribal Mask" "Ornate Belt" "Solar Amulet" "Wrapped Greathelm" "Torment Club" "Long Quarterstaff" "Omen Crest Shield" "Sigil Crest Shield" "Linen Wraps" "Tattered Robe" "Pilgrim Vestments" "Grand Visage" "Jade Amulet" "Amethyst Ring" "Crescent Quarterstaff" "Crucible Tower Shield" 
    Rarity = Unique
    SetTextColor 180 96 0  #价值暗金色
    SetBorderColor 255 0 0  #价值暗金色
    SetFontSize 43 #价值暗金色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\传说.mp3"
    MinimapIcon 0 Orange UpsideDownHouse
    PlayEffect Orange



# 8 特殊装备类

# 8.1 特殊装备类 - 30品质装备

Show # 特殊装备类 - 30品质装备
    ItemLevel >= 1
    Quality >= 30
    Rarity <= Rare
    Corrupted False
    SetTextColor 255 0 0 #崇高价值色
    SetBackgroundColor 3 125 168 #价值底子色
    SetBorderColor 255 255 255 0 #价值底子色
    SetFontSize 45 #价值底子色
    DisableDropSound True
    MinimapIcon 0 Red UpsideDownHouse
    PlayEffect Red


# 8.2 特殊装备类 - 破溃装备

Show # 特殊装备类 - 破溃装备 - 破碎珠宝
    FracturedItem True
    Class "Jewel"
    Rarity <= Rare
    SetTextColor 0 0 0 #破碎珠宝色
    SetBackgroundColor 255 165 0 #破碎珠宝色
    SetBorderColor 128 255 255 #破碎珠宝色
    SetFontSize 50 #破碎珠宝色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\加油.mp3"
    MinimapIcon 0 Pink Pentagon
    PlayEffect Pink

Show # 特殊装备类 - 破溃装备 - 破碎 弓
    FracturedItem True
    Class "Bows"
    Rarity <= Rare
    SetTextColor 0 0 0 #破碎武器色
    SetBackgroundColor 255 255 119 #破碎武器色 
    SetBorderColor 128 255 255 #破碎武器色
    SetFontSize 50 #破碎武器色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\加油.mp3"
    MinimapIcon 0 Purple UpsideDownHouse
    PlayEffect Purple

Show # 特殊装备类 - 破溃装备 - 破碎 双手杖
    FracturedItem True
    Class "Staves"
    Rarity <= Rare
    SetTextColor 0 0 0 #破碎武器色
    SetBackgroundColor 255 255 119 #破碎武器色 
    SetBorderColor 128 255 255 #破碎武器色
    SetFontSize 50 #破碎武器色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\加油.mp3"
    MinimapIcon 0 Purple UpsideDownHouse
    PlayEffect Purple

Show # 特殊装备类 - 破溃装备 - 破碎 法杖 符文匕首
    FracturedItem True
    Class "Wands"
    Rarity <= Rare
    SetTextColor 0 0 0 #破碎武器色
    SetBackgroundColor 255 255 119 #破碎武器色 
    SetBorderColor 128 255 255 #破碎武器色
    SetFontSize 50 #破碎武器色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\加油.mp3"
    MinimapIcon 0 Purple UpsideDownHouse
    PlayEffect Purple

Show # 特殊装备类 - 破溃装备 - 破碎 细剑 爪
    FracturedItem True
    Class "Claws"
    Rarity <= Rare
    SetTextColor 0 0 0 #破碎武器色
    SetBackgroundColor 255 255 119 #破碎武器色 
    SetBorderColor 128 255 255 #破碎武器色
    SetFontSize 50 #破碎武器色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\加油.mp3"
    MinimapIcon 0 Purple UpsideDownHouse
    PlayEffect Purple

Show # 特殊装备类 - 破溃装备 - 破碎 短杖
    FracturedItem True
    Class "Sceptres"
    Rarity <= Rare
    SetTextColor 0 0 0 #破碎武器色
    SetBackgroundColor 255 255 119 #破碎武器色 
    SetBorderColor 128 255 255 #破碎武器色
    SetFontSize 50 #破碎武器色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\加油.mp3"
    MinimapIcon 0 Purple UpsideDownHouse
    PlayEffect Purple

Show # 特殊装备类 - 破溃装备 - 破碎 箭袋
    FracturedItem True
    Class "Quivers"
    Rarity <= Rare
    SetTextColor 0 0 0 #破碎武器色
    SetBackgroundColor 255 255 119 #破碎武器色 
    SetBorderColor 128 255 255 #破碎武器色
    SetFontSize 50 #破碎武器色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\加油.mp3"
    MinimapIcon 0 Purple UpsideDownHouse
    PlayEffect Purple

Show # 特殊装备类 - 破溃装备 - 破碎 腰带
    FracturedItem True
    Rarity <= Rare
    Class "Belts"
    SetTextColor 0 0 0 #破碎饰品色
    SetBackgroundColor 255 200 0 200 #破碎饰品色
    SetBorderColor 128 255 255 #破碎饰品色
    SetFontSize 42 #破碎饰品色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\破碎装备.mp3"
    MinimapIcon 1 Yellow UpsideDownHouse
    PlayEffect Yellow

Show # 特殊装备类 - 破溃装备 - 破碎 项链
    FracturedItem True
    Rarity <= Rare
    Class "Amulets"
    SetTextColor 0 0 0 #破碎饰品色
    SetBackgroundColor 255 200 0 200 #破碎饰品色
    SetBorderColor 128 255 255 #破碎饰品色
    SetFontSize 42 #破碎饰品色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\破碎装备.mp3"
    MinimapIcon 1 Yellow UpsideDownHouse
    PlayEffect Yellow

Show # 特殊装备类 - 破溃装备 - 破碎 戒指
    FracturedItem True
    Rarity <= Rare
    Class "Rings"
    SetTextColor 0 0 0 #破碎饰品色
    SetBackgroundColor 255 200 0 200 #破碎饰品色
    SetBorderColor 128 255 255 #破碎饰品色
    SetFontSize 42 #破碎饰品色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\破碎装备.mp3"
    MinimapIcon 1 Yellow UpsideDownHouse
    PlayEffect Yellow

Show # 特殊装备类 - 破溃装备 - 破碎 盾
    FracturedItem True
    Rarity <= Rare
    Class "Shields"
    SetTextColor 255 255 255 #破碎装备色
    SetBackgroundColor 0 128 0 200 #破碎装备色
    SetBorderColor 128 255 255 #破碎装备色
    SetFontSize 40 #破碎装备色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\破碎装备.mp3"
    MinimapIcon 1 Cyan UpsideDownHouse
    PlayEffect Cyan

Show # 特殊装备类 - 破溃装备 - 破碎鞋子 86以上
    FracturedItem True
    Rarity <= Rare
    Class "Boots"
    ItemLevel >= 86
    SetTextColor 0 0 0 #破碎高阶底色
    SetBackgroundColor 255 255 119 #破碎高阶底色
    SetBorderColor 255 0 0 #破碎高阶底色
    SetFontSize 50 #破碎高阶底色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\666.mp3"
    MinimapIcon 1 Purple UpsideDownHouse
    PlayEffect Purple

Show # 特殊装备类 - 破溃装备 - 破碎物品 86以上
    FracturedItem True
    ItemLevel >= 86
    Rarity <= Rare
    SetTextColor 0 0 0 #破碎中阶底色
    SetBackgroundColor 255 255 119 #破碎中阶底色
    SetBorderColor 0 255 255 #破碎中阶底色
    SetFontSize 50 #破碎中阶底色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\破碎装备.mp3"
    MinimapIcon 1 Pink UpsideDownHouse
    PlayEffect Pink

Show # 特殊装备类 - 破溃装备 - 破碎 手 脚 头
    FracturedItem True
    Rarity <= Rare
    ItemLevel >= 80
    Class "Gloves" "Helmets" "Boots"
    SetTextColor 255 255 255 #破碎装备色
    SetBackgroundColor 0 128 0 200 #破碎装备色
    SetBorderColor 128 255 255 #破碎装备色
    SetFontSize 40 #破碎装备色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\破碎装备.mp3"
    MinimapIcon 1 Cyan UpsideDownHouse
    PlayEffect Cyan

Show # 特殊装备类 - 破溃装备 - 破碎物品 70+
    FracturedItem True
    Rarity <= Rare
    ItemLevel >= 70
    SetTextColor 255 215 0 #破碎物等色
    SetBackgroundColor 54 100 146 160 #破碎物等色
    SetBorderColor 128 255 255 #破碎物等色
    SetFontSize 42 #破碎物等色
    DisableDropSound True

Show # 特殊装备类 - 破溃装备 - 破碎物品 60+
    FracturedItem True
    Rarity <= Rare
    ItemLevel >= 60
    SetTextColor 255 215 0 #破碎普通色
    SetBackgroundColor 140 180 180 225 #破碎普通色
    SetBorderColor 128 255 255 #破碎普通色
    SetFontSize 40 #破碎普通色
    DisableDropSound True

Show # 特殊装备类 - 破溃装备 - 破碎物品 1+
    FracturedItem True
    Rarity <= Rare
    ItemLevel >= 1
    SetTextColor 255 215 0 #破碎低级色
    SetBackgroundColor 54 100 146 160 #破碎低级色
    SetBorderColor 128 255 255 #破碎低级色
    SetFontSize 38 #破碎低级色
    DisableDropSound True

Show # 特殊装备类 - 破溃装备 - 破碎物品
    FracturedItem True
    Rarity <= Rare
    SetTextColor 255 215 0 #破碎低级色
    SetBackgroundColor 54 100 146 160 #破碎低级色
    SetBorderColor 128 255 255 #破碎低级色
    SetFontSize 44 #破碎低级色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\破碎装备.mp3"
    MinimapIcon 1 Cyan UpsideDownHouse
    PlayEffect Cyan


# 8.3  特殊装备类 - 附魔装备

Show # 装备类 - 附魔装备类 - 附魔武器
    AnyEnchantment True
    Class "Claws" "One Hand" "Daggers" "Wands" "Sceptres" "Spears" "Flails" "Bows" "Staves" "Two Hand" "Quarterstaves" "Crossbows" "Traps" "Fishing Rods" "Quivers" "Shields" "Foci"
    Rarity = Rare
    ItemLevel >= 1
    SetTextColor 255 255 255 #价值附魔色
    SetBackgroundColor 128 0 255 #价值附魔色
    SetBorderColor 255 0 0 #价值附魔色
    SetFontSize 45 #价值附魔色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\附魔武器.mp3"
    MinimapIcon 0 Green UpsideDownHouse
    PlayEffect Green

Show # 装备类 - 附魔装备类 - 附魔护具
    AnyEnchantment True
    Class "Gloves" "Boots" "Helmets" "Body Armours"
    Rarity = Rare
    ItemLevel >= 1
    SetTextColor 255 255 255 #价值附魔色
    SetBackgroundColor 128 0 255 #价值附魔色
    SetBorderColor 255 0 0 #价值附魔色
    SetFontSize 45 #价值附魔色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\附魔胸甲.mp3"
    MinimapIcon 0 Green UpsideDownHouse
    PlayEffect Green

Show # 装备类 - 附魔装备类 - 附魔首饰
    AnyEnchantment True
    Rarity = Rare
    Class "Rings" "Amulets" "Belts"
    SetTextColor 38 0 86 #其他附魔色
    SetBackgroundColor 0 128 0 200 #其他附魔色
    SetFontSize 40 #其他附魔色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\附魔装备.mp3"
    PlayEffect Blue




# 9 珠宝类

Show # 珠宝类 - 永恒珠宝
    Class "Jewel"
    BaseType "Timeless Jewel"
    SetTextColor 255 255 255 #超级价值色
    SetBackgroundColor 255 40 0 220 #超级价值色
    SetBorderColor 175 96 37 #超级价值色
    SetFontSize 60 #超级价值色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\永恒珠宝.mp3"
    MinimapIcon 0 Red Pentagon
    PlayEffect Red

Show # 珠宝类 - 钻石珠宝
    Class "Jewel"
    BaseType "Diamond"
    SetTextColor 255 255 255 #超级价值色
    SetBackgroundColor 255 40 0 220 #超级价值色
    SetBorderColor 175 96 37 #超级价值色
    SetFontSize 60 #超级价值色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\666.mp3"
    MinimapIcon 0 Red Pentagon
    PlayEffect Red

Hide # 珠宝类 - 珠宝 黄色
    Class "Jewel"
    Rarity = Rare
    SetTextColor 0 0 0 #黄色珠宝色
    SetBackgroundColor 255 165 0 #黄色珠宝色
    SetBorderColor 255 165 0 #黄色珠宝色
    SetFontSize 50#黄色珠宝色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\集群珠宝.mp3"
    MinimapIcon 0 Yellow Pentagon
    PlayEffect Yellow

Hide # 珠宝类 - 珠宝 蓝色 80+
    Class "Jewel"
    Rarity <= Magic
    ItemLevel >= 80
    SetBackgroundColor 0 0 0 #蓝色珠宝色
    SetBorderColor 255 165 0 #蓝色珠宝色
    SetFontSize 50 #蓝色珠宝色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\集群珠宝.mp3"
    MinimapIcon 1 Cyan Pentagon
    PlayEffect Cyan

Hide # 珠宝类 - 珠宝 蓝色
    Class "Jewel"
    Rarity <= Magic
    ItemLevel >= 1
    SetBackgroundColor 0 0 0 #蓝色珠宝色
    SetBorderColor 255 165 0 #蓝色珠宝色
    SetFontSize 50 #蓝色珠宝色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\集群珠宝.mp3"
    MinimapIcon 1 Cyan Pentagon
    PlayEffect Cyan




# 9 药剂类

Hide # 药剂类 - 5品以上药剂(捡了卖商店换弹珠)
    Class "Flasks"
    ItemLevel >= 1
    Quality >= 5
    SetBackgroundColor 0 0 0 151 #品质药剂色
    SetBorderColor 255 0 0 150 #品质药剂色
    SetFontSize 43 #品质药剂色
    DisableDropSound True

Hide # 药剂类 - 20品以上终极生命药剂
    Class "Flasks"
    BaseType "Ultimate Life Flask"
    ItemLevel >= 1
    Quality >= 20
    SetTextColor 255 0 0 #水银药剂色
    SetBackgroundColor 136 136 255 #水银药剂色
    SetBorderColor 0 0 0 0 #水银药剂色
    SetFontSize 42 #水银药剂色
    DisableDropSound True

Hide # 药剂类 - 20品以上终极魔力药剂
    Class "Flasks"
    BaseType "Ultimate Mana Flask"
    ItemLevel >= 1
    Quality >= 20
    SetTextColor 255 0 0 #水银药剂色
    SetBackgroundColor 136 136 255 #水银药剂色
    SetBorderColor 0 0 0 0 #水银药剂色
    SetFontSize 42 #水银药剂色
    DisableDropSound True


Hide # 药剂类 - 80+终极魔力药剂
    Class "Flasks"
    ItemLevel >= 80
    BaseType "Ultimate Mana Flask"
    SetBackgroundColor 0 0 0 151 #生命药剂价值色
    SetBorderColor 136 136 255 200 #生命药剂价值色
    SetFontSize 41 #生命药剂价值色

Hide # 药剂类 - 终极魔力药剂55-72
    Class "Flasks"
    ItemLevel <= 72
    ItemLevel >= 55
    BaseType "Ultimate Mana Flask"
    SetBackgroundColor 0 0 0 151 #生命药剂价值色
    SetBorderColor 136 136 255 200 #生命药剂价值色
    SetFontSize 41 #生命药剂价值色

Hide # 药剂类 - 80+终极生命药剂
    Class "Flasks"
    ItemLevel >= 80
    BaseType "Ultimate Life Flask"
    SetBackgroundColor 0 0 0 151 #生命药剂价值色
    SetBorderColor 136 136 255 200 #生命药剂价值色
    SetFontSize 41 #生命药剂价值色
    DisableDropSound True

Hide # 药剂类 - 终极生命药剂55-72
    Class "Flasks"
    BaseType "Ultimate Life Flask"
    ItemLevel <= 72
    ItemLevel >= 55
    SetBackgroundColor 0 0 0 151 #生命药剂价值色
    SetBorderColor 136 136 255 200 #生命药剂价值色
    SetFontSize 41 #生命药剂价值色
    DisableDropSound True

Hide # 药剂类 - 其他药剂
    Class "Flasks"
    SetBorderColor 136 136 255 200 #普通药剂色
    SetFontSize 15 #普通药剂色
    DisableDropSound True


# 10 护符类

Hide # 护符类 - 82+物等护符
    Class "Charms"
    ItemLevel >= 82
    SetTextColor 255 0 0 #水银药剂色
    SetBackgroundColor 136 136 255 #水银药剂色
    SetBorderColor 0 0 0 0 #水银药剂色
    SetFontSize 42 #水银药剂色
    DisableDropSound True

Hide # 护符类 - 抗性护符55-72（红玉，蓝玉，黄玉，紫晶）
    Class "Charms"
    BaseType "Ruby Charm" "Sapphire Charm" "Topaz Charm" "Amethyst Charm"
    ItemLevel <= 72
    ItemLevel >= 55
    SetBackgroundColor 0 0 0 151 #普通功能药剂色
    SetBorderColor 136 0 0 150 #普通功能药剂色
    SetFontSize 42 #普通功能药剂色

Show # 护符类 - 黄金护符
    Class "Charms"
    BaseType "Golden Charm"
    SetBackgroundColor 0 0 0 151 #普通功能药剂色
    SetBorderColor 136 0 0 150 #普通功能药剂色
    SetFontSize 42 #普通功能药剂色
    DisableDropSound True
    PlayEffect Green

Show # 鉴黄装备 - 饰品类 - 自定义传奇装备显示(默认紫晶，黑曜)
    BaseType "黃金護符"
    Rarity = Rare
    SetTextColor 0 0 0 #首饰色
    SetBackgroundColor 255 200 0 200 #首饰色
    SetBorderColor 0 0 0 #首饰色
    SetFontSize 43 #首饰色
    DisableDropSound True

Hide # 护符类 - 全部护符55-72
    Class "Charms"
    ItemLevel <= 72
    ItemLevel >= 55
    SetBackgroundColor 0 0 0 151 #普通功能药剂色
    SetBorderColor 136 0 0 150 #普通功能药剂色
    SetFontSize 38 #普通功能药剂色

Show # 护符类 - 全部护符
    Class "Charms"
    SetBackgroundColor 0 0 0 151 #普通功能药剂色
    SetBorderColor 136 0 0 150 #普通功能药剂色
    SetFontSize 38 #普通功能药剂色


# 11 特殊装备类

# 11.1 特殊装备类 - 机会底子装备

Hide # 特殊装备类 - 机会底子装备 - 高价机会石底子(恒星项链，可自己添加)
    BaseType "Shrine Sceptre" "Jade Amulet"
    Rarity = Normal
    ItemLevel >= 1
    SetBackgroundColor 0 0 255 #机会底材色
    SetBorderColor 255 255 255 0 #机会底材色
    SetFontSize 38 #机会底材色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\加油.mp3"
    MinimapIcon 0 Pink Circle
    PlayEffect Pink

# 11.2 特殊装备类 - 涂油装备

Hide # 特殊装备类 - 涂油装备 - 涂油项链
    AnyEnchantment True
    Rarity <= Rare
    Class "Amulets"
    ItemLevel >= 1
    SetTextColor 147 255 255 #涂油装备
    SetBackgroundColor 128 0 128 180 #涂油装备
    SetBorderColor 255 83 83 #涂油装备
    SetFontSize 50 #涂油装备
    DisableDropSound
    CustomAlertSound "文子过滤音效\特殊装备.mp3"
    MinimapIcon 1 Red UpsideDownHouse
    PlayEffect Red 

Hide # 特殊装备类 - 涂油装备 - 涂油戒指
    AnyEnchantment True
    Rarity <= Rare
    Class "Rings"
    ItemLevel >= 1
    SetTextColor 147 255 255 #涂油装备
    SetBackgroundColor 128 0 128 180 #涂油装备
    SetBorderColor 255 83 83 #涂油装备
    SetFontSize 50 #涂油装备
    DisableDropSound
    CustomAlertSound "文子过滤音效\特殊装备.mp3"
    MinimapIcon 1 Red UpsideDownHouse
    PlayEffect Red 


# 11.3 特殊装备类 - 裂隙戒指

Hide # 特殊装备类 - 裂隙戒指 - 裂隙戒指82+白色
    BaseType "Breach Ring"
    ItemLevel >= 82
    Rarity = Normal
    SetBackgroundColor 200 200 200 #裂隙戒指色
    SetBorderColor 0 128 64 #裂隙戒指色
    SetTextColor 255 0 0 #裂隙戒指色
    SetFontSize 50 #裂隙戒指色
    DisableDropSound True
    MinimapIcon 1 Red UpsideDownHouse
    PlayEffect Red

Hide # 特殊装备类 - 裂隙戒指 - 全部裂隙戒指
    BaseType "Breach Ring"
    ItemLevel >= 1
    Rarity <= Rare
    SetBackgroundColor 200 200 200 #裂隙戒指色
    SetBorderColor 0 128 64 #裂隙戒指色
    SetTextColor 0 128 64 #裂隙戒指色
    SetFontSize 40 #裂隙戒指色
    DisableDropSound True
    MinimapIcon 1 Green UpsideDownHouse
    PlayEffect Green



# 12 鉴黄装备

# 12.1 鉴黄装备 - 饰品类

Show # 鉴黄装备 - 饰品类 - 自定义饰品显示(默认紫晶，黑曜)
    BaseType "Amethyst Ring" "Stellar Amulet"
    Rarity = Rare
    SetTextColor 0 0 0 #首饰色
    SetBackgroundColor 255 200 0 200 #首饰色
    SetBorderColor 0 0 0 #首饰色
    SetFontSize 43 #首饰色
    DisableDropSound True

Show # 鉴黄装备 - 饰品类 - 戒指项链
    Class "Rings" "Amulets"
    Rarity = Rare
    SetTextColor 0 0 0 #首饰色
    SetBackgroundColor 255 200 0 200 #首饰色
    SetBorderColor 0 0 0 #首饰色
    SetFontSize 43 #首饰色
    DisableDropSound True

Hide # 鉴黄装备 - 饰品类 - 腰带
    Class "Belts"
    Rarity = Rare
    SetTextColor 0 0 0 #腰带色
    SetBackgroundColor 180 180 80 200 #腰带色
    SetBorderColor 0 0 0 #腰带色
    SetFontSize 42 #腰带色
    DisableDropSound True


# 12.1 鉴黄装备 - 武器类

Show # 鉴黄装备 - 武器类 - 自定义特别高亮（可盒子自定义）
    BaseType  "Attuned Wand" "Seaglass Spear" "Gemini Bow" "Massive Greathammer" "Desolate Crossbow" "Visceral Quiver" "Cultist Focus" "Gemini Bow"
    Rarity = Rare
    SetTextColor 0 0 0 #价值黄装色
    SetBackgroundColor 255 255 119 #价值黄装色
    SetBorderColor 0 0 0 #价值黄装色
    SetFontSize 43 #价值黄装色
    DisableDropSound True

Hide # 鉴黄装备 - 武器类 - 自定义特别高亮（可盒子自定义）
    Class "Bows" "Wands" "Sceptres" "Spears"
    Rarity = Rare
    SetTextColor 0 0 0 #价值黄装色
    SetBackgroundColor 255 255 119 #价值黄装色
    SetBorderColor 0 0 0 #价值黄装色
    SetFontSize 43 #价值黄装色
    DisableDropSound True
    
Hide # 鉴黄装备 - 武器类 - 双手锤（低力量需求）
    Class "Two Hand Maces"
    BaseType "Snakewood Greathammer" "Blacksmith Maul" "Zealot Greathammer" "Solemn Maul" "Heavy Greathammer"
    Rarity = Rare
    SetTextColor 0 0 0 #价值黄装色
    SetBackgroundColor 255 255 119 #价值黄装色
    SetBorderColor 0 0 0 #价值黄装色
    SetFontSize 43 #价值黄装色
    DisableDropSound True


Hide # 鉴黄装备 - 武器类 - 全部武器
    Class "Bows" "Staves" "Two Hand" "Quarterstaves" "Crossbows" "Traps" "Fishing Rods" "Claws" "One Hand" "Daggers" "Wands" "Flails" "Spears" "Sceptres"
    Rarity = Rare
    SetTextColor 255 215 0 #搬砖混沌（单手色）
    SetBackgroundColor 127 127 127 225 #搬砖混沌（单手色）
    SetBorderColor 127 127 127 225 #搬砖混沌（单手色）
    SetFontSize 38#搬砖混沌（单手色）
    DisableDropSound True


# 12.2 鉴黄装备 - 防具类

Show # 鉴黄装备 - 防具类 - 自定义特别高亮（可盒子自定义，法器和盾牌也添加这里）
    BaseType "Sorcerous Tiara" "Kamasan Tiara" "Skycrown Tiara" "Falconer's Jacket" "Rambler Jacket" "Sleek Jacket" "Vile Robe" "Slipstrike Vest" "Sandsworn Sandals" "Bound Sandals" "Luxurious Slippers" "Vaal Gloves" "Vaal Wraps" "Opulent Gloves"
    Rarity = Rare
    SetTextColor 255 215 0 #其他价值黄装色
    SetBackgroundColor 0 128 0 200 #其他价值黄装色
    SetBorderColor 255 255 255 200 #其他价值黄装色
    SetFontSize 42 #其他价值黄装色
    DisableDropSound True

Hide # 鉴黄装备 - 防具类 - 带品质黄装(头手脚盾牌法器)
    Class "Gloves" "Boots" "Helmets"
    Rarity = Rare
    SetTextColor 255 255 119 200 #搬砖混沌（头手脚色）
    SetBackgroundColor 73 118 83 190 #搬砖混沌（头手脚色）
    SetBorderColor 255 255 255 120 #搬砖混沌（头手脚色） 
    SetFontSize 40 #搬砖混沌（头手脚色）
    DisableDropSound True

Hide # 鉴黄装备 - 防具类 - 头手脚
    Class "Gloves" "Boots" "Helmets"
    Rarity = Rare
    SetTextColor 255 255 119 200 #搬砖混沌（头手脚色）
    SetBackgroundColor 73 118 83 190 #搬砖混沌（头手脚色）
    SetBorderColor 255 255 255 0 #搬砖混沌（头手脚色） 
    SetFontSize 40 #搬砖混沌（头手脚色）
    DisableDropSound True

Hide # 鉴黄装备 - 防具类 - 衣服
    Class "Body Armours"
    Rarity = Rare
    SetTextColor 255 235 0 240#搬砖混沌（衣服色）
    SetBackgroundColor 54 100 146 160 #搬砖混沌（衣服色）
    SetBorderColor 255 255 255 0 #搬砖混沌（衣服色）
    SetFontSize 40 #搬砖混沌（衣服色）
    DisableDropSound True



# 12.2 鉴黄装备 - 副手类

Hide # 鉴黄装备 - 副手类 - 法器
    Class "Foci"
    Rarity = Rare
    SetTextColor 255 255 119 200 #搬砖混沌（头手脚色）
    SetBackgroundColor 73 118 83 190 #搬砖混沌（头手脚色）
    SetBorderColor 255 255 255 0 #搬砖混沌（头手脚色） 
    SetFontSize 40 #搬砖混沌（头手脚色）
    DisableDropSound True

Hide # 鉴黄装备 - 副手类 - 盾牌
    Class "Shields"
    Rarity = Rare
    SetTextColor 255 255 119 200 #搬砖混沌（头手脚色）
    SetBackgroundColor 73 118 83 190 #搬砖混沌（头手脚色）
    SetBorderColor 255 255 255 0 #搬砖混沌（头手脚色） 
    SetFontSize 40 #搬砖混沌（头手脚色）
    DisableDropSound True

Hide # 鉴黄装备 - 副手类 - 小圆盾
    Class "Bucklers"
    Rarity = Rare
    SetTextColor 255 255 119 200 #搬砖混沌（头手脚色）
    SetBackgroundColor 73 118 83 190 #搬砖混沌（头手脚色）
    SetBorderColor 255 255 255 0 #搬砖混沌（头手脚色） 
    SetFontSize 40 #搬砖混沌（头手脚色）
    DisableDropSound True

Hide # 鉴黄装备 - 副手类 - 箭袋(武器色加边框高亮)
    Class "Quivers"
    Rarity = Rare
    SetTextColor 255 215 0 #搬砖混沌（单手色）
    SetBackgroundColor 127 127 127 225 #搬砖混沌（单手色）
    SetBorderColor 255 215 0 180 #搬砖混沌（单手色）
    SetFontSize 40 #搬砖混沌（单手色）
    DisableDropSound True





# 13 蓝白装备

Hide # 蓝白装备 - 84+物等全部装备
    Class "Rings" "Amulets" "Belts" "Gloves" "Boots" "Helmets" "Body Armours" "Claws" "One Hand" "Daggers" "Wands" "Sceptres" "Spears" "Flails" "Bows" "Staves" "Two Hand" "Quarterstaves" "Crossbows" "Traps" "Fishing Rods" "Quivers" "Shields" "Foci"
    Rarity <= Rare
    ItemLevel >= 84
    SetBorderColor 255 0 0 120 #高物等装备
    SetFontSize 48 #高物等装备
    DisableDropSound True

# 13.1 蓝白装备 - 饰品类

Hide # 蓝白装备 - 饰品类 - 自定义蓝白饰品（默认紫晶，黑曜，三相戒指）
    BaseType "Amethyst Ring" "Stellar Amulet" "Prismatic Ring"
    Rarity <= Magic
    ItemLevel >= 1
    SetBorderColor 255 0 255 150
    SetFontSize 44
    DisableDropSound True

Hide # 蓝白装备 - 饰品类 - 蓝白戒指
    Class "Rings"
    Rarity <= Magic
    ItemLevel >= 1
    SetBorderColor 255 0 255 150
    SetFontSize 44
    DisableDropSound True

Hide # 蓝白装备 - 饰品类 - 蓝白项链
    Class "Amulets"
    Rarity <= Magic
    ItemLevel >= 1
    SetBorderColor 255 0 255 150
    SetFontSize 44
    DisableDropSound True

Hide # 蓝白装备 - 饰品类 - 蓝色腰带
    Class "Belts"
    Rarity = Magic
    ItemLevel >= 1
    SetBorderColor 255 255 0 120
    SetFontSize 40
    DisableDropSound True



# 13.2 蓝白装备 - 护甲类

Hide # 蓝白装备 - 护甲类 - 自定义81+超级高亮蓝白护甲（默认专家精魄底衣服，法器和盾牌也可添加）
    BaseType "Conjurer Mantle"
    Rarity <= Rare
    ItemLevel >= 81
    SetBackgroundColor 54 100 146 140  #价值底子色
    SetBorderColor 255 0 0 #价值底子色
    SetFontSize 45 #价值底子色
    DisableDropSound True

Hide # 蓝白装备 - 自定义1级+普通高亮蓝白护甲（默认精魄底子,同上）
    BaseType "Corvus Mantle" "Conjurer Mantle"
    Rarity <= Rare
    ItemLevel >= 1
    SetBackgroundColor 54 100 146 140  #价值底子色
    SetBorderColor 255 0 0 80 #价值底子色
    SetFontSize 40 #价值底子色
    DisableDropSound True

Hide # 蓝白装备 - 护甲类 - 自定义55+蓝白护盾装（超蓝边高亮）
    BaseType "Vaal Gloves" "Dunerunner Sandals" "Flowing Raiment" "Vile Robe" "Kamasan Tiara" "Sacred Focus" "Leyline Focus"
    Rarity <= Rare
    ItemLevel >= 55
    SetBorderColor 0 0 255 130 #智慧黄装色
    SetFontSize 42 #价值底子色
    DisableDropSound True

Hide # 蓝白装备 - 护甲类 - 自定义55+蓝白闪避装（超绿边高亮）
    BaseType "Slipstrike Vest" "Wyrmscale Coat" "Trapper Hood" "Desert Cap" "Barbed Bracers" "Dragonscale Boots" "Cavalry Boots"
    Rarity <= Rare
    ItemLevel >= 55
    SetBorderColor 0 255 0 150 #智慧黄装色
    SetFontSize 42 #价值底子色
    DisableDropSound True

Hide # 蓝白装备 - 护甲类 - 自定义55+蓝白头（默认取最高底子各1种+属性头，抗性头）
    BaseType "Paragon Greathelm" "Trapper Hood" "Kamasan Tiara" "Golden Wreath" "Golden Visage" "Sorcerous Tiara"
    Rarity <= Rare
    ItemLevel >= 55
    SetBackgroundColor 54 100 146 140  #价值底子色
    SetBorderColor 73 118 83 120 #护甲黄装色
    SetFontSize 40 #价值底子色
    DisableDropSound True

Hide # 蓝白装备 - 护甲类 - 自定义55+蓝白手（默认取最高底子各1种 +生命手）
    BaseType "Vaal Mitts" "Barbed Bracers" "Vaal Gloves" "Golden Bracers" "Opulent Gloves"
    Rarity <= Rare
    ItemLevel >= 55
    SetBackgroundColor 54 100 146 140  #价值底子色
    SetBorderColor 73 118 83 120 #护甲黄装色
    SetFontSize 40 #价值底子色
    DisableDropSound True

Hide # 蓝白装备 - 护甲类 - 自定义55+蓝白脚（默认取最高底子各1种，抗性脚）
    BaseType "Vaal Greaves" "Dragonscale Boots" "Sandsworn Sandals" "Golden Caligae" "Luxurious Slippers"
    Rarity <= Rare
    ItemLevel >= 55
    SetBackgroundColor 54 100 146 140  #价值底子色
    SetBorderColor 73 118 83 120 #护甲黄装色
    SetFontSize 40 #价值底子色
    DisableDropSound True

Hide # 蓝白装备 - 护甲类 - 自定义55+蓝白衣服（默认取最高底子各1种 加精魄衣 抗性衣）
    BaseType "Conjurer Mantle" "Golden Mantle" "Heroic Armour" "Tournament Mail" "Tournament Mail" "Tournament Mail" "Revered Vestments" "Dastard Armour" "Shrouded Mail" "Shrouded Mail" "Shrouded Mail" "Death Mantle" "Rambler Jacket" "Enlightened Robe" "Flowing Raiment" "Vile Robe"
    Rarity <= Rare
    ItemLevel >= 55
    SetBackgroundColor 54 100 146 140  #价值底子色
    SetBorderColor 73 118 83 120 #护甲黄装色
    SetFontSize 40 #价值底子色
    DisableDropSound True


# 13.2 蓝白装备 - 副手类

Hide # 蓝白装备 - 副手类 - 自定义55+蓝白法器（默认只取最高护盾底子）
    BaseType "Sacred Focus" "Leyline Focus"
    Rarity <= Rare
    ItemLevel >= 55
    SetBackgroundColor 54 100 146 140  #价值底子色
    SetBorderColor 73 118 83 120 #护甲黄装色
    SetFontSize 40 #价值底子色
    DisableDropSound True

Hide # 蓝白装备 - 副手类 - 自定义55+蓝白箭袋（默认只取暴击率，攻速）
    BaseType "Visceral Quiver" "Primed Quiver"
    Rarity <= Rare
    ItemLevel >= 55
    SetBackgroundColor 54 100 146 140  #价值底子色
    SetBorderColor 73 118 83 120 #护甲黄装色
    SetFontSize 40 #价值底子色
    DisableDropSound True

Hide # 蓝白装备 - 副手类 - 自定义55+蓝白盾牌（默认只取最高护甲&护盾复合底子）
    BaseType "Vaal Crest Shield"
    Rarity <= Rare
    ItemLevel >= 55
    SetBackgroundColor 54 100 146 140  #价值底子色
    SetBorderColor 73 118 83 120 #护甲黄装色
    SetFontSize 40 #价值底子色
    DisableDropSound True

Hide # 蓝白装备 - 副手类 -  55+蓝色法器（默认屏蔽，盒子修改显示）
    Class "Foci"
    ItemLevel <= 71
    ItemLevel >= 55
    Rarity = Magic
    SetBorderColor 54 100 146 100
    SetFontSize 40 #价值底子色
    DisableDropSound True

Hide # 蓝白装备 - 副手类 -  55+蓝色盾 （默认屏蔽，盒子修改显示）
    Class "Shields"
    ItemLevel <= 71
    ItemLevel >= 55
    Rarity = Magic
    SetBorderColor 54 100 146 100
    SetFontSize 40 #价值底子色
    DisableDropSound True

Hide # 蓝白装备 - 副手类 -  55+蓝色小圆盾（默认屏蔽，盒子修改显示）
    Class "Bucklers"
    ItemLevel <= 71
    ItemLevel >= 55
    Rarity = Magic
    SetBorderColor 54 100 146 100
    SetFontSize 40 #价值底子色
    DisableDropSound True

Hide # 蓝白装备 - 副手类 -  蓝色箭袋（默认屏蔽，盒子修改显示）
    Class "Quivers"
    ItemLevel <= 73
    ItemLevel >= 55
    Rarity = Magic
    SetBorderColor 54 100 146 100
    SetFontSize 40 #价值底子色
    DisableDropSound True




# 13.3 蓝白装备 - 武器类

Hide # 蓝白装备 - 武器类 - 自定义81+超级高亮蓝白装备（默认调和，虹吸，+1投射弓）
    BaseType "Stalking Spear" "Flying Spear" "Gemini Bow"
    Rarity <= Rare
    ItemLevel >= 81
    SetBackgroundColor 54 100 146 140  #价值底子色
    SetBorderColor 255 0 0 #价值底子色
    SetFontSize 45 #价值底子色
    DisableDropSound True

Hide # 蓝白装备 - 武器类 - 自定义81+普通高亮蓝白装备（默认召唤武器）
    BaseType "Omen Sceptre" "Hallowed Sceptre" "Rattling Sceptre"
    Rarity <= Rare
    ItemLevel >= 81
    SetBackgroundColor 54 100 146 140  #价值底子色
    SetBorderColor 255 0 0 80 #价值底子色
    SetFontSize 42 #价值底子色
    DisableDropSound True

Hide # 蓝白装备 - 武器类- 自定义1级+普通高亮蓝白装备（默认低级调和，虹吸，自己加）
    BaseType "Stalking Spear" "Flying Spear" "Omen Sceptre" "Gemini Bow" "Hallowed Sceptre" "Rattling Sceptre"
    Rarity <= Rare
    ItemLevel >= 1
    SetBackgroundColor 54 100 146 140  #价值底子色
    SetBorderColor 255 0 0 80 #价值底子色
    SetFontSize 40 #价值底子色
    DisableDropSound True

Hide # 蓝白装备 - 武器类 - 自定义显示55+蓝色武器类型（默认弓，战矛，权杖）
    Class "Bows" "Spears" "Sceptres"
    ItemLevel >= 55
    Rarity = Magic
    SetBorderColor 255 128 0 150 #武器装备色
    SetFontSize 44 #价值底子色
    DisableDropSound True

Hide # 蓝白装备 - 武器类 - 自定义显示55+蓝白武器类型（默认弓，战矛）
    Class "Bows" "Spears" "Sceptres"
    ItemLevel >= 55
    Rarity <= Magic
    SetBorderColor 255 128 0 150 #武器装备色
    SetFontSize 44 #价值底子色
    DisableDropSound True


Hide # 蓝白装备 - 武器类 - 81+弓（专家级别）
    Class "Bows"
    BaseType "Gemini Bow" "Guardian Bow" "Warmonger Bow"
    ItemLevel >= 81
    Rarity <= Magic
    SetBorderColor 255 255 119 120 #武器装备色
    SetFontSize 40 #价值底子色
    DisableDropSound True

Hide # 装蓝白装备 - 武器类 - 55+蓝色弓（55+可洗技能,默认屏蔽）
    Class "Bows"
    ItemLevel <= 71
    ItemLevel >= 55
    Rarity = Magic
    SetBorderColor 54 100 146 100
    SetFontSize 40 #价值底子色
    DisableDropSound True


Hide # 装蓝白装备 - 武器类 - 81+双手剑（默认屏蔽，盒子修改显示）
    Class "Two Hand Swords"
    ItemLevel >= 81
    Rarity <= Magic
    SetBorderColor 255 255 119 120 #武器装备色
    SetFontSize 40 #价值底子色
    DisableDropSound True

Hide # 蓝白装备 - 武器类 - 55+双手剑（默认屏蔽，盒子修改显示）
    Class "Two Hand Swords"
    ItemLevel >= 55
    Rarity <= Magic
    SetBorderColor 54 100 146 100
    SetFontSize 40 #价值底子色
    DisableDropSound True

Hide # 蓝白装备 - 武器类 - 81+双手斧
    Class "Two Hand Axes"
    ItemLevel >= 81
    Rarity <= Magic
    SetBorderColor 255 255 119 120 #武器装备色
    SetFontSize 40 #价值底子色
    DisableDropSound True

Hide # 蓝白装备 - 武器类 - 55蓝色双手斧+（默认屏蔽，盒子修改显示）
    Class "Two Hand Axes"
    ItemLevel <= 71
    ItemLevel >= 55
    Rarity = Magic
    SetBorderColor 54 100 146 100
    SetFontSize 40 #价值底子色
    DisableDropSound True

Hide # 蓝白装备 - 武器类 - 81+双手锤 （专家级别）
    Class "Two Hand Maces"
    BaseType "Fanatic Greathammer" "Ruination Maul" "Massive Greathammer" "Anvil Maul"
    ItemLevel >= 81
    Rarity <= Magic
    SetBorderColor 255 255 119 120 #高物等装备
    SetFontSize 40 #价值底子色
    DisableDropSound True

Hide # 蓝白装备 - 武器类 - 55+双手锤 （低力量需求）
    Class "Two Hand Maces"
    BaseType "Zealot Greathammer" "Solemn Maul" "Heavy Greathammer"
    ItemLevel >= 55
    Rarity <= Magic
    SetBorderColor 255 255 119 120 #高物等装备
    SetFontSize 40 #价值底子色
    DisableDropSound True

Hide # 蓝白装备 - 武器类 - 55+全部双手锤（默认屏蔽，盒子修改显示）
    Class "Two Hand Maces"
    ItemLevel >= 55
    Rarity <= Magic
    SetBorderColor 54 100 146 100
    SetFontSize 40 #价值底子色
    DisableDropSound True

Hide # 蓝白装备 - 武器类 - 81+节杖（专家级别）
    Class "Quarterstaves"
    BaseType "Razor Quarterstaff" "Aegis Quarterstaff" "Striking Quarterstaff"
    ItemLevel >= 81
    Rarity <= Magic
    SetBorderColor 255 255 119 120 #武器装备色
    SetFontSize 40 #价值底子色
    DisableDropSound True

Hide # 蓝白装备 - 武器类 - 蓝白武器类55+蓝色节杖（默认屏蔽，盒子修改显示）
    Class "Quarterstaves"
    ItemLevel <= 71
    ItemLevel >= 55
    Rarity = Magic
    SetBorderColor 54 100 146 100
    SetFontSize 40 #价值底子色
    DisableDropSound True

Hide # 蓝白装备 - 武器类 - 81+全部长杖（默认屏蔽，盒子修改显示）
    Class "Staves"
    ItemLevel >= 81
    Rarity <= Magic
    SetBorderColor 255 255 119 120 #武器装备色
    SetFontSize 40 #价值底子色
    DisableDropSound True

Hide # 蓝白装备 - 武器类 - 55+全部长杖（默认屏蔽，盒子修改显示）
    Class "Staves"
    ItemLevel >= 55
    Rarity <= Magic
    SetBorderColor 54 100 146 100
    SetFontSize 40 #价值底子色
    DisableDropSound True

Hide # 蓝白装备 - 武器类 - 81+战弩（专家级别）
    Class "Crossbows"
    BaseType "Desolate Crossbow" "Siege Crossbow" "Gemini Crossbow" "Flexed Crossbow"
    ItemLevel >= 81
    Rarity <= Magic
    SetBorderColor 255 255 119 120 #武器装备色
    SetFontSize 40 #价值底子色
    DisableDropSound True

Hide # 蓝白装备 - 武器类 - 55+蓝色战弩（默认屏蔽，盒子修改显示）
    Class "Crossbows"
    ItemLevel <= 71
    ItemLevel >= 55
    Rarity = Magic
    SetBorderColor 54 100 146 100
    SetFontSize 40 #价值底子色
    DisableDropSound True

Hide # 蓝白装备 - 武器类 - 81+全部爪（默认屏蔽，盒子修改显示）
    Class "Claws"
    ItemLevel >= 81
    Rarity <= Magic
    SetBorderColor 255 255 119 120 #武器装备色
    SetFontSize 40 #价值底子色
    DisableDropSound True

Hide # 蓝白装备 - 武器类 - 55+全部爪（默认屏蔽，盒子修改显示）
    Class "Claws"
    ItemLevel >= 55
    Rarity <= Magic
    SetBorderColor 54 100 146 100
    SetFontSize 40 #价值底子色
    DisableDropSound True

Hide # 蓝白装备 - 武器类 - 81+全部匕首（默认屏蔽，盒子修改显示）
    Class "Daggers"
    ItemLevel >= 81
    Rarity <= Magic
    SetBorderColor 255 255 119 120 #武器装备色
    SetFontSize 40 #价值底子色
    DisableDropSound True

Hide # 蓝白装备 - 武器类 - 55+全部匕首（默认屏蔽，盒子修改显示）
    Class "Daggers"
    ItemLevel >= 55
    Rarity <= Magic
    SetBorderColor 54 100 146 100
    SetFontSize 40 #价值底子色
    DisableDropSound True

Hide # 蓝白装备 - 武器类 - 81+全部法杖（默认显示，盒子修改显示）
    Class "Wands"
    ItemLevel >= 81
    Rarity <= Magic
    SetBorderColor 255 255 119 120 #武器装备色
    SetFontSize 40 #价值底子色
    DisableDropSound True

Hide # 蓝白装备 - 武器类 - 55+蓝色法杖（默认屏蔽，盒子修改显示）
    Class "Wands"
    ItemLevel <= 71
    ItemLevel >= 55
    Rarity = Magic
    SetBorderColor 54 100 146 100
    SetFontSize 40 #价值底子色
    DisableDropSound True

Hide # 蓝白装备 - 武器类 - 81+全部单手剑（默认屏蔽，盒子修改显示）
    Class "One Hand Swords"
    ItemLevel >= 81
    Rarity <= Magic
    SetBorderColor 255 255 119 120 #武器装备色
    SetFontSize 40 #价值底子色
    DisableDropSound True

Hide # 蓝白装备 - 武器类 - 55+全部单手剑（默认屏蔽，盒子修改显示）
    Class "One Hand Swords"
    ItemLevel >= 55
    Rarity <= Magic
    SetBorderColor 54 100 146 100
    SetFontSize 40 #价值底子色
    DisableDropSound True

Hide # 蓝白装备 - 武器类 - 81+全部单手斧（默认屏蔽，盒子修改显示）
    Class "One Hand Axes"
    ItemLevel >= 81
    Rarity <= Magic
    SetBorderColor 255 255 119 120 #武器装备色
    SetFontSize 40 #价值底子色
    DisableDropSound True

Hide # 蓝白装备 - 武器类 - 55+全部单手斧（默认屏蔽，盒子修改显示）
    Class "One Hand Axes"
    ItemLevel >= 55
    Rarity <= Magic
    SetBorderColor 54 100 146 100
    SetFontSize 40 #价值底子色
    DisableDropSound True

Hide # 蓝白装备 - 武器类 - 81+全部单手锤（默认屏蔽，盒子修改显示）
    Class "One Hand Maces"
    BaseType "Strife Pick" "Marauding Mace"
    ItemLevel >= 81
    Rarity <= Magic
    SetBorderColor 255 255 119 120 #武器装备色
    SetFontSize 40 #价值底子色
    DisableDropSound True

Hide # 蓝白装备 - 武器类 - 55+全部单手锤（默认屏蔽，盒子修改显示）
    Class "One Hand Maces"
    ItemLevel >= 55
    Rarity <= Magic
    SetBorderColor 54 100 146 100
    SetFontSize 40 #价值底子色
    DisableDropSound True

Hide # 蓝白装备 - 武器类 - 81+全部短杖（默认显示，盒子修改显示）
    Class "Sceptres"
    ItemLevel >= 81
    Rarity <= Magic
    SetBorderColor 255 255 119 120 #武器装备色
    SetFontSize 40 #价值底子色
    DisableDropSound True

Hide # 蓝白装备 - 武器类 - 55+蓝色短杖（默认屏蔽，盒子修改显示）
    Class "Sceptres"
    ItemLevel <= 71
    ItemLevel >= 55
    Rarity = Magic
    SetBorderColor 54 100 146 100
    SetFontSize 40 #价值底子色
    DisableDropSound True

Hide # 蓝白装备 - 武器类 - 71+全部战矛（默认屏蔽，盒子修改显示）
    Class "Spears"
    BaseType "Flying Spear" "Spiked Spear" "Grand Spear" "Stalking Spear"
    ItemLevel >= 71
    Rarity <= Magic
    SetBorderColor 255 255 119 120 #武器装备色
    SetFontSize 40 #价值底子色
    DisableDropSound True

Hide # 蓝白装备 - 武器类 - 55+蓝色战矛（默认屏蔽，盒子修改显示）
    Class "Spears"
    ItemLevel <= 71
    ItemLevel >= 55
    Rarity = Magic
    SetBorderColor 54 100 146 100
    SetFontSize 42 #价值底子色
    DisableDropSound True

Hide # 蓝白装备 - 武器类 - 81+全部连枷（默认屏蔽，盒子修改显示）
    Class "Flails"
    ItemLevel >= 81
    Rarity <= Magic
    SetBorderColor 255 255 119 120 #武器装备色
    SetFontSize 40 #价值底子色
    DisableDropSound True

Hide # 蓝白装备 - 武器类 - 55+全部连枷（默认屏蔽，盒子修改显示）
    Class "Flails"
    ItemLevel >= 55
    Rarity <= Magic
    SetBorderColor 54 100 146 100
    SetFontSize 40 #价值底子色
    DisableDropSound True


# 13.4 蓝白装备 - 带洞蓝白装

Hide # 蓝白装备 - 带洞蓝白装 - 装备3洞以上
    Rarity <= Rare
    Sockets "3" "4" "5"
    SetBorderColor 255 255 255
    SetFontSize 42
    DisableDropSound True

Hide # 蓝白装备 - 带洞蓝白装 - 装备2洞以上
    Rarity <= Rare
    Sockets "2" "3" "4"
    SetBorderColor 255 255 255
    SetFontSize 42
    DisableDropSound True

Hide # 蓝白装备 - 带洞蓝白装 - 装备1洞以上
    Rarity <= Rare
    Sockets "1"
    ItemLevel <= 70
    SetFontSize 32
    SetBorderColor 255 255 255
    DisableDropSound True


# 13.5 蓝白装备 - 带品蓝白装

Hide # 蓝白装备 - 带品蓝白装 - 武器类 1品以上
    Class "Rings" "Amulets" "Belts" "Claws" "One Hand" "Daggers" "Wands" "Sceptres" "Spears" "Flails" "Bows" "Staves" "Two Hand" "Quarterstaves" "Crossbows" "Traps" "Fishing Rods"
    Quality >= 1
    Rarity <= Rare
    SetBorderColor 255 215 0 180 #品质色
    SetFontSize 45
    DisableDropSound True

Hide # 蓝白装备 - 带品蓝白装 - 护甲类1品以上
    Class "Gloves" "Boots" "Helmets" "Body Armours" "Quivers" "Shields" "Foci"
    Quality >= 1
    Rarity <= Rare
    SetBorderColor 255 215 0 180 #品质色
    SetFontSize 45
    DisableDropSound True



# 14 开荒装备

Hide # 开荒装备 - 60下装备2洞
    Rarity <= Rare
    Sockets "2"
    SetBorderColor 255 255 255
    SetFontSize 42 #价值底子色
    DisableDropSound True

Hide # 开荒装备 - 40以下装备1洞
    ItemLevel < 30
    Rarity <= Rare
    Sockets "1"
    SetFontSize 40
    SetBorderColor 255 255 255
    DisableDropSound True


# 15 其他物品

Show # 其他物品 - 任务物品
    Class "Quest Items"
    SetTextColor 74 230 58 #任务物品色
    SetBackgroundColor 12 54 20 #任务物品色
    SetBorderColor 0 0 0 0 #任务物品色
    SetFontSize 45 #任务物品色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\任务物品.mp3"
    MinimapIcon 0 Orange Triangle
    PlayEffect Orange	
	
# 16 全局设置

Show # 全局设置 - 全局暗金设置（屏蔽暗金先屏蔽这里）
    Rarity = Unique
    SetTextColor 180 96 0 180 #全局暗金色
    SetBorderColor 180 96 0 180 #全局暗金色
    SetFontSize 42 #全局暗金色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\传说.mp3"
    MinimapIcon 1 Yellow UpsideDownHouse
    PlayEffect Yellow

Show # 全局设置 - 全局黄装设定
    Rarity = Rare
    SetTextColor 255 215 0 #全局黄装色
    SetBackgroundColor 0 0 0 151 #全局黄装色
    SetFontSize 35 #全局黄装色
    DisableDropSound True

Hide # 全局设置 - 全局蓝装设定
    Class "Rings" "Amulets" "Belts" "Gloves" "Boots" "Helmets" "Body Armours" "Claws" "One Hand" "Daggers" "Wands" "Sceptres" "Spears" "Flails" "Bows" "Staves" "Two Hand" "Quarterstaves" "Crossbows" "Traps" "Fishing Rods" "Quivers" "Shields" "Foci" "Bucklers"
    Rarity = Magic
    SetBackgroundColor 255 255 255 0 #全局蓝白色
    SetFontSize 10 #全局蓝白色
    DisableDropSound True

Hide # 全局设置 - 全局白装设定
    Class "Rings" "Amulets" "Belts" "Gloves" "Boots" "Helmets" "Body Armours" "Claws" "One Hand" "Daggers" "Wands" "Sceptres" "Spears" "Flails" "Bows" "Staves" "Two Hand" "Quarterstaves" "Crossbows" "Traps" "Fishing Rods" "Quivers" "Shields" "Foci" "Bucklers"
    Rarity = Normal
    SetBackgroundColor 255 255 255 0 #全局蓝白色
    SetFontSize 10 #全局蓝白色
    DisableDropSound True

Show # 全局设置 - 活动通货 福袋等
    Class "Currency"
    BaseType "Coin" "Shard"
    SetTextColor 0 128 0 #活动通货色
    SetBackgroundColor 15 15 15 #活动通货色
    SetBorderColor 0 128 0 #活动通货色
    SetFontSize 45 #活动通货色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\活动通货.mp3"
    PlayEffect Red

Show # 全局设置 - 其他遗漏物品,需更新过滤
    SetTextColor 255 255 255 #遗漏色
    SetBackgroundColor 100 220 145 #遗漏色
    SetBorderColor 0 0 0 0 #遗漏色
    SetFontSize 45 #遗漏色
    DisableDropSound True
    CustomAlertSound "文子过滤音效\拜托~更新一下过滤吧.mp3"
    MinimapIcon 1 Pink Star
    PlayEffect Pink
