{"Rules": [{"Conditions": [], "delayBetweenRuns": 0.6, "Enabled": true, "Name": "电球", "SkillName": "電球_80C00040", "Key": 0, "IsHold": true, "IsEvade": false, "EvadeRange": 15, "HoldTime": 0.6, "IsUserCooldown": true, "IsWalkable": false, "IsMove": false, "Rarity": [1, 0, 2, 3]}, {"Conditions": [], "delayBetweenRuns": 10.0, "Enabled": true, "Name": "烈焰之墙", "SkillName": "烈焰之牆_80C20040", "Key": 0, "IsHold": false, "IsEvade": false, "EvadeRange": 40, "HoldTime": 1.0, "IsUserCooldown": true, "IsWalkable": false, "IsMove": false, "Rarity": [2, 3, 1]}, {"Conditions": [], "delayBetweenRuns": 10.0, "Enabled": true, "Name": "魔力风暴", "SkillName": "魔力暴風_80C30040", "Key": 0, "IsHold": false, "IsEvade": false, "EvadeRange": 30, "HoldTime": 1.0, "IsUserCooldown": true, "IsWalkable": false, "IsMove": false, "Rarity": [2, 3]}, {"Conditions": [], "delayBetweenRuns": 5.0, "Enabled": true, "Name": "翻滚", "SkillName": "翻滚_80000040", "Key": 0, "IsHold": false, "IsEvade": false, "EvadeRange": 20, "HoldTime": 1.0, "IsUserCooldown": true, "IsWalkable": true, "IsMove": false, "Rarity": []}, {"Conditions": [{"$type": "DD.ProfileManager.Conditions.StatusEffectCondition, DD", "buffId": "demon_form_spell_gem_buff", "checkType": "层数", "operator": "不包含", "threshold": 0.0, "component": null}], "delayBetweenRuns": 600.0, "Enabled": false, "Name": "变身", "SkillName": "惡魔變身", "Key": 0, "IsHold": false, "IsEvade": false, "EvadeRange": 0, "HoldTime": 1.0, "IsUserCooldown": true, "IsWalkable": true, "IsMove": false, "Rarity": []}]}