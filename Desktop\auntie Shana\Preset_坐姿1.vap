{"setUnlistedParamsToDefault": "true", "V2": "true", "storables": [{"id": "hipControl", "detachControl": "false", "interactableInPlayMode": "true", "deactivateOtherControlsOnPossess": "true", "possessable": "false", "canGrabPosition": "true", "canGrabRotation": "true", "freezeAtomPhysicsWhenGrabbed": "false", "xPositionLock": "false", "yPositionLock": "false", "zPositionLock": "false", "xPositionLocalLock": "false", "yPositionLocalLock": "false", "zPositionLocalLock": "false", "xRotationLock": "false", "yRotationLock": "false", "zRotationLock": "false", "physicsEnabled": "true", "useGravity": "true", "maxVelocityEnable": "true", "complyPositionThreshold": "0.001", "complyRotationThreshold": "5", "complySpeed": "10", "positionGrid": "0.1", "rotationGrid": "15", "mass": "4", "drag": "1", "maxVelocity": "10", "angularDrag": "10", "holdPositionSpring": "2000", "holdPositionDamper": "35", "holdPositionMaxForce": "1000", "holdRotationSpring": "250", "holdRotationDamper": "5", "holdRotationMaxForce": "1000", "complyPositionSpring": "1500", "complyPositionDamper": "100", "complyRotationSpring": "150", "complyRotationDamper": "10", "linkPositionSpring": "100000", "linkPositionDamper": "250", "linkPositionMaxForce": "100000", "linkRotationSpring": "100000", "linkRotationDamper": "250", "linkRotationMaxForce": "100000", "complyJointDriveSpring": "20", "jointDriveSpring": "0", "jointDriveDamper": "0", "jointDriveMaxForce": "0", "jointDriveXTarget": "0", "jointDriveYTarget": "0", "jointDriveZTarget": "0", "positionState": "On", "rotationState": "On", "positionGridMode": "None", "rotationGridMode": "None", "localPosition": {"x": "0.01073978", "y": "0.683948", "z": "0.0238285"}, "localRotation": {"x": "9.311136", "y": "355.9423", "z": "6.367592"}}, {"id": "pelvisControl", "detachControl": "false", "interactableInPlayMode": "true", "deactivateOtherControlsOnPossess": "true", "possessable": "false", "canGrabPosition": "true", "canGrabRotation": "true", "freezeAtomPhysicsWhenGrabbed": "false", "xPositionLock": "false", "yPositionLock": "false", "zPositionLock": "false", "xPositionLocalLock": "false", "yPositionLocalLock": "false", "zPositionLocalLock": "false", "xRotationLock": "false", "yRotationLock": "false", "zRotationLock": "false", "physicsEnabled": "true", "useGravity": "true", "maxVelocityEnable": "true", "complyPositionThreshold": "0.001", "complyRotationThreshold": "5", "complySpeed": "10", "positionGrid": "0.1", "rotationGrid": "15", "mass": "2", "drag": "1", "maxVelocity": "10", "angularDrag": "10", "holdPositionSpring": "2000", "holdPositionDamper": "35", "holdPositionMaxForce": "1000", "holdRotationSpring": "100", "holdRotationDamper": "5", "holdRotationMaxForce": "1000", "complyPositionSpring": "1500", "complyPositionDamper": "100", "complyRotationSpring": "150", "complyRotationDamper": "10", "linkPositionSpring": "100000", "linkPositionDamper": "250", "linkPositionMaxForce": "100000", "linkRotationSpring": "100000", "linkRotationDamper": "250", "linkRotationMaxForce": "100000", "complyJointDriveSpring": "20", "jointDriveSpring": "150", "jointDriveDamper": "1", "jointDriveMaxForce": "500", "jointDriveXTarget": "0", "jointDriveYTarget": "0", "jointDriveZTarget": "0", "positionState": "Off", "rotationState": "Off", "positionGridMode": "None", "rotationGridMode": "None", "localPosition": {"x": "0.004911575", "y": "0.7202749", "z": "-0.02202973"}, "localRotation": {"x": "12.41747", "y": "3.559528", "z": "2.829485"}}, {"id": "chestControl", "detachControl": "false", "interactableInPlayMode": "true", "deactivateOtherControlsOnPossess": "true", "possessable": "false", "canGrabPosition": "true", "canGrabRotation": "true", "freezeAtomPhysicsWhenGrabbed": "false", "xPositionLock": "false", "yPositionLock": "false", "zPositionLock": "false", "xPositionLocalLock": "false", "yPositionLocalLock": "false", "zPositionLocalLock": "false", "xRotationLock": "false", "yRotationLock": "false", "zRotationLock": "false", "physicsEnabled": "true", "useGravity": "true", "maxVelocityEnable": "true", "complyPositionThreshold": "0.001", "complyRotationThreshold": "5", "complySpeed": "10", "positionGrid": "0.1", "rotationGrid": "15", "mass": "2", "drag": "1", "maxVelocity": "10", "angularDrag": "10", "holdPositionSpring": "2000", "holdPositionDamper": "35", "holdPositionMaxForce": "1000", "holdRotationSpring": "100", "holdRotationDamper": "5", "holdRotationMaxForce": "1000", "complyPositionSpring": "1500", "complyPositionDamper": "100", "complyRotationSpring": "150", "complyRotationDamper": "10", "linkPositionSpring": "100000", "linkPositionDamper": "250", "linkPositionMaxForce": "100000", "linkRotationSpring": "100000", "linkRotationDamper": "250", "linkRotationMaxForce": "100000", "complyJointDriveSpring": "20", "jointDriveSpring": "50", "jointDriveDamper": "1", "jointDriveMaxForce": "500", "jointDriveXTarget": "-11.42117", "jointDriveYTarget": "0", "jointDriveZTarget": "0", "positionState": "On", "rotationState": "On", "positionGridMode": "None", "rotationGridMode": "None", "localPosition": {"x": "0.01174238", "y": "0.9565721", "z": "-0.05365133"}, "localRotation": {"x": "342.0694", "y": "12.01548", "z": "350.1934"}}, {"id": "headControl", "detachControl": "false", "interactableInPlayMode": "true", "deactivateOtherControlsOnPossess": "true", "possessable": "true", "canGrabPosition": "true", "canGrabRotation": "true", "freezeAtomPhysicsWhenGrabbed": "false", "xPositionLock": "false", "yPositionLock": "false", "zPositionLock": "false", "xPositionLocalLock": "false", "yPositionLocalLock": "false", "zPositionLocalLock": "false", "xRotationLock": "false", "yRotationLock": "false", "zRotationLock": "false", "physicsEnabled": "true", "useGravity": "true", "maxVelocityEnable": "true", "complyPositionThreshold": "0.001", "complyRotationThreshold": "10", "complySpeed": "10", "positionGrid": "0.1", "rotationGrid": "15", "mass": "1", "drag": "1", "maxVelocity": "10", "angularDrag": "10", "holdPositionSpring": "2000", "holdPositionDamper": "35", "holdPositionMaxForce": "1000", "holdRotationSpring": "250", "holdRotationDamper": "5", "holdRotationMaxForce": "1000", "complyPositionSpring": "1500", "complyPositionDamper": "100", "complyRotationSpring": "150", "complyRotationDamper": "10", "linkPositionSpring": "100000", "linkPositionDamper": "250", "linkPositionMaxForce": "100000", "linkRotationSpring": "100000", "linkRotationDamper": "250", "linkRotationMaxForce": "100000", "complyJointDriveSpring": "80", "jointDriveSpring": "400", "jointDriveDamper": "5", "jointDriveMaxForce": "70", "jointDriveXTarget": "0", "jointDriveYTarget": "0", "jointDriveZTarget": "0", "positionState": "On", "rotationState": "On", "positionGridMode": "None", "rotationGridMode": "None", "localPosition": {"x": "0.04454126", "y": "1.218954", "z": "0.05908847"}, "localRotation": {"x": "338.0308", "y": "32.91173", "z": "355.8386"}}, {"id": "rHandControl", "detachControl": "false", "interactableInPlayMode": "true", "deactivateOtherControlsOnPossess": "true", "possessable": "true", "canGrabPosition": "true", "canGrabRotation": "true", "freezeAtomPhysicsWhenGrabbed": "false", "xPositionLock": "false", "yPositionLock": "false", "zPositionLock": "false", "xPositionLocalLock": "false", "yPositionLocalLock": "false", "zPositionLocalLock": "false", "xRotationLock": "false", "yRotationLock": "false", "zRotationLock": "false", "physicsEnabled": "true", "useGravity": "true", "maxVelocityEnable": "true", "complyPositionThreshold": "0.001", "complyRotationThreshold": "5", "complySpeed": "10", "positionGrid": "0.1", "rotationGrid": "15", "mass": "1", "drag": "0.1", "maxVelocity": "10", "angularDrag": "10", "holdPositionSpring": "2000", "holdPositionDamper": "35", "holdPositionMaxForce": "1000", "holdRotationSpring": "100", "holdRotationDamper": "5", "holdRotationMaxForce": "1000", "complyPositionSpring": "1500", "complyPositionDamper": "100", "complyRotationSpring": "150", "complyRotationDamper": "10", "linkPositionSpring": "100000", "linkPositionDamper": "250", "linkPositionMaxForce": "100000", "linkRotationSpring": "100000", "linkRotationDamper": "250", "linkRotationMaxForce": "100000", "complyJointDriveSpring": "10", "jointDriveSpring": "40", "jointDriveDamper": "0.5", "jointDriveMaxForce": "20", "jointDriveXTarget": "0", "jointDriveYTarget": "0", "jointDriveZTarget": "0", "positionState": "On", "rotationState": "On", "positionGridMode": "None", "rotationGridMode": "None", "localPosition": {"x": "0.2475686", "y": "1.113232", "z": "0.1213311"}, "localRotation": {"x": "4.033085", "y": "185.9503", "z": "32.82313"}}, {"id": "lHandControl", "detachControl": "false", "interactableInPlayMode": "true", "deactivateOtherControlsOnPossess": "true", "possessable": "true", "canGrabPosition": "true", "canGrabRotation": "true", "freezeAtomPhysicsWhenGrabbed": "false", "xPositionLock": "false", "yPositionLock": "false", "zPositionLock": "false", "xPositionLocalLock": "false", "yPositionLocalLock": "false", "zPositionLocalLock": "false", "xRotationLock": "false", "yRotationLock": "false", "zRotationLock": "false", "physicsEnabled": "true", "useGravity": "true", "maxVelocityEnable": "true", "complyPositionThreshold": "0.001", "complyRotationThreshold": "5", "complySpeed": "10", "positionGrid": "0.1", "rotationGrid": "15", "mass": "1", "drag": "0.1", "maxVelocity": "10", "angularDrag": "10", "holdPositionSpring": "2000", "holdPositionDamper": "35", "holdPositionMaxForce": "1000", "holdRotationSpring": "100", "holdRotationDamper": "5", "holdRotationMaxForce": "1000", "complyPositionSpring": "1500", "complyPositionDamper": "100", "complyRotationSpring": "150", "complyRotationDamper": "10", "linkPositionSpring": "100000", "linkPositionDamper": "250", "linkPositionMaxForce": "100000", "linkRotationSpring": "100000", "linkRotationDamper": "250", "linkRotationMaxForce": "100000", "complyJointDriveSpring": "10", "jointDriveSpring": "40", "jointDriveDamper": "0.5", "jointDriveMaxForce": "20", "jointDriveXTarget": "0", "jointDriveYTarget": "0", "jointDriveZTarget": "0", "positionState": "On", "rotationState": "On", "positionGridMode": "None", "rotationGridMode": "None", "localPosition": {"x": "-0.05575329", "y": "0.8280907", "z": "0.1417636"}, "localRotation": {"x": "282.3566", "y": "131.4907", "z": "329.0494"}}, {"id": "rFootControl", "detachControl": "false", "interactableInPlayMode": "true", "deactivateOtherControlsOnPossess": "true", "possessable": "true", "canGrabPosition": "true", "canGrabRotation": "true", "freezeAtomPhysicsWhenGrabbed": "false", "xPositionLock": "false", "yPositionLock": "false", "zPositionLock": "false", "xPositionLocalLock": "false", "yPositionLocalLock": "false", "zPositionLocalLock": "false", "xRotationLock": "false", "yRotationLock": "false", "zRotationLock": "false", "physicsEnabled": "true", "useGravity": "true", "maxVelocityEnable": "true", "complyPositionThreshold": "0.001", "complyRotationThreshold": "5", "complySpeed": "10", "positionGrid": "0.1", "rotationGrid": "15", "mass": "1", "drag": "1", "maxVelocity": "10", "angularDrag": "10", "holdPositionSpring": "2000", "holdPositionDamper": "35", "holdPositionMaxForce": "1000", "holdRotationSpring": "250", "holdRotationDamper": "5", "holdRotationMaxForce": "1000", "complyPositionSpring": "1500", "complyPositionDamper": "100", "complyRotationSpring": "150", "complyRotationDamper": "10", "linkPositionSpring": "100000", "linkPositionDamper": "250", "linkPositionMaxForce": "100000", "linkRotationSpring": "100000", "linkRotationDamper": "250", "linkRotationMaxForce": "100000", "complyJointDriveSpring": "10", "jointDriveSpring": "200", "jointDriveDamper": "0.5", "jointDriveMaxForce": "50", "jointDriveXTarget": "0", "jointDriveYTarget": "0", "jointDriveZTarget": "0", "positionState": "On", "rotationState": "On", "positionGridMode": "None", "rotationGridMode": "None", "localPosition": {"x": "0.1609607", "y": "0.1070883", "z": "0.444966"}, "localRotation": {"x": "37.67424", "y": "1.748458", "z": "15.39651"}}, {"id": "lFootControl", "detachControl": "false", "interactableInPlayMode": "true", "deactivateOtherControlsOnPossess": "true", "possessable": "true", "canGrabPosition": "true", "canGrabRotation": "true", "freezeAtomPhysicsWhenGrabbed": "false", "xPositionLock": "false", "yPositionLock": "false", "zPositionLock": "false", "xPositionLocalLock": "false", "yPositionLocalLock": "false", "zPositionLocalLock": "false", "xRotationLock": "false", "yRotationLock": "false", "zRotationLock": "false", "physicsEnabled": "true", "useGravity": "true", "maxVelocityEnable": "true", "complyPositionThreshold": "0.001", "complyRotationThreshold": "5", "complySpeed": "10", "positionGrid": "0.1", "rotationGrid": "15", "mass": "1", "drag": "1", "maxVelocity": "10", "angularDrag": "10", "holdPositionSpring": "2000", "holdPositionDamper": "35", "holdPositionMaxForce": "1000", "holdRotationSpring": "250", "holdRotationDamper": "5", "holdRotationMaxForce": "1000", "complyPositionSpring": "1500", "complyPositionDamper": "100", "complyRotationSpring": "150", "complyRotationDamper": "10", "linkPositionSpring": "100000", "linkPositionDamper": "250", "linkPositionMaxForce": "100000", "linkRotationSpring": "100000", "linkRotationDamper": "250", "linkRotationMaxForce": "100000", "complyJointDriveSpring": "10", "jointDriveSpring": "200", "jointDriveDamper": "0.5", "jointDriveMaxForce": "50", "jointDriveXTarget": "0", "jointDriveYTarget": "0", "jointDriveZTarget": "0", "positionState": "On", "rotationState": "On", "positionGridMode": "None", "rotationGridMode": "None", "localPosition": {"x": "-0.1608843", "y": "0.05246329", "z": "0.3764788"}, "localRotation": {"x": "28.63853", "y": "5.113371", "z": "5.620852"}}, {"id": "neckControl", "detachControl": "false", "interactableInPlayMode": "false", "deactivateOtherControlsOnPossess": "true", "possessable": "false", "canGrabPosition": "true", "canGrabRotation": "true", "freezeAtomPhysicsWhenGrabbed": "false", "xPositionLock": "false", "yPositionLock": "false", "zPositionLock": "false", "xPositionLocalLock": "false", "yPositionLocalLock": "false", "zPositionLocalLock": "false", "xRotationLock": "false", "yRotationLock": "false", "zRotationLock": "false", "physicsEnabled": "true", "useGravity": "false", "maxVelocityEnable": "true", "complyPositionThreshold": "0.001", "complyRotationThreshold": "5", "complySpeed": "10", "positionGrid": "0.1", "rotationGrid": "15", "mass": "0.7", "drag": "1", "maxVelocity": "10", "angularDrag": "10", "holdPositionSpring": "2000", "holdPositionDamper": "35", "holdPositionMaxForce": "1000", "holdRotationSpring": "100", "holdRotationDamper": "5", "holdRotationMaxForce": "1000", "complyPositionSpring": "1500", "complyPositionDamper": "100", "complyRotationSpring": "150", "complyRotationDamper": "10", "linkPositionSpring": "100000", "linkPositionDamper": "250", "linkPositionMaxForce": "100000", "linkRotationSpring": "100000", "linkRotationDamper": "250", "linkRotationMaxForce": "100000", "complyJointDriveSpring": "20", "jointDriveSpring": "20", "jointDriveDamper": "2", "jointDriveMaxForce": "25", "jointDriveXTarget": "0", "jointDriveYTarget": "0", "jointDriveZTarget": "0", "positionState": "On", "rotationState": "Off", "positionGridMode": "None", "rotationGridMode": "None", "localPosition": {"x": "0.01085974", "y": "1.128793", "z": "0.05762113"}, "localRotation": {"x": "2.324056", "y": "29.59408", "z": "359.4521"}}, {"id": "eyeTargetControl", "interactableInPlayMode": "true", "deactivateOtherControlsOnPossess": "true", "possessable": "false", "canGrabPosition": "true", "canGrabRotation": "true", "freezeAtomPhysicsWhenGrabbed": "false", "xPositionLock": "false", "yPositionLock": "false", "zPositionLock": "false", "xPositionLocalLock": "false", "yPositionLocalLock": "false", "zPositionLocalLock": "false", "xRotationLock": "false", "yRotationLock": "false", "zRotationLock": "false", "physicsEnabled": "false", "useGravity": "false", "maxVelocityEnable": "true", "complyPositionThreshold": "0.001", "complyRotationThreshold": "5", "complySpeed": "10", "positionGrid": "0.1", "rotationGrid": "15", "mass": "0.01", "drag": "0", "maxVelocity": "10", "angularDrag": "0", "holdPositionSpring": "100", "holdPositionDamper": "0", "holdPositionMaxForce": "10", "holdRotationSpring": "0", "holdRotationDamper": "0", "holdRotationMaxForce": "0", "complyPositionSpring": "1500", "complyPositionDamper": "100", "complyRotationSpring": "150", "complyRotationDamper": "10", "linkPositionSpring": "100000", "linkPositionDamper": "250", "linkPositionMaxForce": "100000", "linkRotationSpring": "100000", "linkRotationDamper": "250", "linkRotationMaxForce": "100000", "complyJointDriveSpring": "20", "jointDriveSpring": "0", "jointDriveDamper": "0", "jointDriveMaxForce": "0", "jointDriveXTarget": "0", "jointDriveYTarget": "0", "jointDriveZTarget": "0", "positionState": "On", "rotationState": "On", "positionGridMode": "None", "rotationGridMode": "None", "localPosition": {"x": "0.371175", "y": "1.793903", "z": "1.269015"}, "localRotation": {"x": "351.6945", "y": "83.37861", "z": "357.1643"}}, {"id": "rNippleControl", "detachControl": "false", "interactableInPlayMode": "true", "deactivateOtherControlsOnPossess": "true", "possessable": "false", "canGrabPosition": "true", "canGrabRotation": "true", "freezeAtomPhysicsWhenGrabbed": "false", "xPositionLock": "false", "yPositionLock": "false", "zPositionLock": "false", "xPositionLocalLock": "false", "yPositionLocalLock": "false", "zPositionLocalLock": "false", "xRotationLock": "false", "yRotationLock": "false", "zRotationLock": "false", "physicsEnabled": "true", "useGravity": "false", "maxVelocityEnable": "true", "complyPositionThreshold": "0.001", "complyRotationThreshold": "5", "complySpeed": "10", "positionGrid": "0.1", "rotationGrid": "15", "mass": "0.1", "drag": "0.1", "maxVelocity": "10", "angularDrag": "0", "holdPositionSpring": "100", "holdPositionDamper": "0", "holdPositionMaxForce": "10", "holdRotationSpring": "0", "holdRotationDamper": "0", "holdRotationMaxForce": "0", "complyPositionSpring": "1500", "complyPositionDamper": "100", "complyRotationSpring": "150", "complyRotationDamper": "10", "linkPositionSpring": "100000", "linkPositionDamper": "250", "linkPositionMaxForce": "100000", "linkRotationSpring": "100000", "linkRotationDamper": "250", "linkRotationMaxForce": "100000", "complyJointDriveSpring": "20", "jointDriveSpring": "0", "jointDriveDamper": "0", "jointDriveMaxForce": "1000", "jointDriveXTarget": "0", "jointDriveYTarget": "0", "jointDriveZTarget": "0", "positionState": "Off", "rotationState": "Off", "positionGridMode": "None", "rotationGridMode": "None", "localPosition": {"x": "0.1594675", "y": "0.9324719", "z": "0.1485265"}, "localRotation": {"x": "357.739", "y": "217.041", "z": "2.865904"}}, {"id": "lNippleControl", "detachControl": "false", "interactableInPlayMode": "true", "deactivateOtherControlsOnPossess": "true", "possessable": "false", "canGrabPosition": "true", "canGrabRotation": "true", "freezeAtomPhysicsWhenGrabbed": "false", "xPositionLock": "false", "yPositionLock": "false", "zPositionLock": "false", "xPositionLocalLock": "false", "yPositionLocalLock": "false", "zPositionLocalLock": "false", "xRotationLock": "false", "yRotationLock": "false", "zRotationLock": "false", "physicsEnabled": "true", "useGravity": "false", "maxVelocityEnable": "true", "complyPositionThreshold": "0.001", "complyRotationThreshold": "5", "complySpeed": "10", "positionGrid": "0.1", "rotationGrid": "15", "mass": "0.1", "drag": "0.1", "maxVelocity": "10", "angularDrag": "0", "holdPositionSpring": "100", "holdPositionDamper": "0", "holdPositionMaxForce": "10", "holdRotationSpring": "0", "holdRotationDamper": "0", "holdRotationMaxForce": "0", "complyPositionSpring": "1500", "complyPositionDamper": "100", "complyRotationSpring": "150", "complyRotationDamper": "10", "linkPositionSpring": "100000", "linkPositionDamper": "250", "linkPositionMaxForce": "100000", "linkRotationSpring": "100000", "linkRotationDamper": "250", "linkRotationMaxForce": "100000", "complyJointDriveSpring": "20", "jointDriveSpring": "0", "jointDriveDamper": "0", "jointDriveMaxForce": "0", "jointDriveXTarget": "0", "jointDriveYTarget": "0", "jointDriveZTarget": "0", "positionState": "Off", "rotationState": "Off", "positionGridMode": "None", "rotationGridMode": "None", "localPosition": {"x": "-0.03287243", "y": "0.9263792", "z": "0.1817761"}, "localRotation": {"x": "356.8416", "y": "162.9438", "z": "355.9118"}}, {"id": "rElbowControl", "detachControl": "false", "interactableInPlayMode": "false", "deactivateOtherControlsOnPossess": "true", "possessable": "false", "canGrabPosition": "true", "canGrabRotation": "true", "freezeAtomPhysicsWhenGrabbed": "false", "xPositionLock": "false", "yPositionLock": "false", "zPositionLock": "false", "xPositionLocalLock": "false", "yPositionLocalLock": "false", "zPositionLocalLock": "false", "xRotationLock": "false", "yRotationLock": "false", "zRotationLock": "false", "physicsEnabled": "true", "useGravity": "true", "maxVelocityEnable": "true", "complyPositionThreshold": "0.001", "complyRotationThreshold": "5", "complySpeed": "10", "positionGrid": "0.1", "rotationGrid": "15", "mass": "0.3", "drag": "0.1", "maxVelocity": "10", "angularDrag": "10", "holdPositionSpring": "2000", "holdPositionDamper": "35", "holdPositionMaxForce": "1000", "holdRotationSpring": "100", "holdRotationDamper": "5", "holdRotationMaxForce": "1000", "complyPositionSpring": "1500", "complyPositionDamper": "100", "complyRotationSpring": "150", "complyRotationDamper": "10", "linkPositionSpring": "100000", "linkPositionDamper": "250", "linkPositionMaxForce": "100000", "linkRotationSpring": "100000", "linkRotationDamper": "250", "linkRotationMaxForce": "100000", "complyJointDriveSpring": "20", "jointDriveSpring": "25", "jointDriveDamper": "0.5", "jointDriveMaxForce": "5", "jointDriveXTarget": "20.40795", "jointDriveYTarget": "0", "jointDriveZTarget": "0", "positionState": "On", "rotationState": "On", "positionGridMode": "None", "rotationGridMode": "None", "localPosition": {"x": "0.2241874", "y": "0.8653455", "z": "0.1926401"}, "localRotation": {"x": "341.1292", "y": "284.4922", "z": "293.5687"}}, {"id": "lElbowControl", "detachControl": "false", "interactableInPlayMode": "false", "deactivateOtherControlsOnPossess": "true", "possessable": "false", "canGrabPosition": "true", "canGrabRotation": "true", "freezeAtomPhysicsWhenGrabbed": "false", "xPositionLock": "false", "yPositionLock": "false", "zPositionLock": "false", "xPositionLocalLock": "false", "yPositionLocalLock": "false", "zPositionLocalLock": "false", "xRotationLock": "false", "yRotationLock": "false", "zRotationLock": "false", "physicsEnabled": "true", "useGravity": "true", "maxVelocityEnable": "true", "complyPositionThreshold": "0.001", "complyRotationThreshold": "5", "complySpeed": "10", "positionGrid": "0.1", "rotationGrid": "15", "mass": "0.3", "drag": "0.1", "maxVelocity": "10", "angularDrag": "10", "holdPositionSpring": "2000", "holdPositionDamper": "35", "holdPositionMaxForce": "1000", "holdRotationSpring": "100", "holdRotationDamper": "5", "holdRotationMaxForce": "1000", "complyPositionSpring": "1500", "complyPositionDamper": "100", "complyRotationSpring": "150", "complyRotationDamper": "10", "linkPositionSpring": "100000", "linkPositionDamper": "250", "linkPositionMaxForce": "100000", "linkRotationSpring": "100000", "linkRotationDamper": "250", "linkRotationMaxForce": "100000", "complyJointDriveSpring": "20", "jointDriveSpring": "25", "jointDriveDamper": "0.5", "jointDriveMaxForce": "5", "jointDriveXTarget": "-20.40796", "jointDriveYTarget": "0", "jointDriveZTarget": "0", "positionState": "On", "rotationState": "On", "positionGridMode": "None", "rotationGridMode": "None", "localPosition": {"x": "-0.16005", "y": "0.8623499", "z": "-0.05789614"}, "localRotation": {"x": "314.8888", "y": "58.69639", "z": "23.40563"}}, {"id": "rKneeControl", "detachControl": "false", "interactableInPlayMode": "true", "deactivateOtherControlsOnPossess": "true", "possessable": "false", "canGrabPosition": "true", "canGrabRotation": "true", "freezeAtomPhysicsWhenGrabbed": "false", "xPositionLock": "false", "yPositionLock": "false", "zPositionLock": "false", "xPositionLocalLock": "false", "yPositionLocalLock": "false", "zPositionLocalLock": "false", "xRotationLock": "false", "yRotationLock": "false", "zRotationLock": "false", "physicsEnabled": "true", "useGravity": "true", "maxVelocityEnable": "true", "complyPositionThreshold": "0.001", "complyRotationThreshold": "5", "complySpeed": "10", "positionGrid": "0.1", "rotationGrid": "15", "mass": "1", "drag": "1", "maxVelocity": "10", "angularDrag": "10", "holdPositionSpring": "2000", "holdPositionDamper": "35", "holdPositionMaxForce": "1000", "holdRotationSpring": "100", "holdRotationDamper": "5", "holdRotationMaxForce": "1000", "complyPositionSpring": "1500", "complyPositionDamper": "100", "complyRotationSpring": "150", "complyRotationDamper": "10", "linkPositionSpring": "100000", "linkPositionDamper": "250", "linkPositionMaxForce": "100000", "linkRotationSpring": "100000", "linkRotationDamper": "250", "linkRotationMaxForce": "100000", "complyJointDriveSpring": "20", "jointDriveSpring": "20", "jointDriveDamper": "0.5", "jointDriveMaxForce": "5", "jointDriveXTarget": "-22.61987", "jointDriveYTarget": "0", "jointDriveZTarget": "0", "positionState": "On", "rotationState": "On", "positionGridMode": "None", "rotationGridMode": "None", "localPosition": {"x": "0.1184186", "y": "0.5374684", "z": "0.263839"}, "localRotation": {"x": "63.61478", "y": "45.74848", "z": "92.4756"}}, {"id": "lKneeControl", "detachControl": "false", "interactableInPlayMode": "true", "deactivateOtherControlsOnPossess": "true", "possessable": "false", "canGrabPosition": "true", "canGrabRotation": "true", "freezeAtomPhysicsWhenGrabbed": "false", "xPositionLock": "false", "yPositionLock": "false", "zPositionLock": "false", "xPositionLocalLock": "false", "yPositionLocalLock": "false", "zPositionLocalLock": "false", "xRotationLock": "false", "yRotationLock": "false", "zRotationLock": "false", "physicsEnabled": "true", "useGravity": "true", "maxVelocityEnable": "true", "complyPositionThreshold": "0.001", "complyRotationThreshold": "5", "complySpeed": "10", "positionGrid": "0.1", "rotationGrid": "15", "mass": "1", "drag": "0.1", "maxVelocity": "10", "angularDrag": "10", "holdPositionSpring": "2000", "holdPositionDamper": "35", "holdPositionMaxForce": "1000", "holdRotationSpring": "100", "holdRotationDamper": "5", "holdRotationMaxForce": "1000", "complyPositionSpring": "1500", "complyPositionDamper": "100", "complyRotationSpring": "150", "complyRotationDamper": "10", "linkPositionSpring": "100000", "linkPositionDamper": "250", "linkPositionMaxForce": "100000", "linkRotationSpring": "100000", "linkRotationDamper": "250", "linkRotationMaxForce": "100000", "complyJointDriveSpring": "20", "jointDriveSpring": "20", "jointDriveDamper": "0.5", "jointDriveMaxForce": "5", "jointDriveXTarget": "-22.61987", "jointDriveYTarget": "0", "jointDriveZTarget": "0", "positionState": "On", "rotationState": "On", "positionGridMode": "None", "rotationGridMode": "None", "localPosition": {"x": "0.06757321", "y": "0.5939444", "z": "0.2537286"}, "localRotation": {"x": "47.65623", "y": "341.9526", "z": "8.390676"}}, {"id": "rToeControl", "detachControl": "false", "interactableInPlayMode": "false", "deactivateOtherControlsOnPossess": "true", "possessable": "false", "canGrabPosition": "true", "canGrabRotation": "true", "freezeAtomPhysicsWhenGrabbed": "false", "xPositionLock": "false", "yPositionLock": "false", "zPositionLock": "false", "xPositionLocalLock": "false", "yPositionLocalLock": "false", "zPositionLocalLock": "false", "xRotationLock": "false", "yRotationLock": "false", "zRotationLock": "false", "physicsEnabled": "true", "useGravity": "true", "maxVelocityEnable": "true", "complyPositionThreshold": "0.001", "complyRotationThreshold": "5", "complySpeed": "10", "positionGrid": "0.1", "rotationGrid": "15", "mass": "0.1", "drag": "0", "maxVelocity": "10", "angularDrag": "0.05", "holdPositionSpring": "2000", "holdPositionDamper": "35", "holdPositionMaxForce": "1000", "holdRotationSpring": "100", "holdRotationDamper": "5", "holdRotationMaxForce": "1000", "complyPositionSpring": "1500", "complyPositionDamper": "100", "complyRotationSpring": "150", "complyRotationDamper": "10", "linkPositionSpring": "100000", "linkPositionDamper": "250", "linkPositionMaxForce": "100000", "linkRotationSpring": "100000", "linkRotationDamper": "250", "linkRotationMaxForce": "100000", "complyJointDriveSpring": "20", "jointDriveSpring": "10", "jointDriveDamper": "0.5", "jointDriveMaxForce": "1", "jointDriveXTarget": "0", "jointDriveYTarget": "0", "jointDriveZTarget": "0", "positionState": "Off", "rotationState": "Off", "positionGridMode": "None", "rotationGridMode": "None", "localPosition": {"x": "0.1845604", "y": "0.007574975", "z": "0.5299672"}, "localRotation": {"x": "14.05091", "y": "0.6639181", "z": "5.664029"}}, {"id": "lToeControl", "detachControl": "false", "interactableInPlayMode": "false", "deactivateOtherControlsOnPossess": "true", "possessable": "false", "canGrabPosition": "true", "canGrabRotation": "true", "freezeAtomPhysicsWhenGrabbed": "false", "xPositionLock": "false", "yPositionLock": "false", "zPositionLock": "false", "xPositionLocalLock": "false", "yPositionLocalLock": "false", "zPositionLocalLock": "false", "xRotationLock": "false", "yRotationLock": "false", "zRotationLock": "false", "physicsEnabled": "true", "useGravity": "true", "maxVelocityEnable": "true", "complyPositionThreshold": "0.001", "complyRotationThreshold": "5", "complySpeed": "10", "positionGrid": "0.1", "rotationGrid": "15", "mass": "0.1", "drag": "0", "maxVelocity": "10", "angularDrag": "0.05", "holdPositionSpring": "2000", "holdPositionDamper": "35", "holdPositionMaxForce": "1000", "holdRotationSpring": "100", "holdRotationDamper": "5", "holdRotationMaxForce": "1000", "complyPositionSpring": "1500", "complyPositionDamper": "100", "complyRotationSpring": "150", "complyRotationDamper": "10", "linkPositionSpring": "100000", "linkPositionDamper": "250", "linkPositionMaxForce": "100000", "linkRotationSpring": "100000", "linkRotationDamper": "250", "linkRotationMaxForce": "100000", "complyJointDriveSpring": "20", "jointDriveSpring": "10", "jointDriveDamper": "0.5", "jointDriveMaxForce": "1", "jointDriveXTarget": "0", "jointDriveYTarget": "0", "jointDriveZTarget": "0", "positionState": "Off", "rotationState": "Off", "positionGridMode": "None", "rotationGridMode": "None", "localPosition": {"x": "-0.1070611", "y": "0.01981622", "z": "0.4598732"}, "localRotation": {"x": "19.13394", "y": "359.7926", "z": "355.99"}}, {"id": "abdomenControl", "detachControl": "false", "interactableInPlayMode": "false", "deactivateOtherControlsOnPossess": "true", "possessable": "false", "canGrabPosition": "true", "canGrabRotation": "true", "freezeAtomPhysicsWhenGrabbed": "false", "xPositionLock": "false", "yPositionLock": "false", "zPositionLock": "false", "xPositionLocalLock": "false", "yPositionLocalLock": "false", "zPositionLocalLock": "false", "xRotationLock": "false", "yRotationLock": "false", "zRotationLock": "false", "physicsEnabled": "true", "useGravity": "true", "maxVelocityEnable": "true", "complyPositionThreshold": "0.001", "complyRotationThreshold": "5", "complySpeed": "10", "positionGrid": "0.1", "rotationGrid": "15", "mass": "2", "drag": "1", "maxVelocity": "10", "angularDrag": "10", "holdPositionSpring": "2000", "holdPositionDamper": "35", "holdPositionMaxForce": "1000", "holdRotationSpring": "100", "holdRotationDamper": "5", "holdRotationMaxForce": "1000", "complyPositionSpring": "1500", "complyPositionDamper": "100", "complyRotationSpring": "150", "complyRotationDamper": "10", "linkPositionSpring": "100000", "linkPositionDamper": "250", "linkPositionMaxForce": "100000", "linkRotationSpring": "100000", "linkRotationDamper": "250", "linkRotationMaxForce": "100000", "complyJointDriveSpring": "20", "jointDriveSpring": "100", "jointDriveDamper": "1", "jointDriveMaxForce": "500", "jointDriveXTarget": "0", "jointDriveYTarget": "0", "jointDriveZTarget": "0", "positionState": "Off", "rotationState": "Off", "positionGridMode": "None", "rotationGridMode": "None", "localPosition": {"x": "0.00757665", "y": "0.6981519", "z": "-0.0585001"}, "localRotation": {"x": "14.98778", "y": "1.33596", "z": "355.0186"}}, {"id": "abdomen2Control", "detachControl": "false", "interactableInPlayMode": "false", "deactivateOtherControlsOnPossess": "true", "possessable": "false", "canGrabPosition": "true", "canGrabRotation": "true", "freezeAtomPhysicsWhenGrabbed": "false", "xPositionLock": "false", "yPositionLock": "false", "zPositionLock": "false", "xPositionLocalLock": "false", "yPositionLocalLock": "false", "zPositionLocalLock": "false", "xRotationLock": "false", "yRotationLock": "false", "zRotationLock": "false", "physicsEnabled": "true", "useGravity": "true", "maxVelocityEnable": "true", "complyPositionThreshold": "0.001", "complyRotationThreshold": "5", "complySpeed": "10", "positionGrid": "0.1", "rotationGrid": "15", "mass": "2", "drag": "1", "maxVelocity": "10", "angularDrag": "10", "holdPositionSpring": "2000", "holdPositionDamper": "35", "holdPositionMaxForce": "1000", "holdRotationSpring": "100", "holdRotationDamper": "5", "holdRotationMaxForce": "1000", "complyPositionSpring": "1500", "complyPositionDamper": "100", "complyRotationSpring": "150", "complyRotationDamper": "10", "linkPositionSpring": "100000", "linkPositionDamper": "250", "linkPositionMaxForce": "100000", "linkRotationSpring": "100000", "linkRotationDamper": "250", "linkRotationMaxForce": "100000", "complyJointDriveSpring": "20", "jointDriveSpring": "100", "jointDriveDamper": "1", "jointDriveMaxForce": "500", "jointDriveXTarget": "0", "jointDriveYTarget": "0", "jointDriveZTarget": "0", "positionState": "Off", "rotationState": "Off", "positionGridMode": "None", "rotationGridMode": "None", "localPosition": {"x": "0.01645285", "y": "0.7852553", "z": "-0.02537317"}, "localRotation": {"x": "3.528238", "y": "4.939254", "z": "353.0455"}}, {"id": "rThighControl", "detachControl": "false", "interactableInPlayMode": "false", "deactivateOtherControlsOnPossess": "true", "possessable": "false", "canGrabPosition": "true", "canGrabRotation": "true", "freezeAtomPhysicsWhenGrabbed": "false", "xPositionLock": "false", "yPositionLock": "false", "zPositionLock": "false", "xPositionLocalLock": "false", "yPositionLocalLock": "false", "zPositionLocalLock": "false", "xRotationLock": "false", "yRotationLock": "false", "zRotationLock": "false", "physicsEnabled": "true", "useGravity": "true", "maxVelocityEnable": "true", "complyPositionThreshold": "0.001", "complyRotationThreshold": "5", "complySpeed": "10", "positionGrid": "0.1", "rotationGrid": "15", "mass": "2", "drag": "1", "maxVelocity": "10", "angularDrag": "10", "holdPositionSpring": "2000", "holdPositionDamper": "35", "holdPositionMaxForce": "1000", "holdRotationSpring": "100", "holdRotationDamper": "5", "holdRotationMaxForce": "1000", "complyPositionSpring": "1500", "complyPositionDamper": "100", "complyRotationSpring": "150", "complyRotationDamper": "10", "linkPositionSpring": "100000", "linkPositionDamper": "250", "linkPositionMaxForce": "100000", "linkRotationSpring": "100000", "linkRotationDamper": "250", "linkRotationMaxForce": "100000", "complyJointDriveSpring": "20", "jointDriveSpring": "50", "jointDriveDamper": "0.5", "jointDriveMaxForce": "18", "jointDriveXTarget": "22.61987", "jointDriveYTarget": "0", "jointDriveZTarget": "0", "positionState": "On", "rotationState": "On", "positionGridMode": "None", "rotationGridMode": "None", "localPosition": {"x": "0.1200024", "y": "0.6195171", "z": "-0.04095662"}, "localRotation": {"x": "313.3475", "y": "16.37444", "z": "9.466985"}}, {"id": "lThighControl", "detachControl": "false", "interactableInPlayMode": "false", "deactivateOtherControlsOnPossess": "true", "possessable": "false", "canGrabPosition": "true", "canGrabRotation": "true", "freezeAtomPhysicsWhenGrabbed": "false", "xPositionLock": "false", "yPositionLock": "false", "zPositionLock": "false", "xPositionLocalLock": "false", "yPositionLocalLock": "false", "zPositionLocalLock": "false", "xRotationLock": "false", "yRotationLock": "false", "zRotationLock": "false", "physicsEnabled": "true", "useGravity": "true", "maxVelocityEnable": "true", "complyPositionThreshold": "0.001", "complyRotationThreshold": "5", "complySpeed": "10", "positionGrid": "0.1", "rotationGrid": "15", "mass": "2", "drag": "1", "maxVelocity": "10", "angularDrag": "10", "holdPositionSpring": "2000", "holdPositionDamper": "35", "holdPositionMaxForce": "1000", "holdRotationSpring": "100", "holdRotationDamper": "5", "holdRotationMaxForce": "1000", "complyPositionSpring": "1500", "complyPositionDamper": "100", "complyRotationSpring": "150", "complyRotationDamper": "10", "linkPositionSpring": "100000", "linkPositionDamper": "250", "linkPositionMaxForce": "100000", "linkRotationSpring": "100000", "linkRotationDamper": "250", "linkRotationMaxForce": "100000", "complyJointDriveSpring": "20", "jointDriveSpring": "50", "jointDriveDamper": "0.5", "jointDriveMaxForce": "18", "jointDriveXTarget": "22.61987", "jointDriveYTarget": "0", "jointDriveZTarget": "0", "positionState": "On", "rotationState": "On", "positionGridMode": "None", "rotationGridMode": "None", "localPosition": {"x": "-0.1207788", "y": "0.6087654", "z": "-0.01371384"}, "localRotation": {"x": "5.421251", "y": "264.4809", "z": "85.62291"}}, {"id": "rArmControl", "detachControl": "false", "interactableInPlayMode": "false", "deactivateOtherControlsOnPossess": "true", "possessable": "false", "canGrabPosition": "true", "canGrabRotation": "true", "freezeAtomPhysicsWhenGrabbed": "false", "xPositionLock": "false", "yPositionLock": "false", "zPositionLock": "false", "xPositionLocalLock": "false", "yPositionLocalLock": "false", "zPositionLocalLock": "false", "xRotationLock": "false", "yRotationLock": "false", "zRotationLock": "false", "physicsEnabled": "true", "useGravity": "true", "maxVelocityEnable": "true", "complyPositionThreshold": "0.001", "complyRotationThreshold": "5", "complySpeed": "10", "positionGrid": "0.1", "rotationGrid": "15", "mass": "0.6", "drag": "0.1", "maxVelocity": "10", "angularDrag": "10", "holdPositionSpring": "2000", "holdPositionDamper": "35", "holdPositionMaxForce": "1000", "holdRotationSpring": "100", "holdRotationDamper": "5", "holdRotationMaxForce": "1000", "complyPositionSpring": "1500", "complyPositionDamper": "100", "complyRotationSpring": "150", "complyRotationDamper": "10", "linkPositionSpring": "100000", "linkPositionDamper": "250", "linkPositionMaxForce": "100000", "linkRotationSpring": "100000", "linkRotationDamper": "250", "linkRotationMaxForce": "100000", "complyJointDriveSpring": "20", "jointDriveSpring": "40", "jointDriveDamper": "2", "jointDriveMaxForce": "50", "jointDriveXTarget": "53.1301", "jointDriveYTarget": "0", "jointDriveZTarget": "0", "positionState": "On", "rotationState": "On", "positionGridMode": "None", "rotationGridMode": "None", "localPosition": {"x": "0.1740375", "y": "1.095224", "z": "0.02197742"}, "localRotation": {"x": "358.1726", "y": "344.7717", "z": "274.6276"}}, {"id": "lArmControl", "detachControl": "false", "interactableInPlayMode": "false", "deactivateOtherControlsOnPossess": "true", "possessable": "false", "canGrabPosition": "true", "canGrabRotation": "true", "freezeAtomPhysicsWhenGrabbed": "false", "xPositionLock": "false", "yPositionLock": "false", "zPositionLock": "false", "xPositionLocalLock": "false", "yPositionLocalLock": "false", "zPositionLocalLock": "false", "xRotationLock": "false", "yRotationLock": "false", "zRotationLock": "false", "physicsEnabled": "true", "useGravity": "true", "maxVelocityEnable": "true", "complyPositionThreshold": "0.001", "complyRotationThreshold": "5", "complySpeed": "10", "positionGrid": "0.1", "rotationGrid": "15", "mass": "0.6", "drag": "0.1", "maxVelocity": "10", "angularDrag": "10", "holdPositionSpring": "2000", "holdPositionDamper": "35", "holdPositionMaxForce": "1000", "holdRotationSpring": "100", "holdRotationDamper": "5", "holdRotationMaxForce": "1000", "complyPositionSpring": "1500", "complyPositionDamper": "100", "complyRotationSpring": "150", "complyRotationDamper": "10", "linkPositionSpring": "100000", "linkPositionDamper": "250", "linkPositionMaxForce": "100000", "linkRotationSpring": "100000", "linkRotationDamper": "250", "linkRotationMaxForce": "100000", "complyJointDriveSpring": "20", "jointDriveSpring": "40", "jointDriveDamper": "2", "jointDriveMaxForce": "50", "jointDriveXTarget": "-53.1301", "jointDriveYTarget": "0", "jointDriveZTarget": "0", "positionState": "On", "rotationState": "On", "positionGridMode": "None", "rotationGridMode": "None", "localPosition": {"x": "-0.1152418", "y": "1.135315", "z": "0.1503798"}, "localRotation": {"x": "358.0238", "y": "267.3845", "z": "77.19138"}}, {"id": "rShoulderControl", "detachControl": "false", "interactableInPlayMode": "false", "deactivateOtherControlsOnPossess": "true", "possessable": "false", "canGrabPosition": "true", "canGrabRotation": "true", "freezeAtomPhysicsWhenGrabbed": "false", "xPositionLock": "false", "yPositionLock": "false", "zPositionLock": "false", "xPositionLocalLock": "false", "yPositionLocalLock": "false", "zPositionLocalLock": "false", "xRotationLock": "false", "yRotationLock": "false", "zRotationLock": "false", "physicsEnabled": "true", "useGravity": "false", "maxVelocityEnable": "true", "complyPositionThreshold": "0.001", "complyRotationThreshold": "5", "complySpeed": "10", "positionGrid": "0.1", "rotationGrid": "15", "mass": "0.3", "drag": "1", "maxVelocity": "10", "angularDrag": "1", "holdPositionSpring": "2000", "holdPositionDamper": "35", "holdPositionMaxForce": "1000", "holdRotationSpring": "100", "holdRotationDamper": "5", "holdRotationMaxForce": "1000", "complyPositionSpring": "1500", "complyPositionDamper": "100", "complyRotationSpring": "150", "complyRotationDamper": "10", "linkPositionSpring": "100000", "linkPositionDamper": "250", "linkPositionMaxForce": "100000", "linkRotationSpring": "100000", "linkRotationDamper": "250", "linkRotationMaxForce": "100000", "complyJointDriveSpring": "20", "jointDriveSpring": "200", "jointDriveDamper": "10", "jointDriveMaxForce": "200", "jointDriveXTarget": "-7.988342", "jointDriveYTarget": "5.752882", "jointDriveZTarget": "-0.4020386", "positionState": "Off", "rotationState": "Off", "positionGridMode": "None", "rotationGridMode": "None", "localPosition": {"x": "0.06003446", "y": "1.06079", "z": "0.07094461"}, "localRotation": {"x": "3.979103", "y": "17.47511", "z": "29.05768"}}, {"id": "lShoulderControl", "detachControl": "false", "interactableInPlayMode": "false", "deactivateOtherControlsOnPossess": "true", "possessable": "false", "canGrabPosition": "true", "canGrabRotation": "true", "freezeAtomPhysicsWhenGrabbed": "false", "xPositionLock": "false", "yPositionLock": "false", "zPositionLock": "false", "xPositionLocalLock": "false", "yPositionLocalLock": "false", "zPositionLocalLock": "false", "xRotationLock": "false", "yRotationLock": "false", "zRotationLock": "false", "physicsEnabled": "true", "useGravity": "false", "maxVelocityEnable": "true", "complyPositionThreshold": "0.001", "complyRotationThreshold": "5", "complySpeed": "10", "positionGrid": "0.1", "rotationGrid": "15", "mass": "0.3", "drag": "1", "maxVelocity": "10", "angularDrag": "1", "holdPositionSpring": "2000", "holdPositionDamper": "35", "holdPositionMaxForce": "1000", "holdRotationSpring": "100", "holdRotationDamper": "5", "holdRotationMaxForce": "1000", "complyPositionSpring": "1500", "complyPositionDamper": "100", "complyRotationSpring": "150", "complyRotationDamper": "10", "linkPositionSpring": "100000", "linkPositionDamper": "250", "linkPositionMaxForce": "100000", "linkRotationSpring": "100000", "linkRotationDamper": "250", "linkRotationMaxForce": "100000", "complyJointDriveSpring": "20", "jointDriveSpring": "200", "jointDriveDamper": "10", "jointDriveMaxForce": "200", "jointDriveXTarget": "7.988342", "jointDriveYTarget": "-5.752869", "jointDriveZTarget": "-0.4020386", "positionState": "Off", "rotationState": "Off", "positionGridMode": "None", "rotationGridMode": "None", "localPosition": {"x": "0.03086848", "y": "1.060216", "z": "0.07612813"}, "localRotation": {"x": "22.73018", "y": "354.9507", "z": "340.1364"}}, {"id": "hip", "relativeRootPosition": {"x": "-0.007588439", "y": "0.01204914", "z": "-0.06840461"}, "relativeRootRotation": {"x": "1.330561", "y": "2.472985", "z": "356.7267"}, "rootPosition": {"x": "-0.00397016", "y": "0.654957", "z": "0.1017173"}, "rootRotation": {"x": "10.34958", "y": "19.54997", "z": "3.517781"}}, {"id": "pelvis", "position": {"x": "-3.256334E-05", "y": "0.01772952", "z": "0.01744981"}, "rotation": {"x": "2.401423", "y": "4.718399", "z": "358.4288"}}, {"id": "rThigh", "position": {"x": "0.08127234", "y": "-0.09260517", "z": "-0.01418315"}, "rotation": {"x": "276.5004", "y": "295.1487", "z": "67.31402"}}, {"id": "rShin", "position": {"x": "-0.02247974", "y": "-0.4287004", "z": "0.007924348"}, "rotation": {"x": "64.02194", "y": "333.4371", "z": "335.3091"}}, {"id": "rFoot", "position": {"x": "-0.008603739", "y": "-0.4792858", "z": "-0.001503199"}, "rotation": {"x": "31.3978", "y": "4.96549", "z": "2.315834"}}, {"id": "rToe", "position": {"x": "0.001302887", "y": "-0.004929818", "z": "0.1237065"}, "rotation": {"x": "350.482", "y": "1.104053", "z": "353.2007"}}, {"id": "lThigh", "position": {"x": "-0.08122852", "y": "-0.09260631", "z": "-0.01419871"}, "rotation": {"x": "284.7385", "y": "92.708", "z": "271.7086"}}, {"id": "l<PERSON>hin", "position": {"x": "0.02253109", "y": "-0.4286591", "z": "0.008171052"}, "rotation": {"x": "80.40839", "y": "348.4656", "z": "345.0511"}}, {"id": "lFoot", "position": {"x": "0.008796768", "y": "-0.4792611", "z": "-0.001733571"}, "rotation": {"x": "21.71245", "y": "357.7101", "z": "11.22481"}}, {"id": "lToe", "position": {"x": "-0.001935229", "y": "-0.005446512", "z": "0.1229102"}, "rotation": {"x": "355.7409", "y": "359.5195", "z": "0.6346646"}}, {"id": "LGlute", "position": {"x": "-0.05995701", "y": "-0.09979913", "z": "-6.117905E-05"}, "rotation": {"x": "9.978654", "y": "0.003389007", "z": "0.01870017"}}, {"id": "RGlute", "position": {"x": "0.0600417", "y": "-0.09980002", "z": "-6.063236E-05"}, "rotation": {"x": "9.980069", "y": "-0.0007132116", "z": "359.9852"}}, {"id": "abdomen", "position": {"x": "-7.003937E-06", "y": "-0.01064956", "z": "-0.01450657"}, "rotation": {"x": "4.806762", "y": "2.370438", "z": "350.9966"}}, {"id": "abdomen2", "position": {"x": "1.64723E-05", "y": "0.09311068", "z": "0.009665013"}, "rotation": {"x": "348.2952", "y": "2.650874", "z": "357.1721"}}, {"id": "chest", "position": {"x": "-2.486119E-05", "y": "0.1273981", "z": "0.008441238"}, "rotation": {"x": "14.38888", "y": "7.212209", "z": "7.881774"}}, {"id": "lPectoral", "position": {"x": "-0.02922992", "y": "0.04564729", "z": "-0.03529286"}, "rotation": {"x": "353.7014", "y": "339.3754", "z": "359.9516"}}, {"id": "rPectoral", "position": {"x": "0.02924543", "y": "0.04563757", "z": "-0.03540081"}, "rotation": {"x": "353.6997", "y": "20.35596", "z": "0.09249011"}}, {"id": "r<PERSON><PERSON><PERSON>", "position": {"x": "0.0148408", "y": "0.1689067", "z": "0.03256541"}, "rotation": {"x": "345.5903", "y": "7.540309", "z": "25.62431"}}, {"id": "rShldr", "position": {"x": "0.1226289", "y": "-0.01266615", "z": "-0.01046021"}, "rotation": {"x": "324.1894", "y": "349.5688", "z": "262.9901"}}, {"id": "rForeArm", "position": {"x": "0.2927327", "y": "-0.0006569624", "z": "-0.002335444"}, "rotation": {"x": "0.369751", "y": "208.0073", "z": "1.324488"}}, {"id": "rHand", "position": {"x": "0.2192069", "y": "0.001130664", "z": "0.005795151"}, "rotation": {"x": "3.146549", "y": "356.3962", "z": "333.1025"}}, {"id": "l<PERSON><PERSON><PERSON>", "position": {"x": "-0.01478704", "y": "0.1688647", "z": "0.03257769"}, "rotation": {"x": "4.40523", "y": "345.6208", "z": "344.0031"}}, {"id": "lShldr", "position": {"x": "-0.1225925", "y": "-0.01268142", "z": "-0.01050665"}, "rotation": {"x": "1.835099", "y": "6.974821", "z": "93.09"}}, {"id": "lForeArm", "position": {"x": "-0.2926085", "y": "-0.0004937053", "z": "-0.001937553"}, "rotation": {"x": "18.67675", "y": "111.611", "z": "3.780573"}}, {"id": "lHand", "position": {"x": "-0.2191146", "y": "0.003106124", "z": "0.007947594"}, "rotation": {"x": "344.1549", "y": "10.2073", "z": "355.5128"}}, {"id": "neck", "position": {"x": "8.90661E-05", "y": "0.2592498", "z": "-0.01088957"}, "rotation": {"x": "345.0782", "y": "20.18377", "z": "352.1553"}}, {"id": "head", "position": {"x": "-6.427546E-05", "y": "0.084621", "z": "0.0009167064"}, "rotation": {"x": "342.9426", "y": "1.057993", "z": "358.6682"}}, {"id": "geometry", "morphs": [{"uid": "Left Fingers Fist", "name": "Left Fingers Fist", "value": "0"}, {"uid": "Left Fingers Grasp", "name": "Left Fingers Grasp", "value": "-0.3572396"}, {"uid": "Left Fingers In-Out", "name": "Left Fingers In-Out", "value": "-0.9731632"}, {"uid": "Left Index Finger Bend", "name": "Left Index Finger Bend", "value": "0.4904931"}, {"uid": "Left Mid Finger Bend", "name": "Left Mid Finger Bend", "value": "0.5565411"}, {"uid": "Left <PERSON>y Finger Bend", "name": "Left <PERSON>y Finger Bend", "value": "0.4046202"}, {"uid": "Left Ring Finger Bend", "name": "Left Ring Finger Bend", "value": "0.5587281"}, {"uid": "Left Thumb Bend", "name": "Left Thumb Bend", "value": "0.3004068"}, {"uid": "Left Thumb Fist", "name": "Left Thumb Fist", "value": "0"}, {"uid": "Left Thumb Grasp", "name": "Left Thumb Grasp", "value": "-0.3572396"}, {"uid": "Left Thumb In-Out", "name": "Left Thumb In-Out", "value": "-0.9731632"}, {"uid": "Left Hand Grasp", "name": "Left Hand Grasp", "value": "-0.3572396"}, {"uid": "Left Hand Spread", "name": "Left Hand Spread", "value": "-0.9731632"}, {"uid": "Right Fingers Fist", "name": "Right Fingers Fist", "value": "0"}, {"uid": "Right Fingers Grasp", "name": "Right Fingers Grasp", "value": "0.3393861"}, {"uid": "Right Fingers In-Out", "name": "Right Fingers In-Out", "value": "0.2227995"}, {"uid": "Right Fingers Squeeze", "name": "Right Fingers Squeeze", "value": "0.4618582"}, {"uid": "Right Fingers Straighten", "name": "Right Fingers Straighten", "value": "0.8890204"}, {"uid": "Right Index Finger Bend", "name": "Right Index Finger Bend", "value": "-0.2333484"}, {"uid": "Right Mid Finger Bend", "name": "Right Mid Finger Bend", "value": "-0.8015802"}, {"uid": "Right <PERSON><PERSON>", "name": "Right <PERSON><PERSON>", "value": "0.3332447"}, {"uid": "Right Ring Finger Bend", "name": "Right Ring Finger Bend", "value": "-0.4445412"}, {"uid": "Right Thumb Fist", "name": "Right Thumb Fist", "value": "0"}, {"uid": "Right Thumb <PERSON>", "name": "Right Thumb <PERSON>", "value": "0.3393861"}, {"uid": "Right Thumb In-Out", "name": "Right Thumb In-Out", "value": "0.2227995"}, {"uid": "Right Thumb <PERSON>en", "name": "Right Thumb <PERSON>en", "value": "0.8890204"}, {"uid": "Right Hand Chop", "name": "Right Hand Chop", "value": "0.4618582"}, {"uid": "Right Hand Grasp", "name": "Right Hand Grasp", "value": "0.3393861"}, {"uid": "Right Hand Spread", "name": "Right Hand Spread", "value": "0.2227995"}, {"uid": "Right Hand Straighten", "name": "Right Hand Straighten", "value": "0.8890204"}, {"uid": "AshAuryn.Pose_Tools.22:/Custom/Atom/Person/Morphs/female/AshAuryn/Pose Tools/Hands/right pew pew 2-278d5eee.vmi", "name": "right pew pew", "value": "1"}, {"uid": "AshAuryn.Pose_Tools.22:/Custom/Atom/Person/Morphs/female/AshAuryn/Pose Tools/Hands/right point-9598eb19.vmi", "name": "right point", "value": "0.2228781"}, {"uid": "Brow Inner Up Left", "name": "Brow Inner Up Left", "value": "0.1101307"}, {"uid": "Brow Inner Up Right", "name": "Brow Inner Up Right", "value": "0.1101307"}, {"uid": "Brow Outer Down", "name": "Brow Outer Down", "value": "0.2476496"}, {"uid": "Brow Outer Down Left", "name": "Brow Outer Down Left", "value": "0.08880661"}, {"uid": "Brow Outer Down Right", "name": "Brow Outer Down Right", "value": "0.08880661"}, {"uid": "Brow Up Left", "name": "Brow Up Left", "value": "0.1027476"}, {"uid": "Brow Up Right", "name": "Brow Up Right", "value": "0.1027476"}, {"uid": "AshAuryn.Expressions.5:/Custom/Atom/Person/Morphs/female/AshAuryn/Expressions/AshAuryn_worry2.vmi", "name": "AA - Worry 2", "value": "0.1287532"}, {"uid": "Shock", "name": "Shock", "value": "0.07682416"}, {"uid": "Smile Full Face", "name": "Smile Full Face", "value": "0.1006364"}, {"uid": "Smile Open Full Face", "name": "Smile Open Full Face", "value": "0.04762896"}, {"uid": "Eyelids Bottom Up Left", "name": "Eyelids Bottom Up Left", "value": "0.2797846"}, {"uid": "Eyelids Bottom Up Right", "name": "Eyelids Bottom Up Right", "value": "0.2794822"}, {"uid": "Eyelids Top Up Left", "name": "Eyelids Top Up Left", "value": "0.8393537"}, {"uid": "Eyelids Top Up Right", "name": "Eyelids Top Up Right", "value": "0.8384467"}, {"uid": "Mouth Open Wide 2", "name": "Mouth Open Wide 2", "value": "0.043231"}, {"uid": "OW", "name": "OW", "value": "0.2776563"}]}]}