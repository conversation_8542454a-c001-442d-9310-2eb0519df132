{"Rules": [{"Conditions": [{"$type": "DD.ProfileManager.Conditions.VitalsCondition, DD", "operator": "大于", "vitalType": "能量护盾", "threshold": 2000, "component": null}], "delayBetweenRuns": 1.0, "Enabled": true, "Name": "0", "SkillName": "烈焰之牆", "Key": 0, "IsHold": true, "IsEvade": true, "EvadeRange": 30, "HoldTime": 1.0, "IsUserCooldown": true, "IsWalkable": false, "IsMove": false, "Rarity": [0, 1, 2, 3]}, {"Conditions": [], "delayBetweenRuns": 10.0, "Enabled": true, "Name": "1", "SkillName": "烈炎風暴", "Key": 0, "IsHold": false, "IsEvade": true, "EvadeRange": 30, "HoldTime": 1.0, "IsUserCooldown": true, "IsWalkable": false, "IsMove": false, "Rarity": [0, 1, 2, 3]}, {"Conditions": [], "delayBetweenRuns": 0.0, "Enabled": true, "Name": "2", "SkillName": "劇痛獻祭", "Key": 0, "IsHold": false, "IsEvade": true, "EvadeRange": 30, "HoldTime": 1.0, "IsUserCooldown": true, "IsWalkable": false, "IsMove": false, "Rarity": [3, 2]}, {"Conditions": [], "delayBetweenRuns": 7.0, "Enabled": true, "Name": "3", "SkillName": "脆弱", "Key": 0, "IsHold": false, "IsEvade": true, "EvadeRange": 30, "HoldTime": 1.0, "IsUserCooldown": true, "IsWalkable": false, "IsMove": false, "Rarity": [2, 3]}]}