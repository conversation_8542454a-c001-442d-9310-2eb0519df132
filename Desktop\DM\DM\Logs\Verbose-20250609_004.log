2025-06-09 21:42:16.873 +08:00 [INF] 模块: Game State -> : Time: 36.9699 ms. 地址:[938231] Started searching offset with:0
2025-06-09 21:42:16.933 +08:00 [INF] 模块: Atlas Helper -> : Time: 37.1996 ms. 地址:[941648] Started searching offset with:0
2025-06-09 21:42:16.933 +08:00 [INF] 模块: Area change -> : Time: 37.9577 ms. 地址:[1041769] Started searching offset with:0
2025-06-09 21:42:16.933 +08:00 [INF] 模块: BlackBarSize -> : Time: 161.1485 ms. 地址:[4531143] Started searching offset with:0
2025-06-09 21:42:16.933 +08:00 [INF] 模块: Terrain Rotation Selector -> : Time: 919.835 ms. 地址:[25416559] Started searching offset with:0
2025-06-09 21:42:16.933 +08:00 [INF] 模块: Terrain Rotator Helper -> : Time: 946.6705 ms. 地址:[25416559] Started searching offset with:0
2025-06-09 21:42:16.933 +08:00 [INF] 模块: File Root -> : Time: 1172.5431 ms. 地址:[29867444] Started searching offset with:0
2025-06-09 21:42:16.933 +08:00 [INF] 模块: DiagnosticInfoType -> : Time: 2467.3953 ms. 地址:[0] Started searching offset with:600000
2025-06-09 21:42:16.933 +08:00 [INF] 初始化用时 6942.7952 毫秒.
2025-06-09 21:42:16.933 +08:00 [INF] Resize from: {X=0,Y=0,Width=1584,Height=861} to {X=560,Y=227,Width=800,Height=600}
2025-06-09 21:42:17.010 +08:00 [INF] DD loaded in 116.1375 ms.
2025-06-09 21:42:17.041 +08:00 [ERR] 线程1启动
2025-06-09 21:42:17.041 +08:00 [ERR] 线程2启动
2025-06-09 21:42:17.041 +08:00 [INF] 监测线程启动
2025-06-09 21:42:17.041 +08:00 [INF] 子目录列表：
2025-06-09 21:42:17.041 +08:00 [INF] 冰之打击武僧
2025-06-09 21:42:17.041 +08:00 [INF] 召唤
2025-06-09 21:42:17.041 +08:00 [INF] 召唤弓箭手
2025-06-09 21:42:17.072 +08:00 [INF] 囚神僧作者自己用的BD
2025-06-09 21:42:17.103 +08:00 [INF] 囚神杖武僧
2025-06-09 21:42:17.103 +08:00 [INF] 恶魔受伤释放
2025-06-09 21:42:17.103 +08:00 [INF] 恶魔受伤释放双祈愿
2025-06-09 21:42:17.103 +08:00 [INF] 游侠
2025-06-09 21:42:17.103 +08:00 [INF] 电武僧
2025-06-09 21:42:17.103 +08:00 [INF] 电球
2025-06-09 21:42:17.103 +08:00 [INF] 自己用
2025-06-09 21:42:17.103 +08:00 [INF] 闪电
2025-06-09 21:42:17.103 +08:00 [INF] 闪电箭
2025-06-09 21:42:17.103 +08:00 [INF] 阴抓BD模板
2025-06-09 21:42:17.103 +08:00 [INF] 陰爪BD先取消走A
2025-06-09 21:42:17.103 +08:00 [INF] 骑鸟电矛
2025-06-09 21:42:17.103 +08:00 [INF] DD -> 初始化用时: 69.4907 毫秒.
2025-06-09 21:42:17.107 +08:00 [INF] 加载地图数据耗时：1
2025-06-09 21:42:17.134 +08:00 [INF] 当前地图： (0)  N：-1 T：0
2025-06-09 21:42:17.768 +08:00 [INF] Resize from: {X=560,Y=227,Width=800,Height=600} to {X=742,Y=196,Width=800,Height=600}
2025-06-09 21:42:18.108 +08:00 [INF] Resize from: {X=742,Y=196,Width=800,Height=600} to {X=833,Y=173,Width=800,Height=600}
2025-06-09 21:42:18.431 +08:00 [INF] Resize from: {X=833,Y=173,Width=800,Height=600} to {X=835,Y=174,Width=800,Height=600}
2025-06-09 21:42:39.167 +08:00 [INF] Resize from: {X=835,Y=174,Width=800,Height=600} to {X=0,Y=23,Width=1920,Height=1009}
2025-06-09 21:42:44.969 +08:00 [INF] Resize from: {X=0,Y=23,Width=1920,Height=1009} to {X=835,Y=174,Width=800,Height=600}
2025-06-09 21:43:40.455 +08:00 [INF] 模块: Game State -> : Time: 34.7529 ms. 地址:[938231] Started searching offset with:0
2025-06-09 21:43:40.498 +08:00 [INF] 模块: Atlas Helper -> : Time: 34.1056 ms. 地址:[941648] Started searching offset with:0
2025-06-09 21:43:40.498 +08:00 [INF] 模块: Area change -> : Time: 35.978 ms. 地址:[1041769] Started searching offset with:0
2025-06-09 21:43:40.498 +08:00 [INF] 模块: BlackBarSize -> : Time: 153.4247 ms. 地址:[4531143] Started searching offset with:0
2025-06-09 21:43:40.498 +08:00 [INF] 模块: Terrain Rotation Selector -> : Time: 852.601 ms. 地址:[25416559] Started searching offset with:0
2025-06-09 21:43:40.498 +08:00 [INF] 模块: Terrain Rotator Helper -> : Time: 855.8199 ms. 地址:[25416559] Started searching offset with:0
2025-06-09 21:43:40.498 +08:00 [INF] 模块: File Root -> : Time: 1101.1717 ms. 地址:[29867444] Started searching offset with:0
2025-06-09 21:43:40.499 +08:00 [INF] 模块: DiagnosticInfoType -> : Time: 2293.3673 ms. 地址:[0] Started searching offset with:600000
2025-06-09 21:43:40.499 +08:00 [INF] 初始化用时 6426.3386 毫秒.
2025-06-09 21:43:40.499 +08:00 [INF] Resize from: {X=0,Y=0,Width=1584,Height=861} to {X=1009,Y=557,Width=177,Height=124}
2025-06-09 21:43:40.592 +08:00 [INF] DD loaded in 110.1257 ms.
2025-06-09 21:43:40.623 +08:00 [ERR] 线程1启动
2025-06-09 21:43:40.623 +08:00 [ERR] 线程2启动
2025-06-09 21:43:40.623 +08:00 [INF] 监测线程启动
2025-06-09 21:43:40.623 +08:00 [INF] 子目录列表：
2025-06-09 21:43:40.623 +08:00 [INF] 冰之打击武僧
2025-06-09 21:43:40.623 +08:00 [INF] 召唤
2025-06-09 21:43:40.623 +08:00 [INF] 召唤弓箭手
2025-06-09 21:43:40.669 +08:00 [INF] 囚神僧作者自己用的BD
2025-06-09 21:43:40.669 +08:00 [INF] 囚神杖武僧
2025-06-09 21:43:40.669 +08:00 [INF] 恶魔受伤释放
2025-06-09 21:43:40.669 +08:00 [INF] 恶魔受伤释放双祈愿
2025-06-09 21:43:40.669 +08:00 [INF] 游侠
2025-06-09 21:43:40.669 +08:00 [INF] 电武僧
2025-06-09 21:43:40.669 +08:00 [INF] 电球
2025-06-09 21:43:40.669 +08:00 [INF] 自己用
2025-06-09 21:43:40.669 +08:00 [INF] 闪电
2025-06-09 21:43:40.669 +08:00 [INF] 闪电箭
2025-06-09 21:43:40.669 +08:00 [INF] 阴抓BD模板
2025-06-09 21:43:40.669 +08:00 [INF] 陰爪BD先取消走A
2025-06-09 21:43:40.669 +08:00 [INF] 骑鸟电矛
2025-06-09 21:43:40.669 +08:00 [INF] DD -> 初始化用时: 61.1908 毫秒.
2025-06-09 21:43:40.699 +08:00 [INF] 加载地图数据耗时：1
2025-06-09 21:43:40.699 +08:00 [INF] 当前地图： (0)  N：-1 T：0
2025-06-09 21:47:35.261 +08:00 [INF] 模块: Game State -> : Time: 36.2482 ms. 地址:[938231] Started searching offset with:0
2025-06-09 21:47:35.304 +08:00 [INF] 模块: Area change -> : Time: 37.7838 ms. 地址:[1041769] Started searching offset with:0
2025-06-09 21:47:35.304 +08:00 [INF] 模块: Atlas Helper -> : Time: 35.343 ms. 地址:[941648] Started searching offset with:0
2025-06-09 21:47:35.304 +08:00 [INF] 模块: BlackBarSize -> : Time: 157.1378 ms. 地址:[4531143] Started searching offset with:0
2025-06-09 21:47:35.304 +08:00 [INF] 模块: Terrain Rotator Helper -> : Time: 875.7688 ms. 地址:[25416559] Started searching offset with:0
2025-06-09 21:47:35.304 +08:00 [INF] 模块: Terrain Rotation Selector -> : Time: 876.8133 ms. 地址:[25416559] Started searching offset with:0
2025-06-09 21:47:35.304 +08:00 [INF] 模块: File Root -> : Time: 1120.6874 ms. 地址:[29867444] Started searching offset with:0
2025-06-09 21:47:35.304 +08:00 [INF] 模块: DiagnosticInfoType -> : Time: 2283.2349 ms. 地址:[0] Started searching offset with:600000
2025-06-09 21:47:35.304 +08:00 [INF] 初始化用时 6444.9662 毫秒.
2025-06-09 21:47:35.330 +08:00 [INF] Resize from: {X=0,Y=0,Width=1584,Height=861} to {X=880,Y=497,Width=177,Height=124}
2025-06-09 21:47:35.426 +08:00 [INF] DD loaded in 111.0727 ms.
2025-06-09 21:47:35.426 +08:00 [ERR] 线程1启动
2025-06-09 21:47:35.426 +08:00 [ERR] 线程2启动
2025-06-09 21:47:35.426 +08:00 [INF] 监测线程启动
2025-06-09 21:47:35.426 +08:00 [INF] 子目录列表：
2025-06-09 21:47:35.426 +08:00 [INF] 冰之打击武僧
2025-06-09 21:47:35.426 +08:00 [INF] 召唤
2025-06-09 21:47:35.426 +08:00 [INF] 召唤弓箭手
2025-06-09 21:47:35.473 +08:00 [INF] 囚神僧作者自己用的BD
2025-06-09 21:47:35.473 +08:00 [INF] 囚神杖武僧
2025-06-09 21:47:35.473 +08:00 [INF] 恶魔受伤释放
2025-06-09 21:47:35.473 +08:00 [INF] 恶魔受伤释放双祈愿
2025-06-09 21:47:35.473 +08:00 [INF] 游侠
2025-06-09 21:47:35.473 +08:00 [INF] 电武僧
2025-06-09 21:47:35.473 +08:00 [INF] 电球
2025-06-09 21:47:35.473 +08:00 [INF] 自己用
2025-06-09 21:47:35.473 +08:00 [INF] 闪电
2025-06-09 21:47:35.473 +08:00 [INF] 闪电箭
2025-06-09 21:47:35.473 +08:00 [INF] 阴抓BD模板
2025-06-09 21:47:35.473 +08:00 [INF] 陰爪BD先取消走A
2025-06-09 21:47:35.473 +08:00 [INF] 骑鸟电矛
2025-06-09 21:47:35.473 +08:00 [INF] DD -> 初始化用时: 61.5138 毫秒.
2025-06-09 21:47:35.503 +08:00 [INF] 加载地图数据耗时：1
2025-06-09 21:47:35.503 +08:00 [INF] 当前地图： (0)  N：-1 T：0
2025-06-09 21:50:25.404 +08:00 [INF] 模块: Game State -> : Time: 36.7392 ms. 地址:[938231] Started searching offset with:0
2025-06-09 21:50:25.440 +08:00 [INF] 模块: Area change -> : Time: 36.9349 ms. 地址:[1041769] Started searching offset with:0
2025-06-09 21:50:25.440 +08:00 [INF] 模块: Atlas Helper -> : Time: 35.0125 ms. 地址:[941648] Started searching offset with:0
2025-06-09 21:50:25.441 +08:00 [INF] 模块: BlackBarSize -> : Time: 156.9136 ms. 地址:[4531143] Started searching offset with:0
2025-06-09 21:50:25.441 +08:00 [INF] 模块: Terrain Rotator Helper -> : Time: 854.6224 ms. 地址:[25416559] Started searching offset with:0
2025-06-09 21:50:25.441 +08:00 [INF] 模块: Terrain Rotation Selector -> : Time: 855.0117 ms. 地址:[25416559] Started searching offset with:0
2025-06-09 21:50:25.441 +08:00 [INF] 模块: File Root -> : Time: 1083.926 ms. 地址:[29867444] Started searching offset with:0
2025-06-09 21:50:25.441 +08:00 [INF] 模块: DiagnosticInfoType -> : Time: 2211.6677 ms. 地址:[0] Started searching offset with:600000
2025-06-09 21:50:25.441 +08:00 [ERR] System.AggregateException: 发生一个或多个错误。 ---> System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)
   --- 内部异常堆栈跟踪的结尾 ---
   在 System.Threading.Tasks.Task.ThrowIfExceptional(Boolean includeTaskCanceledExceptions)
   在 System.Threading.Tasks.Task.Wait(Int32 millisecondsTimeout, CancellationToken cancellationToken)
   在 System.Threading.Tasks.Parallel.ForWorker[TLocal](Int32 fromInclusive, Int32 toExclusive, ParallelOptions parallelOptions, Action`1 body, Action`2 bodyWithState, Func`4 bodyWithLocal, Func`1 localInit, Action`1 localFinally)
   在 System.Threading.Tasks.Parallel.For(Int32 fromInclusive, Int32 toExclusive, Action`1 body)
   在 ExileCore.PoEMemory.FilesFromMemory.GetAllFiles()
   在 ExileCore.PoEMemory.FilesContainer.ReloadFiles()
   在 ExileCore.PoEMemory.FilesContainer..ctor(IMemory memory)
   在 ExileCore.PoEMemory.MemoryObjects.TheGame..ctor(IMemory m, Cache cache, CoreSettings settings)
   在 ExileCore.GameController..ctor(Memory memory, SoundController soundController, SettingsContainer settings, MultiThreadManager multiThreadManager)
---> (内部异常 #0) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #1) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #2) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #3) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #4) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #5) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #6) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #7) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #8) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #9) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #10) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #11) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #12) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #13) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #14) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #15) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

2025-06-09 21:50:25.441 +08:00 [ERR] Inject -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.GameController..ctor(Memory memory, SoundController soundController, SettingsContainer settings, MultiThreadManager multiThreadManager)
   在 ExileCore.Core.Inject()
2025-06-09 21:50:25.441 +08:00 [INF] 初始化用时 5866.6993 毫秒.
2025-06-09 21:50:25.441 +08:00 [ERR] System.AggregateException: 发生一个或多个错误。 ---> System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)
   --- 内部异常堆栈跟踪的结尾 ---
   在 System.Threading.Tasks.Task.ThrowIfExceptional(Boolean includeTaskCanceledExceptions)
   在 System.Threading.Tasks.Task.Wait(Int32 millisecondsTimeout, CancellationToken cancellationToken)
   在 System.Threading.Tasks.Parallel.ForWorker[TLocal](Int32 fromInclusive, Int32 toExclusive, ParallelOptions parallelOptions, Action`1 body, Action`2 bodyWithState, Func`4 bodyWithLocal, Func`1 localInit, Action`1 localFinally)
   在 System.Threading.Tasks.Parallel.For(Int32 fromInclusive, Int32 toExclusive, Action`1 body)
   在 ExileCore.PoEMemory.FilesFromMemory.GetAllFiles()
   在 ExileCore.PoEMemory.FilesContainer.ReloadFiles()
   在 ExileCore.PoEMemory.FilesContainer..ctor(IMemory memory)
   在 ExileCore.PoEMemory.MemoryObjects.TheGame..ctor(IMemory m, Cache cache, CoreSettings settings)
   在 ExileCore.GameController..ctor(Memory memory, SoundController soundController, SettingsContainer settings, MultiThreadManager multiThreadManager)
---> (内部异常 #0) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #1) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

2025-06-09 21:50:25.453 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:25.463 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:25.490 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:25.520 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:25.552 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:25.584 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:25.615 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:25.645 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:25.693 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:25.724 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:25.757 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:25.788 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:25.819 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:25.851 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:25.881 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:25.913 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:25.959 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:25.990 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:26.022 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:26.052 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:26.084 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:26.116 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:26.147 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:26.179 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:26.225 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:26.256 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:26.287 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:26.318 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:26.348 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:26.380 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:26.411 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:26.458 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:26.489 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:26.520 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:26.551 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:26.583 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:26.614 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:26.646 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:26.678 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:26.725 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:26.755 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:26.787 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:26.818 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:26.849 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:26.880 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:26.926 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:26.957 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:26.988 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:27.020 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:27.052 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:27.083 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:27.114 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:27.146 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:27.192 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:27.223 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:27.254 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:27.286 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:27.317 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:27.348 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:27.379 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:27.425 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:27.425 +08:00 [ERR] System.AggregateException: 发生一个或多个错误。 ---> System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)
   --- 内部异常堆栈跟踪的结尾 ---
   在 System.Threading.Tasks.Task.ThrowIfExceptional(Boolean includeTaskCanceledExceptions)
   在 System.Threading.Tasks.Task.Wait(Int32 millisecondsTimeout, CancellationToken cancellationToken)
   在 System.Threading.Tasks.Parallel.ForWorker[TLocal](Int32 fromInclusive, Int32 toExclusive, ParallelOptions parallelOptions, Action`1 body, Action`2 bodyWithState, Func`4 bodyWithLocal, Func`1 localInit, Action`1 localFinally)
   在 System.Threading.Tasks.Parallel.For(Int32 fromInclusive, Int32 toExclusive, Action`1 body)
   在 ExileCore.PoEMemory.FilesFromMemory.GetAllFiles()
   在 ExileCore.PoEMemory.FilesContainer.ReloadFiles()
   在 ExileCore.PoEMemory.FilesContainer..ctor(IMemory memory)
   在 ExileCore.PoEMemory.MemoryObjects.TheGame..ctor(IMemory m, Cache cache, CoreSettings settings)
   在 ExileCore.GameController..ctor(Memory memory, SoundController soundController, SettingsContainer settings, MultiThreadManager multiThreadManager)
---> (内部异常 #0) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #1) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #2) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #3) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #4) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #5) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #6) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

2025-06-09 21:50:27.425 +08:00 [ERR] Inject -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.GameController..ctor(Memory memory, SoundController soundController, SettingsContainer settings, MultiThreadManager multiThreadManager)
   在 ExileCore.Core.Inject()
2025-06-09 21:50:27.456 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:27.488 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:27.519 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:27.550 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:27.581 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:27.613 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:27.660 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:27.692 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:27.724 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:27.755 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:27.786 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:27.818 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:27.848 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:27.881 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:27.912 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:27.958 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:28.015 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:28.015 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:28.052 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:28.082 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:28.113 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:28.145 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:28.191 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:28.223 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:28.255 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:28.285 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:28.317 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:28.348 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:28.379 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:28.426 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:28.457 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:28.489 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:28.520 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:28.552 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:28.583 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:28.614 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:28.646 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:28.693 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:28.724 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:28.755 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:28.787 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:28.819 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:28.851 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:28.882 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:28.914 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:28.945 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:28.992 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:29.025 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:29.057 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:29.088 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:29.120 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:29.151 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:29.182 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:29.213 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:29.245 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:29.291 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:29.322 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:29.353 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:29.383 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:29.415 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:29.446 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:29.491 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:29.522 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:29.553 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 21:50:29.723 +08:00 [INF] DD loaded in 108.1702 ms.
2025-06-09 21:50:29.723 +08:00 [ERR] 线程1启动
2025-06-09 21:50:29.723 +08:00 [ERR] 线程2启动
2025-06-09 21:50:29.723 +08:00 [INF] 监测线程启动
2025-06-09 21:50:29.723 +08:00 [INF] 子目录列表：
2025-06-09 21:50:29.723 +08:00 [INF] 冰之打击武僧
2025-06-09 21:50:29.723 +08:00 [INF] 召唤
2025-06-09 21:50:29.753 +08:00 [INF] 召唤弓箭手
2025-06-09 21:50:29.785 +08:00 [INF] 囚神僧作者自己用的BD
2025-06-09 21:50:29.785 +08:00 [INF] 囚神杖武僧
2025-06-09 21:50:29.785 +08:00 [INF] 恶魔受伤释放
2025-06-09 21:50:29.785 +08:00 [INF] 恶魔受伤释放双祈愿
2025-06-09 21:50:29.785 +08:00 [INF] 游侠
2025-06-09 21:50:29.785 +08:00 [INF] 电武僧
2025-06-09 21:50:29.785 +08:00 [INF] 电球
2025-06-09 21:50:29.785 +08:00 [INF] 自己用
2025-06-09 21:50:29.785 +08:00 [INF] 闪电
2025-06-09 21:50:29.785 +08:00 [INF] 闪电箭
2025-06-09 21:50:29.785 +08:00 [INF] 阴抓BD模板
2025-06-09 21:50:29.785 +08:00 [INF] 陰爪BD先取消走A
2025-06-09 21:50:29.785 +08:00 [INF] 骑鸟电矛
2025-06-09 21:50:29.785 +08:00 [INF] DD -> 初始化用时: 64.3861 毫秒.
2025-06-09 21:50:29.815 +08:00 [INF] 加载地图数据耗时：1
2025-06-09 21:50:29.815 +08:00 [INF] 当前地图： (0)  N：-1 T：0
2025-06-09 21:53:40.979 +08:00 [INF] 模块: Atlas Helper -> : Time: 38.2695 ms. 地址:[941648] Started searching offset with:0
2025-06-09 21:53:41.043 +08:00 [INF] 模块: Game State -> : Time: 37.1154 ms. 地址:[938231] Started searching offset with:0
2025-06-09 21:53:41.043 +08:00 [INF] 模块: Area change -> : Time: 38.2932 ms. 地址:[1041769] Started searching offset with:0
2025-06-09 21:53:41.043 +08:00 [INF] 模块: BlackBarSize -> : Time: 160.5395 ms. 地址:[4531143] Started searching offset with:0
2025-06-09 21:53:41.043 +08:00 [INF] 模块: Terrain Rotation Selector -> : Time: 916.0531 ms. 地址:[25416559] Started searching offset with:0
2025-06-09 21:53:41.043 +08:00 [INF] 模块: Terrain Rotator Helper -> : Time: 937.2693 ms. 地址:[25416559] Started searching offset with:0
2025-06-09 21:53:41.044 +08:00 [INF] 模块: File Root -> : Time: 1156.6755 ms. 地址:[29867444] Started searching offset with:0
2025-06-09 21:53:41.044 +08:00 [INF] 模块: DiagnosticInfoType -> : Time: 2408.393 ms. 地址:[0] Started searching offset with:600000
2025-06-09 21:53:41.044 +08:00 [INF] 初始化用时 7552.3736 毫秒.
2025-06-09 21:53:41.044 +08:00 [INF] Resize from: {X=0,Y=0,Width=1584,Height=861} to {X=880,Y=497,Width=177,Height=124}
2025-06-09 21:53:41.140 +08:00 [INF] DD loaded in 113.5595 ms.
2025-06-09 21:53:41.140 +08:00 [ERR] 线程1启动
2025-06-09 21:53:41.140 +08:00 [ERR] 线程2启动
2025-06-09 21:53:41.140 +08:00 [INF] 监测线程启动
2025-06-09 21:53:41.140 +08:00 [INF] 子目录列表：
2025-06-09 21:53:41.140 +08:00 [INF] 冰之打击武僧
2025-06-09 21:53:41.140 +08:00 [INF] 召唤
2025-06-09 21:53:41.171 +08:00 [INF] 召唤弓箭手
2025-06-09 21:53:41.206 +08:00 [INF] 囚神僧作者自己用的BD
2025-06-09 21:53:41.206 +08:00 [INF] 囚神杖武僧
2025-06-09 21:53:41.206 +08:00 [INF] 恶魔受伤释放
2025-06-09 21:53:41.206 +08:00 [INF] 恶魔受伤释放双祈愿
2025-06-09 21:53:41.206 +08:00 [INF] 游侠
2025-06-09 21:53:41.206 +08:00 [INF] 电武僧
2025-06-09 21:53:41.207 +08:00 [INF] 电球
2025-06-09 21:53:41.207 +08:00 [INF] 自己用
2025-06-09 21:53:41.207 +08:00 [INF] 闪电
2025-06-09 21:53:41.207 +08:00 [INF] 闪电箭
2025-06-09 21:53:41.207 +08:00 [INF] 阴抓BD模板
2025-06-09 21:53:41.207 +08:00 [INF] 陰爪BD先取消走A
2025-06-09 21:53:41.207 +08:00 [INF] 骑鸟电矛
2025-06-09 21:53:41.232 +08:00 [INF] DD -> 初始化用时: 75.6835 毫秒.
2025-06-09 21:53:41.232 +08:00 [INF] 加载地图数据耗时：1
2025-06-09 21:53:41.232 +08:00 [INF] 当前地图： (0)  N：-1 T：0
2025-06-09 21:53:41.569 +08:00 [INF] Resize from: {X=880,Y=497,Width=177,Height=124} to {X=906,Y=549,Width=177,Height=124}
2025-06-09 21:53:41.910 +08:00 [INF] Resize from: {X=906,Y=549,Width=177,Height=124} to {X=744,Y=622,Width=177,Height=124}
2025-06-09 21:53:42.233 +08:00 [INF] Resize from: {X=744,Y=622,Width=177,Height=124} to {X=669,Y=758,Width=177,Height=124}
2025-06-09 21:53:42.540 +08:00 [INF] Resize from: {X=669,Y=758,Width=177,Height=124} to {X=681,Y=757,Width=177,Height=124}
2025-06-09 21:54:44.345 +08:00 [INF] Resize from: {X=681,Y=757,Width=177,Height=124} to {X=525,Y=585,Width=177,Height=124}
2025-06-09 21:54:44.672 +08:00 [INF] Resize from: {X=525,Y=585,Width=177,Height=124} to {X=522,Y=581,Width=177,Height=124}
2025-06-09 21:54:45.333 +08:00 [INF] Resize from: {X=522,Y=581,Width=177,Height=124} to {X=484,Y=563,Width=177,Height=124}
2025-06-09 21:54:45.639 +08:00 [INF] Resize from: {X=484,Y=563,Width=177,Height=124} to {X=428,Y=535,Width=177,Height=124}
2025-06-09 21:55:09.909 +08:00 [INF] 模块: Game State -> : Time: 36.408 ms. 地址:[938231] Started searching offset with:0
2025-06-09 21:55:09.953 +08:00 [INF] 模块: Atlas Helper -> : Time: 35.5121 ms. 地址:[941648] Started searching offset with:0
2025-06-09 21:55:09.953 +08:00 [INF] 模块: Area change -> : Time: 37.3569 ms. 地址:[1041769] Started searching offset with:0
2025-06-09 21:55:09.953 +08:00 [INF] 模块: BlackBarSize -> : Time: 156.4486 ms. 地址:[4531143] Started searching offset with:0
2025-06-09 21:55:09.953 +08:00 [INF] 模块: Terrain Rotator Helper -> : Time: 889.4627 ms. 地址:[25416559] Started searching offset with:0
2025-06-09 21:55:09.953 +08:00 [INF] 模块: Terrain Rotation Selector -> : Time: 889.5615 ms. 地址:[25416559] Started searching offset with:0
2025-06-09 21:55:09.953 +08:00 [INF] 模块: File Root -> : Time: 1153.2696 ms. 地址:[29867444] Started searching offset with:0
2025-06-09 21:55:09.954 +08:00 [INF] 模块: DiagnosticInfoType -> : Time: 2318.0142 ms. 地址:[0] Started searching offset with:600000
2025-06-09 21:55:09.954 +08:00 [INF] 初始化用时 6543.903 毫秒.
2025-06-09 21:55:09.954 +08:00 [INF] Resize from: {X=0,Y=0,Width=1584,Height=861} to {X=8,Y=31,Width=1904,Height=993}
2025-06-09 21:55:10.055 +08:00 [INF] DD loaded in 111.4451 ms.
2025-06-09 21:55:10.086 +08:00 [ERR] 线程1启动
2025-06-09 21:55:10.086 +08:00 [ERR] 线程2启动
2025-06-09 21:55:10.086 +08:00 [INF] 监测线程启动
2025-06-09 21:55:10.086 +08:00 [INF] 子目录列表：
2025-06-09 21:55:10.086 +08:00 [INF] 冰之打击武僧
2025-06-09 21:55:10.086 +08:00 [INF] 召唤
2025-06-09 21:55:10.086 +08:00 [INF] 召唤弓箭手
2025-06-09 21:55:10.116 +08:00 [INF] 囚神僧作者自己用的BD
2025-06-09 21:55:10.116 +08:00 [INF] 囚神杖武僧
2025-06-09 21:55:10.116 +08:00 [INF] 恶魔受伤释放
2025-06-09 21:55:10.116 +08:00 [INF] 恶魔受伤释放双祈愿
2025-06-09 21:55:10.116 +08:00 [INF] 游侠
2025-06-09 21:55:10.116 +08:00 [INF] 电武僧
2025-06-09 21:55:10.116 +08:00 [INF] 电球
2025-06-09 21:55:10.116 +08:00 [INF] 自己用
2025-06-09 21:55:10.116 +08:00 [INF] 闪电
2025-06-09 21:55:10.116 +08:00 [INF] 闪电箭
2025-06-09 21:55:10.116 +08:00 [INF] 阴抓BD模板
2025-06-09 21:55:10.147 +08:00 [INF] 陰爪BD先取消走A
2025-06-09 21:55:10.147 +08:00 [INF] 骑鸟电矛
2025-06-09 21:55:10.147 +08:00 [INF] DD -> 初始化用时: 65.3968 毫秒.
2025-06-09 21:55:10.147 +08:00 [INF] 加载地图数据耗时：1
2025-06-09 21:55:10.147 +08:00 [INF] 当前地图： (0)  N：-1 T：0
2025-06-09 22:02:21.049 +08:00 [INF] 模块: Area change -> : Time: 35.1784 ms. 地址:[1041769] Started searching offset with:0
2025-06-09 22:02:21.089 +08:00 [INF] 模块: Game State -> : Time: 33.9929 ms. 地址:[938231] Started searching offset with:0
2025-06-09 22:02:21.089 +08:00 [INF] 模块: Atlas Helper -> : Time: 32.9747 ms. 地址:[941648] Started searching offset with:0
2025-06-09 22:02:21.089 +08:00 [INF] 模块: BlackBarSize -> : Time: 148.3412 ms. 地址:[4531143] Started searching offset with:0
2025-06-09 22:02:21.089 +08:00 [INF] 模块: Terrain Rotator Helper -> : Time: 863.9747 ms. 地址:[25416559] Started searching offset with:0
2025-06-09 22:02:21.089 +08:00 [INF] 模块: Terrain Rotation Selector -> : Time: 864.8506 ms. 地址:[25416559] Started searching offset with:0
2025-06-09 22:02:21.089 +08:00 [INF] 模块: File Root -> : Time: 1095.9607 ms. 地址:[29867444] Started searching offset with:0
2025-06-09 22:02:21.089 +08:00 [INF] 模块: DiagnosticInfoType -> : Time: 2501.4978 ms. 地址:[0] Started searching offset with:600000
2025-06-09 22:02:21.089 +08:00 [ERR] System.AggregateException: 发生一个或多个错误。 ---> System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)
   --- 内部异常堆栈跟踪的结尾 ---
   在 System.Threading.Tasks.Task.ThrowIfExceptional(Boolean includeTaskCanceledExceptions)
   在 System.Threading.Tasks.Task.Wait(Int32 millisecondsTimeout, CancellationToken cancellationToken)
   在 System.Threading.Tasks.Parallel.ForWorker[TLocal](Int32 fromInclusive, Int32 toExclusive, ParallelOptions parallelOptions, Action`1 body, Action`2 bodyWithState, Func`4 bodyWithLocal, Func`1 localInit, Action`1 localFinally)
   在 System.Threading.Tasks.Parallel.For(Int32 fromInclusive, Int32 toExclusive, Action`1 body)
   在 ExileCore.PoEMemory.FilesFromMemory.GetAllFiles()
   在 ExileCore.PoEMemory.FilesContainer.ReloadFiles()
   在 ExileCore.PoEMemory.FilesContainer..ctor(IMemory memory)
   在 ExileCore.PoEMemory.MemoryObjects.TheGame..ctor(IMemory m, Cache cache, CoreSettings settings)
   在 ExileCore.GameController..ctor(Memory memory, SoundController soundController, SettingsContainer settings, MultiThreadManager multiThreadManager)
---> (内部异常 #0) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #1) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #2) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #3) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #4) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #5) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #6) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #7) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #8) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #9) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #10) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #11) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #12) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #13) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #14) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #15) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

2025-06-09 22:02:21.089 +08:00 [ERR] Inject -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.GameController..ctor(Memory memory, SoundController soundController, SettingsContainer settings, MultiThreadManager multiThreadManager)
   在 ExileCore.Core.Inject()
2025-06-09 22:02:21.089 +08:00 [INF] 初始化用时 6129.1919 毫秒.
2025-06-09 22:02:21.103 +08:00 [ERR] System.AggregateException: 发生一个或多个错误。 ---> System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)
   --- 内部异常堆栈跟踪的结尾 ---
   在 System.Threading.Tasks.Task.ThrowIfExceptional(Boolean includeTaskCanceledExceptions)
   在 System.Threading.Tasks.Task.Wait(Int32 millisecondsTimeout, CancellationToken cancellationToken)
   在 System.Threading.Tasks.Parallel.ForWorker[TLocal](Int32 fromInclusive, Int32 toExclusive, ParallelOptions parallelOptions, Action`1 body, Action`2 bodyWithState, Func`4 bodyWithLocal, Func`1 localInit, Action`1 localFinally)
   在 System.Threading.Tasks.Parallel.For(Int32 fromInclusive, Int32 toExclusive, Action`1 body)
   在 ExileCore.PoEMemory.FilesFromMemory.GetAllFiles()
   在 ExileCore.PoEMemory.FilesContainer.ReloadFiles()
   在 ExileCore.PoEMemory.FilesContainer..ctor(IMemory memory)
   在 ExileCore.PoEMemory.MemoryObjects.TheGame..ctor(IMemory m, Cache cache, CoreSettings settings)
   在 ExileCore.GameController..ctor(Memory memory, SoundController soundController, SettingsContainer settings, MultiThreadManager multiThreadManager)
---> (内部异常 #0) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #1) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

2025-06-09 22:02:21.103 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:21.111 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:21.130 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:21.160 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:21.191 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:21.222 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:21.253 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:21.300 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:21.330 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:21.361 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:21.390 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:21.422 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:21.453 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:21.499 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:21.530 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:21.561 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:21.593 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:21.623 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:21.654 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:21.701 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:21.731 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:21.761 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:21.791 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:21.823 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:21.868 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:21.900 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:21.931 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:21.962 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:21.993 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:22.024 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:22.055 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:22.101 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:22.131 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:22.163 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:22.194 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:22.226 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:22.257 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:22.289 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:22.334 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:22.365 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:22.396 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:22.427 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:22.458 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:22.490 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:22.520 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:22.566 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:22.597 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:22.629 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:22.661 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:22.691 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:22.723 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:22.753 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:22.800 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:22.830 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:22.862 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:22.892 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:22.923 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:22.953 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:23.000 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:23.030 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:23.062 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:23.092 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:23.123 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:23.123 +08:00 [ERR] System.AggregateException: 发生一个或多个错误。 ---> System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)
   --- 内部异常堆栈跟踪的结尾 ---
   在 System.Threading.Tasks.Task.ThrowIfExceptional(Boolean includeTaskCanceledExceptions)
   在 System.Threading.Tasks.Task.Wait(Int32 millisecondsTimeout, CancellationToken cancellationToken)
   在 System.Threading.Tasks.Parallel.ForWorker[TLocal](Int32 fromInclusive, Int32 toExclusive, ParallelOptions parallelOptions, Action`1 body, Action`2 bodyWithState, Func`4 bodyWithLocal, Func`1 localInit, Action`1 localFinally)
   在 System.Threading.Tasks.Parallel.For(Int32 fromInclusive, Int32 toExclusive, Action`1 body)
   在 ExileCore.PoEMemory.FilesFromMemory.GetAllFiles()
   在 ExileCore.PoEMemory.FilesContainer.ReloadFiles()
   在 ExileCore.PoEMemory.FilesContainer..ctor(IMemory memory)
   在 ExileCore.PoEMemory.MemoryObjects.TheGame..ctor(IMemory m, Cache cache, CoreSettings settings)
   在 ExileCore.GameController..ctor(Memory memory, SoundController soundController, SettingsContainer settings, MultiThreadManager multiThreadManager)
---> (内部异常 #0) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #1) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #2) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #3) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #4) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

2025-06-09 22:02:23.123 +08:00 [ERR] Inject -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.GameController..ctor(Memory memory, SoundController soundController, SettingsContainer settings, MultiThreadManager multiThreadManager)
   在 ExileCore.Core.Inject()
2025-06-09 22:02:23.153 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:23.200 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:23.231 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:23.263 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:23.294 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:23.324 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:23.354 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:23.401 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:23.431 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:23.462 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:23.493 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:23.523 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:23.555 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:23.601 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:23.633 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:23.664 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:23.695 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:23.726 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:23.756 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:23.788 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:23.834 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:23.865 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:23.896 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:23.928 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:23.958 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:23.988 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:24.020 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:24.066 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:24.123 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:24.123 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:24.158 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:24.190 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:24.221 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:24.267 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:24.299 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:24.329 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:24.361 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:24.391 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:24.423 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:24.455 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:24.501 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:24.536 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:24.563 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:24.595 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:24.625 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:24.656 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:24.688 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:24.734 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:24.765 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:24.796 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:24.826 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:24.857 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:24.888 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:24.919 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:24.966 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:24.998 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:25.028 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:25.058 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:25.089 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:25.120 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:25.166 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:25.197 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:25.228 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:25.258 +08:00 [ERR] Core Tick Menu -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.MenuWindow.Render(GameController _gameController, List`1 plugins)
   在 ExileCore.Core.Tick()
2025-06-09 22:02:25.427 +08:00 [INF] DD loaded in 113.6333 ms.
2025-06-09 22:02:25.427 +08:00 [ERR] 线程1启动
2025-06-09 22:02:25.427 +08:00 [ERR] 线程2启动
2025-06-09 22:02:25.427 +08:00 [INF] 监测线程启动
2025-06-09 22:02:25.457 +08:00 [INF] 子目录列表：
2025-06-09 22:02:25.457 +08:00 [INF] 冰之打击武僧
2025-06-09 22:02:25.457 +08:00 [INF] 召唤
2025-06-09 22:02:25.457 +08:00 [INF] 召唤弓箭手
2025-06-09 22:02:25.489 +08:00 [INF] 囚神僧作者自己用的BD
2025-06-09 22:02:25.489 +08:00 [INF] 囚神杖武僧
2025-06-09 22:02:25.489 +08:00 [INF] 恶魔受伤释放
2025-06-09 22:02:25.489 +08:00 [INF] 恶魔受伤释放双祈愿
2025-06-09 22:02:25.489 +08:00 [INF] 游侠
2025-06-09 22:02:25.489 +08:00 [INF] 电武僧
2025-06-09 22:02:25.489 +08:00 [INF] 电球
2025-06-09 22:02:25.489 +08:00 [INF] 自己用
2025-06-09 22:02:25.489 +08:00 [INF] 闪电
2025-06-09 22:02:25.489 +08:00 [INF] 闪电箭
2025-06-09 22:02:25.489 +08:00 [INF] 阴抓BD模板
2025-06-09 22:02:25.489 +08:00 [INF] 陰爪BD先取消走A
2025-06-09 22:02:25.489 +08:00 [INF] 骑鸟电矛
2025-06-09 22:02:25.489 +08:00 [INF] DD -> 初始化用时: 65.8063 毫秒.
2025-06-09 22:02:25.534 +08:00 [INF] 加载地图数据耗时：2
2025-06-09 22:02:25.534 +08:00 [INF] 当前地图： (0)  N：-1 T：0
