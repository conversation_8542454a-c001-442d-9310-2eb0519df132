2025-05-04 21:02:34.707 +08:00 [ERR] Core -> Loading SoundController failed.
2025-05-04 21:02:34.782 +08:00 [ERR] Core -> SharpDX.SharpDXException: HRESULT: [0x80070490], Module: [Unknown], ApiCode: [Unknown/Unknown], Message: 找不到元素。

   在 SharpDX.Result.CheckError()
   在 SharpDX.XAudio2.XAudio2.CreateMasteringVoice(MasteringVoice masteringVoiceOut, Int32 inputChannels, Int32 inputSampleRate, Int32 flags, String szDeviceId, Nullable`1 effectChainRef, AudioStreamCategory streamCategory)
   在 SharpDX.XAudio2.MasteringVoice..ctor(XAudio2 device, Int32 inputChannels, Int32 inputSampleRate)
   在 ExileCore.SoundController..ctor(String dir)
   在 ExileCore.Core..ctor(RenderForm form)
2025-05-04 21:02:34.783 +08:00 [INF] 模块: Area change -> : Time: 37.5772 ms. 地址:[1037465] Started searching offset with:0
2025-05-04 21:02:34.783 +08:00 [INF] 模块: Game State -> : Time: 36.1704 ms. 地址:[934055] Started searching offset with:0
2025-05-04 21:02:34.783 +08:00 [INF] 模块: Atlas Helper -> : Time: 38.6811 ms. 地址:[937344] Started searching offset with:0
2025-05-04 21:02:34.783 +08:00 [INF] 模块: BlackBarSize -> : Time: 164.1079 ms. 地址:[4510023] Started searching offset with:0
2025-05-04 21:02:34.783 +08:00 [INF] 模块: Terrain Rotator Helper -> : Time: 917.2658 ms. 地址:[25114927] Started searching offset with:0
2025-05-04 21:02:34.783 +08:00 [INF] 模块: Terrain Rotation Selector -> : Time: 959.9805 ms. 地址:[25114927] Started searching offset with:0
2025-05-04 21:02:34.783 +08:00 [INF] 模块: File Root -> : Time: 1175.325 ms. 地址:[29539156] Started searching offset with:0
2025-05-04 21:02:34.783 +08:00 [INF] 模块: DiagnosticInfoType -> : Time: 2657.198 ms. 地址:[0] Started searching offset with:600000
2025-05-04 21:02:34.783 +08:00 [INF] 区域刷新
2025-05-04 21:02:34.783 +08:00 [INF] 初始化用时 8177.905 毫秒.
2025-05-04 21:02:34.783 +08:00 [INF] Resize from: {X=0,Y=0,Width=804,Height=581} to {X=0,Y=0,Width=800,Height=600}
2025-05-04 21:02:34.846 +08:00 [INF] DD loaded in 155.1968 ms.
2025-05-04 21:02:34.846 +08:00 [ERR] 线程1启动
2025-05-04 21:02:34.846 +08:00 [ERR] 线程2启动
2025-05-04 21:02:34.846 +08:00 [INF] 监测线程启动
2025-05-04 21:02:34.846 +08:00 [INF] 子目录列表：
2025-05-04 21:02:34.846 +08:00 [INF] 冰之打击武僧
2025-05-04 21:02:34.849 +08:00 [ERR] 跳过动画
2025-05-04 21:02:34.903 +08:00 [INF] 召唤
2025-05-04 21:02:34.903 +08:00 [INF] 召唤弓箭手
2025-05-04 21:02:34.903 +08:00 [INF] 囚神僧作者自己用的BD
2025-05-04 21:02:34.903 +08:00 [INF] 囚神杖武僧
2025-05-04 21:02:34.903 +08:00 [INF] 恶魔受伤释放
2025-05-04 21:02:34.904 +08:00 [INF] 恶魔受伤释放双祈愿
2025-05-04 21:02:34.904 +08:00 [INF] 游侠
2025-05-04 21:02:34.904 +08:00 [INF] 电武僧
2025-05-04 21:02:34.926 +08:00 [INF] 电球
2025-05-04 21:02:34.926 +08:00 [INF] 自己用
2025-05-04 21:02:34.926 +08:00 [INF] 闪电
2025-05-04 21:02:34.926 +08:00 [INF] 闪电箭
2025-05-04 21:02:34.926 +08:00 [INF] 阴抓BD模板
2025-05-04 21:02:34.926 +08:00 [INF] 陰爪BD先取消走A
2025-05-04 21:02:34.926 +08:00 [INF] 骑鸟电矛
2025-05-04 21:02:34.926 +08:00 [INF] DD -> 初始化用时: 116.2203 毫秒.
2025-05-04 21:02:35.059 +08:00 [INF] 加载地图数据耗时：78
2025-05-04 21:02:35.059 +08:00 [INF] 当前地图：藏身處：殞落 (65) HideoutFelled N：1 T：1
2025-05-04 21:02:36.577 +08:00 [INF] 加载地图数据耗时：75
2025-05-04 21:02:36.577 +08:00 [INF] 当前地图：藏身處：殞落 (65) HideoutFelled N：1 T：1
2025-05-04 21:02:40.034 +08:00 [ERR] 角色卡点
2025-05-04 21:02:40.034 +08:00 [INF] [脱困移动] 移动到随机点: X:240.0154 Y:173.1343
2025-05-04 21:02:40.059 +08:00 [INF] 翻滚
2025-05-04 21:02:41.393 +08:00 [ERR] 角色卡点
2025-05-04 21:02:41.393 +08:00 [INF] [脱困移动] 移动到随机点: X:229.0387 Y:172.7799
2025-05-04 21:02:41.393 +08:00 [INF] 翻滚
2025-05-04 21:02:42.726 +08:00 [ERR] 角色卡点
2025-05-04 21:02:42.726 +08:00 [INF] 翻滚
2025-05-04 21:02:44.126 +08:00 [ERR] 角色卡点
2025-05-04 21:02:44.126 +08:00 [INF] 翻滚
2025-05-04 21:02:45.527 +08:00 [ERR] 角色卡点
2025-05-04 21:02:45.559 +08:00 [INF] 翻滚
2025-05-04 21:02:46.994 +08:00 [ERR] 角色卡点
2025-05-04 21:02:47.027 +08:00 [INF] 翻滚
2025-05-04 21:02:48.426 +08:00 [ERR] 角色卡点
2025-05-04 21:02:48.459 +08:00 [INF] 翻滚
2025-05-04 21:02:49.827 +08:00 [ERR] 角色卡点
2025-05-04 21:02:49.827 +08:00 [INF] 翻滚
2025-05-04 21:02:51.126 +08:00 [ERR] 角色卡点
2025-05-04 21:02:51.159 +08:00 [INF] 翻滚
2025-05-04 21:02:52.461 +08:00 [ERR] 角色卡点
2025-05-04 21:02:52.492 +08:00 [INF] 翻滚
2025-05-04 21:02:53.894 +08:00 [ERR] 角色卡点
2025-05-04 21:02:53.926 +08:00 [INF] 翻滚
2025-05-04 21:02:55.227 +08:00 [ERR] 角色卡点
2025-05-04 21:02:55.227 +08:00 [INF] 翻滚
2025-05-04 21:02:56.526 +08:00 [ERR] 角色卡点
2025-05-04 21:02:56.559 +08:00 [INF] 翻滚
2025-05-04 21:02:57.826 +08:00 [ERR] 角色卡点
2025-05-04 21:02:57.826 +08:00 [INF] 翻滚
2025-05-04 21:02:59.193 +08:00 [ERR] 角色卡点
2025-05-04 21:02:59.193 +08:00 [INF] 翻滚
2025-05-04 21:03:00.494 +08:00 [ERR] 角色卡点
2025-05-04 21:03:00.494 +08:00 [INF] 翻滚
2025-05-04 21:03:01.760 +08:00 [ERR] 角色卡点
2025-05-04 21:03:01.760 +08:00 [INF] 翻滚
2025-05-04 21:03:03.026 +08:00 [ERR] 角色卡点
2025-05-04 21:03:03.061 +08:00 [INF] 翻滚
2025-05-04 21:03:04.292 +08:00 [ERR] 角色卡点
2025-05-04 21:03:04.292 +08:00 [INF] 翻滚
2025-05-04 21:03:05.493 +08:00 [ERR] 角色卡点
2025-05-04 21:03:05.527 +08:00 [INF] 翻滚
2025-05-04 21:03:06.727 +08:00 [ERR] 角色卡点
2025-05-04 21:03:06.760 +08:00 [INF] 翻滚
2025-05-04 21:03:07.994 +08:00 [ERR] 角色卡点
2025-05-04 21:03:07.994 +08:00 [INF] 翻滚
2025-05-04 21:03:09.226 +08:00 [ERR] 角色卡点
2025-05-04 21:03:09.226 +08:00 [INF] 翻滚
2025-05-04 21:03:10.460 +08:00 [ERR] 角色卡点
2025-05-04 21:03:10.493 +08:00 [INF] 翻滚
2025-05-04 21:03:11.693 +08:00 [ERR] 角色卡点
2025-05-04 21:03:11.693 +08:00 [INF] 翻滚
2025-05-04 21:03:16.560 +08:00 [ERR] 角色卡点
2025-05-04 21:03:16.727 +08:00 [ERR] 角色卡点
2025-05-04 21:03:16.859 +08:00 [ERR] 角色卡点
2025-05-04 21:03:17.026 +08:00 [ERR] 角色卡点
2025-05-04 21:03:17.194 +08:00 [ERR] 角色卡点
2025-05-04 21:03:17.327 +08:00 [ERR] 角色卡点
2025-05-04 21:03:17.527 +08:00 [ERR] 角色卡点
2025-05-04 21:03:17.660 +08:00 [ERR] 角色卡点
2025-05-04 21:03:17.826 +08:00 [ERR] 角色卡点
2025-05-04 21:03:17.994 +08:00 [ERR] 角色卡点
2025-05-04 21:03:18.160 +08:00 [ERR] 角色卡点
2025-05-04 21:03:22.660 +08:00 [ERR] 角色卡点
2025-05-04 21:03:22.826 +08:00 [ERR] 角色卡点
2025-05-04 21:03:22.993 +08:00 [ERR] 角色卡点
2025-05-04 21:03:23.159 +08:00 [ERR] 角色卡点
2025-05-04 21:03:26.526 +08:00 [ERR] 角色卡点
2025-05-04 21:03:26.694 +08:00 [ERR] 角色卡点
2025-05-04 21:03:26.860 +08:00 [ERR] 角色卡点
2025-05-04 21:03:26.994 +08:00 [ERR] 角色卡点
2025-05-04 21:03:27.160 +08:00 [ERR] 角色卡点
2025-05-04 21:03:27.326 +08:00 [ERR] 角色卡点
2025-05-04 21:03:27.492 +08:00 [ERR] 角色卡点
2025-05-04 21:03:27.760 +08:00 [ERR] 角色卡点
2025-05-04 21:03:27.927 +08:00 [ERR] 角色卡点
2025-05-04 21:03:28.092 +08:00 [ERR] 角色卡点
2025-05-04 21:03:28.226 +08:00 [ERR] 角色卡点
2025-05-04 21:03:28.394 +08:00 [ERR] 角色卡点
2025-05-04 21:03:28.559 +08:00 [ERR] 角色卡点
2025-05-04 21:03:28.692 +08:00 [ERR] 角色卡点
2025-05-04 21:03:28.859 +08:00 [ERR] 角色卡点
2025-05-04 21:03:28.994 +08:00 [ERR] 角色卡点
2025-05-04 21:03:29.160 +08:00 [ERR] 角色卡点
2025-05-04 21:03:29.327 +08:00 [ERR] 角色卡点
2025-05-04 21:03:29.493 +08:00 [ERR] 角色卡点
2025-05-04 21:03:29.659 +08:00 [ERR] 角色卡点
2025-05-04 21:03:29.793 +08:00 [ERR] 角色卡点
2025-05-04 21:03:29.959 +08:00 [ERR] 角色卡点
2025-05-04 21:03:30.127 +08:00 [ERR] 角色卡点
2025-05-04 21:03:30.292 +08:00 [ERR] 角色卡点
2025-05-04 21:03:30.427 +08:00 [ERR] 角色卡点
2025-05-04 21:03:30.592 +08:00 [ERR] 角色卡点
2025-05-04 21:03:30.760 +08:00 [ERR] 角色卡点
2025-05-04 21:03:30.926 +08:00 [ERR] 角色卡点
2025-05-04 21:03:31.061 +08:00 [ERR] 角色卡点
2025-05-04 21:03:31.226 +08:00 [ERR] 角色卡点
2025-05-04 21:03:31.361 +08:00 [ERR] 角色卡点
2025-05-04 21:03:31.527 +08:00 [ERR] 角色卡点
2025-05-04 21:03:31.692 +08:00 [ERR] 角色卡点
2025-05-04 21:03:31.860 +08:00 [ERR] 角色卡点
2025-05-04 21:03:32.026 +08:00 [ERR] 角色卡点
2025-05-04 21:03:32.160 +08:00 [ERR] 角色卡点
2025-05-04 21:03:32.327 +08:00 [ERR] 角色卡点
2025-05-04 21:03:32.492 +08:00 [ERR] 角色卡点
2025-05-04 21:03:32.627 +08:00 [ERR] 角色卡点
2025-05-04 21:03:32.793 +08:00 [ERR] 角色卡点
2025-05-04 21:03:32.960 +08:00 [ERR] 角色卡点
2025-05-04 21:03:33.093 +08:00 [ERR] 角色卡点
2025-05-04 21:03:33.260 +08:00 [ERR] 角色卡点
2025-05-04 21:03:33.393 +08:00 [ERR] 角色卡点
2025-05-04 21:03:33.560 +08:00 [ERR] 角色卡点
2025-05-04 21:03:33.727 +08:00 [ERR] 角色卡点
2025-05-04 21:03:33.860 +08:00 [ERR] 角色卡点
2025-05-04 21:03:34.059 +08:00 [ERR] 角色卡点
2025-05-04 21:03:34.194 +08:00 [ERR] 角色卡点
2025-05-04 21:03:34.360 +08:00 [ERR] 角色卡点
2025-05-04 21:03:34.526 +08:00 [ERR] 角色卡点
2025-05-04 21:03:34.693 +08:00 [ERR] 角色卡点
2025-05-04 21:03:34.894 +08:00 [ERR] 角色卡点
2025-05-04 21:03:35.094 +08:00 [ERR] 角色卡点
2025-05-04 21:03:35.293 +08:00 [ERR] 角色卡点
2025-05-04 21:03:35.526 +08:00 [ERR] 角色卡点
2025-05-04 21:03:35.727 +08:00 [ERR] 角色卡点
2025-05-04 21:03:35.925 +08:00 [ERR] 角色卡点
2025-05-04 21:03:36.126 +08:00 [ERR] 角色卡点
2025-05-04 21:03:36.327 +08:00 [ERR] 角色卡点
2025-05-04 21:03:36.560 +08:00 [ERR] 角色卡点
2025-05-04 21:03:36.760 +08:00 [ERR] 角色卡点
2025-05-04 21:03:36.959 +08:00 [ERR] 角色卡点
2025-05-04 21:03:37.159 +08:00 [ERR] 角色卡点
2025-05-04 21:03:37.359 +08:00 [ERR] 角色卡点
2025-05-04 21:03:37.559 +08:00 [ERR] 角色卡点
2025-05-04 21:03:37.760 +08:00 [ERR] 角色卡点
2025-05-04 21:03:37.960 +08:00 [ERR] 角色卡点
2025-05-04 21:03:38.159 +08:00 [ERR] 角色卡点
2025-05-04 21:03:38.361 +08:00 [ERR] 角色卡点
2025-05-04 21:03:38.559 +08:00 [ERR] 角色卡点
2025-05-04 21:03:38.761 +08:00 [ERR] 角色卡点
2025-05-04 21:03:38.959 +08:00 [ERR] 角色卡点
2025-05-04 21:03:39.159 +08:00 [ERR] 角色卡点
2025-05-04 21:03:39.360 +08:00 [ERR] 角色卡点
2025-05-04 21:03:39.560 +08:00 [ERR] 角色卡点
2025-05-04 21:03:46.260 +08:00 [ERR] 角色卡点
2025-05-04 21:03:46.492 +08:00 [ERR] 角色卡点
2025-05-04 21:03:46.692 +08:00 [ERR] 角色卡点
2025-05-04 21:03:46.893 +08:00 [ERR] 角色卡点
2025-05-04 21:03:47.127 +08:00 [ERR] 角色卡点
2025-05-04 21:03:47.360 +08:00 [ERR] 角色卡点
2025-05-04 21:03:51.494 +08:00 [ERR] 角色卡点
2025-05-04 21:03:53.126 +08:00 [ERR] 角色卡点
2025-05-04 21:03:53.827 +08:00 [ERR] 角色卡点
2025-05-04 21:03:54.026 +08:00 [ERR] 角色卡点
2025-05-04 21:03:54.227 +08:00 [ERR] 角色卡点
2025-05-04 21:03:54.426 +08:00 [ERR] 角色卡点
2025-05-04 21:03:54.627 +08:00 [ERR] 角色卡点
2025-05-04 21:03:54.827 +08:00 [ERR] 角色卡点
2025-05-04 21:03:58.494 +08:00 [ERR] 角色卡点
2025-05-04 21:03:58.726 +08:00 [ERR] 角色卡点
2025-05-04 21:04:01.259 +08:00 [ERR] 角色卡点
2025-05-04 21:04:01.492 +08:00 [ERR] 角色卡点
2025-05-04 21:04:01.727 +08:00 [ERR] 角色卡点
2025-05-04 21:04:01.959 +08:00 [ERR] 角色卡点
2025-05-04 21:04:02.194 +08:00 [ERR] 角色卡点
2025-05-04 21:04:02.393 +08:00 [ERR] 角色卡点
2025-05-04 21:04:02.626 +08:00 [ERR] 角色卡点
2025-05-04 21:04:02.859 +08:00 [ERR] 角色卡点
2025-05-04 21:04:03.092 +08:00 [ERR] 角色卡点
2025-05-04 21:04:03.326 +08:00 [ERR] 角色卡点
2025-05-04 21:04:03.559 +08:00 [ERR] 角色卡点
2025-05-04 21:04:03.794 +08:00 [ERR] 角色卡点
2025-05-04 21:04:04.027 +08:00 [ERR] 角色卡点
2025-05-04 21:04:04.260 +08:00 [ERR] 角色卡点
2025-05-04 21:04:04.493 +08:00 [ERR] 角色卡点
2025-05-04 21:04:04.727 +08:00 [ERR] 角色卡点
2025-05-04 21:04:04.963 +08:00 [ERR] 角色卡点
2025-05-04 21:04:05.194 +08:00 [ERR] 角色卡点
2025-05-04 21:04:06.627 +08:00 [ERR] 角色卡点
2025-05-04 21:04:08.426 +08:00 [ERR] 角色卡点
2025-05-04 21:04:10.127 +08:00 [ERR] 角色卡点
2025-05-04 21:04:12.060 +08:00 [ERR] 角色卡点
2025-05-04 21:04:13.961 +08:00 [ERR] 角色卡点
2025-05-04 21:04:15.826 +08:00 [ERR] 角色卡点
2025-05-04 21:04:17.626 +08:00 [ERR] 角色卡点
2025-05-04 21:04:19.359 +08:00 [ERR] 角色卡点
2025-05-04 21:04:21.128 +08:00 [ERR] 角色卡点
2025-05-04 21:04:22.927 +08:00 [ERR] 角色卡点
2025-05-04 21:04:24.693 +08:00 [ERR] 角色卡点
2025-05-04 21:04:26.459 +08:00 [ERR] 角色卡点
2025-05-04 21:04:28.193 +08:00 [ERR] 角色卡点
2025-05-04 21:04:29.994 +08:00 [ERR] 角色卡点
2025-05-04 21:04:31.826 +08:00 [ERR] 角色卡点
2025-05-04 21:04:33.559 +08:00 [ERR] 角色卡点
2025-05-04 21:04:35.393 +08:00 [ERR] 角色卡点
2025-05-04 21:04:35.393 +08:00 [INF] 翻滚
2025-05-04 21:04:37.126 +08:00 [ERR] 角色卡点
2025-05-04 21:04:38.893 +08:00 [ERR] 角色卡点
2025-05-04 21:04:40.726 +08:00 [ERR] 角色卡点
2025-05-04 21:04:40.726 +08:00 [INF] 翻滚
2025-05-04 21:04:42.593 +08:00 [ERR] 角色卡点
2025-05-04 21:04:44.393 +08:00 [ERR] 角色卡点
2025-05-04 21:04:46.126 +08:00 [ERR] 角色卡点
2025-05-04 21:08:26.262 +08:00 [ERR] Core -> Loading SoundController failed.
2025-05-04 21:08:26.387 +08:00 [ERR] Core -> SharpDX.SharpDXException: HRESULT: [0x80070490], Module: [Unknown], ApiCode: [Unknown/Unknown], Message: 找不到元素。

   在 SharpDX.Result.CheckError()
   在 SharpDX.XAudio2.XAudio2.CreateMasteringVoice(MasteringVoice masteringVoiceOut, Int32 inputChannels, Int32 inputSampleRate, Int32 flags, String szDeviceId, Nullable`1 effectChainRef, AudioStreamCategory streamCategory)
   在 SharpDX.XAudio2.MasteringVoice..ctor(XAudio2 device, Int32 inputChannels, Int32 inputSampleRate)
   在 ExileCore.SoundController..ctor(String dir)
   在 ExileCore.Core..ctor(RenderForm form)
2025-05-04 21:08:26.387 +08:00 [INF] 模块: Game State -> : Time: 36.4405 ms. 地址:[934055] Started searching offset with:0
2025-05-04 21:08:26.387 +08:00 [INF] 模块: Area change -> : Time: 37.5075 ms. 地址:[1037465] Started searching offset with:0
2025-05-04 21:08:26.387 +08:00 [INF] 模块: Atlas Helper -> : Time: 38.6098 ms. 地址:[937344] Started searching offset with:0
2025-05-04 21:08:26.387 +08:00 [INF] 模块: BlackBarSize -> : Time: 164.2234 ms. 地址:[4510023] Started searching offset with:0
2025-05-04 21:08:26.387 +08:00 [INF] 模块: Terrain Rotation Selector -> : Time: 921.9288 ms. 地址:[25114927] Started searching offset with:0
2025-05-04 21:08:26.387 +08:00 [INF] 模块: Terrain Rotator Helper -> : Time: 947.6328 ms. 地址:[25114927] Started searching offset with:0
2025-05-04 21:08:26.387 +08:00 [INF] 模块: File Root -> : Time: 1183.7093 ms. 地址:[29539156] Started searching offset with:0
2025-05-04 21:08:26.387 +08:00 [INF] 模块: DiagnosticInfoType -> : Time: 2619.1006 ms. 地址:[0] Started searching offset with:600000
2025-05-04 21:08:26.387 +08:00 [INF] Resize from: {X=0,Y=0,Width=804,Height=581} to {X=0,Y=0,Width=800,Height=600}
2025-05-04 21:08:26.387 +08:00 [INF] 初始化用时 8218.85 毫秒.
2025-05-04 21:08:26.436 +08:00 [INF] DD loaded in 173.3533 ms.
2025-05-04 21:08:26.436 +08:00 [ERR] 线程1启动
2025-05-04 21:08:26.436 +08:00 [ERR] 线程2启动
2025-05-04 21:08:26.436 +08:00 [INF] 监测线程启动
2025-05-04 21:08:26.436 +08:00 [INF] 子目录列表：
2025-05-04 21:08:26.436 +08:00 [INF] 冰之打击武僧
2025-05-04 21:08:26.436 +08:00 [INF] 召唤
2025-05-04 21:08:26.436 +08:00 [INF] 召唤弓箭手
2025-05-04 21:08:26.436 +08:00 [INF] 囚神僧作者自己用的BD
2025-05-04 21:08:26.436 +08:00 [INF] 囚神杖武僧
2025-05-04 21:08:26.436 +08:00 [INF] 恶魔受伤释放
2025-05-04 21:08:26.436 +08:00 [INF] 恶魔受伤释放双祈愿
2025-05-04 21:08:26.436 +08:00 [INF] 游侠
2025-05-04 21:08:26.436 +08:00 [INF] 电武僧
2025-05-04 21:08:26.436 +08:00 [INF] 电球
2025-05-04 21:08:26.436 +08:00 [INF] 自己用
2025-05-04 21:08:26.452 +08:00 [INF] 闪电
2025-05-04 21:08:26.453 +08:00 [INF] 闪电箭
2025-05-04 21:08:26.453 +08:00 [INF] 阴抓BD模板
2025-05-04 21:08:26.453 +08:00 [INF] 陰爪BD先取消走A
2025-05-04 21:08:26.453 +08:00 [INF] 骑鸟电矛
2025-05-04 21:08:26.456 +08:00 [INF] DD -> 初始化用时: 149.2801 毫秒.
2025-05-04 21:08:26.545 +08:00 [INF] 加载地图数据耗时：2
2025-05-04 21:08:26.545 +08:00 [INF] 当前地图： (0)  N：-1 T：0
2025-05-04 21:09:03.897 +08:00 [ERR] Core -> Loading SoundController failed.
2025-05-04 21:09:03.944 +08:00 [ERR] Core -> SharpDX.SharpDXException: HRESULT: [0x80070490], Module: [Unknown], ApiCode: [Unknown/Unknown], Message: 找不到元素。

   在 SharpDX.Result.CheckError()
   在 SharpDX.XAudio2.XAudio2.CreateMasteringVoice(MasteringVoice masteringVoiceOut, Int32 inputChannels, Int32 inputSampleRate, Int32 flags, String szDeviceId, Nullable`1 effectChainRef, AudioStreamCategory streamCategory)
   在 SharpDX.XAudio2.MasteringVoice..ctor(XAudio2 device, Int32 inputChannels, Int32 inputSampleRate)
   在 ExileCore.SoundController..ctor(String dir)
   在 ExileCore.Core..ctor(RenderForm form)
2025-05-04 21:09:03.944 +08:00 [INF] 模块: Game State -> : Time: 57.1246 ms. 地址:[934055] Started searching offset with:0
2025-05-04 21:09:03.944 +08:00 [INF] 模块: Area change -> : Time: 63.9 ms. 地址:[1037465] Started searching offset with:0
2025-05-04 21:09:03.944 +08:00 [INF] 模块: Atlas Helper -> : Time: 63.7463 ms. 地址:[937344] Started searching offset with:0
2025-05-04 21:09:03.944 +08:00 [INF] 模块: BlackBarSize -> : Time: 166.1967 ms. 地址:[4510023] Started searching offset with:0
2025-05-04 21:09:03.944 +08:00 [INF] 模块: Terrain Rotation Selector -> : Time: 983.1362 ms. 地址:[25114927] Started searching offset with:0
2025-05-04 21:09:03.944 +08:00 [INF] 模块: Terrain Rotator Helper -> : Time: 998.6267 ms. 地址:[25114927] Started searching offset with:0
2025-05-04 21:09:03.944 +08:00 [INF] 模块: File Root -> : Time: 1226.3476 ms. 地址:[29539156] Started searching offset with:0
2025-05-04 21:09:03.944 +08:00 [INF] 模块: DiagnosticInfoType -> : Time: 2548.687 ms. 地址:[0] Started searching offset with:600000
2025-05-04 21:09:03.944 +08:00 [INF] 初始化用时 6881.566 毫秒.
2025-05-04 21:09:03.944 +08:00 [INF] Resize from: {X=0,Y=0,Width=804,Height=581} to {X=0,Y=0,Width=800,Height=600}
2025-05-04 21:09:03.983 +08:00 [INF] DD loaded in 124.6672 ms.
2025-05-04 21:09:03.997 +08:00 [ERR] 线程1启动
2025-05-04 21:09:03.997 +08:00 [ERR] 线程2启动
2025-05-04 21:09:03.997 +08:00 [INF] 监测线程启动
2025-05-04 21:09:03.997 +08:00 [INF] 子目录列表：
2025-05-04 21:09:03.997 +08:00 [INF] 冰之打击武僧
2025-05-04 21:09:04.053 +08:00 [INF] 召唤
2025-05-04 21:09:04.053 +08:00 [INF] 召唤弓箭手
2025-05-04 21:09:04.084 +08:00 [INF] 囚神僧作者自己用的BD
2025-05-04 21:09:04.084 +08:00 [INF] 囚神杖武僧
2025-05-04 21:09:04.084 +08:00 [INF] 恶魔受伤释放
2025-05-04 21:09:04.084 +08:00 [INF] 恶魔受伤释放双祈愿
2025-05-04 21:09:04.084 +08:00 [INF] 游侠
2025-05-04 21:09:04.084 +08:00 [INF] 电武僧
2025-05-04 21:09:04.084 +08:00 [INF] 电球
2025-05-04 21:09:04.084 +08:00 [INF] 自己用
2025-05-04 21:09:04.084 +08:00 [INF] 闪电
2025-05-04 21:09:04.084 +08:00 [INF] 闪电箭
2025-05-04 21:09:04.084 +08:00 [INF] 阴抓BD模板
2025-05-04 21:09:04.084 +08:00 [INF] 陰爪BD先取消走A
2025-05-04 21:09:04.084 +08:00 [INF] 骑鸟电矛
2025-05-04 21:09:04.084 +08:00 [INF] DD -> 初始化用时: 79.6479 毫秒.
2025-05-04 21:09:04.118 +08:00 [INF] 加载地图数据耗时：2
2025-05-04 21:09:04.118 +08:00 [INF] 当前地图： (0)  N：-1 T：0
2025-05-04 21:10:36.595 +08:00 [ERR] Core -> Loading SoundController failed.
2025-05-04 21:10:36.649 +08:00 [ERR] Core -> SharpDX.SharpDXException: HRESULT: [0x80070490], Module: [Unknown], ApiCode: [Unknown/Unknown], Message: 找不到元素。

   在 SharpDX.Result.CheckError()
   在 SharpDX.XAudio2.XAudio2.CreateMasteringVoice(MasteringVoice masteringVoiceOut, Int32 inputChannels, Int32 inputSampleRate, Int32 flags, String szDeviceId, Nullable`1 effectChainRef, AudioStreamCategory streamCategory)
   在 SharpDX.XAudio2.MasteringVoice..ctor(XAudio2 device, Int32 inputChannels, Int32 inputSampleRate)
   在 ExileCore.SoundController..ctor(String dir)
   在 ExileCore.Core..ctor(RenderForm form)
2025-05-04 21:10:36.649 +08:00 [INF] 模块: Atlas Helper -> : Time: 36.9938 ms. 地址:[937344] Started searching offset with:0
2025-05-04 21:10:36.649 +08:00 [INF] 模块: Game State -> : Time: 36.425 ms. 地址:[934055] Started searching offset with:0
2025-05-04 21:10:36.649 +08:00 [INF] 模块: Area change -> : Time: 37.3589 ms. 地址:[1037465] Started searching offset with:0
2025-05-04 21:10:36.650 +08:00 [INF] 模块: BlackBarSize -> : Time: 169.0063 ms. 地址:[4510023] Started searching offset with:0
2025-05-04 21:10:36.650 +08:00 [INF] 模块: Terrain Rotator Helper -> : Time: 931.9068 ms. 地址:[25114927] Started searching offset with:0
2025-05-04 21:10:36.650 +08:00 [INF] 模块: Terrain Rotation Selector -> : Time: 950.7044 ms. 地址:[25114927] Started searching offset with:0
2025-05-04 21:10:36.650 +08:00 [INF] 模块: File Root -> : Time: 1228.469 ms. 地址:[29539156] Started searching offset with:0
2025-05-04 21:10:36.650 +08:00 [INF] 模块: DiagnosticInfoType -> : Time: 2572.439 ms. 地址:[0] Started searching offset with:600000
2025-05-04 21:10:36.650 +08:00 [INF] 初始化用时 6669.1814 毫秒.
2025-05-04 21:10:36.650 +08:00 [INF] Resize from: {X=0,Y=0,Width=804,Height=581} to {X=0,Y=0,Width=800,Height=600}
2025-05-04 21:10:36.693 +08:00 [INF] DD loaded in 125.8944 ms.
2025-05-04 21:10:36.693 +08:00 [ERR] 线程1启动
2025-05-04 21:10:36.693 +08:00 [ERR] 线程2启动
2025-05-04 21:10:36.693 +08:00 [INF] 监测线程启动
2025-05-04 21:10:36.693 +08:00 [INF] 子目录列表：
2025-05-04 21:10:36.693 +08:00 [INF] 冰之打击武僧
2025-05-04 21:10:36.762 +08:00 [INF] 召唤
2025-05-04 21:10:36.762 +08:00 [INF] 召唤弓箭手
2025-05-04 21:10:36.762 +08:00 [INF] 囚神僧作者自己用的BD
2025-05-04 21:10:36.762 +08:00 [INF] 囚神杖武僧
2025-05-04 21:10:36.762 +08:00 [INF] 恶魔受伤释放
2025-05-04 21:10:36.762 +08:00 [INF] 恶魔受伤释放双祈愿
2025-05-04 21:10:36.762 +08:00 [INF] 游侠
2025-05-04 21:10:36.762 +08:00 [INF] 电武僧
2025-05-04 21:10:36.762 +08:00 [INF] 电球
2025-05-04 21:10:36.762 +08:00 [INF] 自己用
2025-05-04 21:10:36.762 +08:00 [INF] 闪电
2025-05-04 21:10:36.762 +08:00 [INF] 闪电箭
2025-05-04 21:10:36.762 +08:00 [INF] 阴抓BD模板
2025-05-04 21:10:36.762 +08:00 [INF] 陰爪BD先取消走A
2025-05-04 21:10:36.762 +08:00 [INF] 骑鸟电矛
2025-05-04 21:10:36.762 +08:00 [INF] DD -> 初始化用时: 82.761 毫秒.
2025-05-04 21:10:36.795 +08:00 [INF] 加载地图数据耗时：2
2025-05-04 21:10:36.795 +08:00 [INF] 当前地图： (0)  N：-1 T：0
2025-05-04 21:11:41.490 +08:00 [INF] 加载地图数据耗时：88
2025-05-04 21:11:41.490 +08:00 [INF] 当前地图：藏身處：殞落 (65) HideoutFelled N：1 T：1
2025-05-04 21:17:50.563 +08:00 [INF] 子目录列表：
2025-05-04 21:17:50.563 +08:00 [INF] 冰之打击武僧
2025-05-04 21:17:50.563 +08:00 [INF] 召唤
2025-05-04 21:17:50.563 +08:00 [INF] 召唤弓箭手
2025-05-04 21:17:50.563 +08:00 [INF] 囚神僧作者自己用的BD
2025-05-04 21:17:50.563 +08:00 [INF] 囚神杖武僧
2025-05-04 21:17:50.563 +08:00 [INF] 恶魔受伤释放
2025-05-04 21:17:50.563 +08:00 [INF] 恶魔受伤释放双祈愿
2025-05-04 21:17:50.563 +08:00 [INF] 游侠
2025-05-04 21:17:50.563 +08:00 [INF] 电武僧
2025-05-04 21:17:50.563 +08:00 [INF] 电球
2025-05-04 21:17:50.563 +08:00 [INF] 自己用
2025-05-04 21:17:50.563 +08:00 [INF] 闪电
2025-05-04 21:17:50.563 +08:00 [INF] 闪电箭
2025-05-04 21:17:50.563 +08:00 [INF] 阴抓BD模板
2025-05-04 21:17:50.563 +08:00 [INF] 陰爪BD先取消走A
2025-05-04 21:17:50.563 +08:00 [INF] 骑鸟电矛
2025-05-04 21:17:50.696 +08:00 [INF] 子目录列表：
2025-05-04 21:17:50.696 +08:00 [INF] 冰之打击武僧
2025-05-04 21:17:50.696 +08:00 [INF] 召唤
2025-05-04 21:17:50.696 +08:00 [INF] 召唤弓箭手
2025-05-04 21:17:50.696 +08:00 [INF] 囚神僧作者自己用的BD
2025-05-04 21:17:50.696 +08:00 [INF] 囚神杖武僧
2025-05-04 21:17:50.696 +08:00 [INF] 恶魔受伤释放
2025-05-04 21:17:50.696 +08:00 [INF] 恶魔受伤释放双祈愿
2025-05-04 21:17:50.696 +08:00 [INF] 游侠
2025-05-04 21:17:50.696 +08:00 [INF] 电武僧
2025-05-04 21:17:50.696 +08:00 [INF] 电球
2025-05-04 21:17:50.696 +08:00 [INF] 自己用
2025-05-04 21:17:50.696 +08:00 [INF] 闪电
2025-05-04 21:17:50.696 +08:00 [INF] 闪电箭
2025-05-04 21:17:50.696 +08:00 [INF] 阴抓BD模板
2025-05-04 21:17:50.696 +08:00 [INF] 陰爪BD先取消走A
2025-05-04 21:17:50.696 +08:00 [INF] 骑鸟电矛
2025-05-04 21:17:52.785 +08:00 [INF] 加载地图数据耗时：53
2025-05-04 21:17:52.785 +08:00 [INF] 当前地图：藏身處：殞落 (65) HideoutFelled N：1 T：1
2025-05-04 21:17:52.924 +08:00 [INF] 加载地图数据耗时：58
2025-05-04 21:17:52.924 +08:00 [INF] 当前地图：藏身處：殞落 (65) HideoutFelled N：1 T：1
2025-05-04 21:17:53.184 +08:00 [INF] 加载地图数据耗时：118
2025-05-04 21:17:53.184 +08:00 [INF] 当前地图：藏身處：殞落 (65) HideoutFelled N：1 T：1
2025-05-04 21:17:57.096 +08:00 [ERR] 角色卡点
2025-05-04 21:17:57.096 +08:00 [INF] [脱困移动] 移动到随机点: X:267.1989 Y:206.756
2025-05-04 21:17:58.330 +08:00 [ERR] 角色卡点
2025-05-04 21:17:58.330 +08:00 [INF] [脱困移动] 移动到随机点: X:256.1827 Y:214.9379
2025-05-04 21:17:59.562 +08:00 [ERR] 角色卡点
2025-05-04 21:18:00.797 +08:00 [ERR] 角色卡点
2025-05-04 21:18:00.830 +08:00 [INF] 翻滚
2025-05-04 21:18:02.062 +08:00 [ERR] 角色卡点
2025-05-04 21:18:03.330 +08:00 [ERR] 角色卡点
2025-05-04 21:18:04.563 +08:00 [ERR] 角色卡点
2025-05-04 21:18:07.163 +08:00 [ERR] 角色卡点
2025-05-04 21:18:08.430 +08:00 [ERR] 角色卡点
2025-05-04 21:18:09.096 +08:00 [ERR] 角色卡点
2025-05-04 21:18:09.329 +08:00 [ERR] 角色卡点
2025-05-04 21:18:09.529 +08:00 [ERR] 角色卡点
2025-05-04 21:18:09.729 +08:00 [ERR] 角色卡点
2025-05-04 21:18:10.628 +08:00 [ERR] 角色卡点
2025-05-04 21:26:11.041 +08:00 [ERR] Core -> Loading SoundController failed.
2025-05-04 21:26:11.109 +08:00 [ERR] Core -> SharpDX.SharpDXException: HRESULT: [0x80070490], Module: [Unknown], ApiCode: [Unknown/Unknown], Message: 找不到元素。

   在 SharpDX.Result.CheckError()
   在 SharpDX.XAudio2.XAudio2.CreateMasteringVoice(MasteringVoice masteringVoiceOut, Int32 inputChannels, Int32 inputSampleRate, Int32 flags, String szDeviceId, Nullable`1 effectChainRef, AudioStreamCategory streamCategory)
   在 SharpDX.XAudio2.MasteringVoice..ctor(XAudio2 device, Int32 inputChannels, Int32 inputSampleRate)
   在 ExileCore.SoundController..ctor(String dir)
   在 ExileCore.Core..ctor(RenderForm form)
2025-05-04 21:26:11.109 +08:00 [INF] 模块: Game State -> : Time: 36.7719 ms. 地址:[934055] Started searching offset with:0
2025-05-04 21:26:11.109 +08:00 [INF] 模块: Area change -> : Time: 38.1881 ms. 地址:[1037465] Started searching offset with:0
2025-05-04 21:26:11.109 +08:00 [INF] 模块: Atlas Helper -> : Time: 49.4501 ms. 地址:[937344] Started searching offset with:0
2025-05-04 21:26:11.109 +08:00 [INF] 模块: BlackBarSize -> : Time: 162.7625 ms. 地址:[4510023] Started searching offset with:0
2025-05-04 21:26:11.109 +08:00 [INF] 模块: Terrain Rotation Selector -> : Time: 954.3925 ms. 地址:[25114927] Started searching offset with:0
2025-05-04 21:26:11.109 +08:00 [INF] 模块: Terrain Rotator Helper -> : Time: 1047.767 ms. 地址:[25114927] Started searching offset with:0
2025-05-04 21:26:11.109 +08:00 [INF] 模块: File Root -> : Time: 1204.5916 ms. 地址:[29539156] Started searching offset with:0
2025-05-04 21:26:11.109 +08:00 [INF] 模块: DiagnosticInfoType -> : Time: 2613.1601 ms. 地址:[0] Started searching offset with:600000
2025-05-04 21:26:11.109 +08:00 [INF] 初始化用时 7472.2895 毫秒.
2025-05-04 21:26:11.109 +08:00 [INF] Resize from: {X=0,Y=0,Width=804,Height=581} to {X=0,Y=0,Width=800,Height=600}
2025-05-04 21:26:11.154 +08:00 [INF] DD loaded in 132.7249 ms.
2025-05-04 21:26:11.154 +08:00 [ERR] 线程1启动
2025-05-04 21:26:11.154 +08:00 [ERR] 线程2启动
2025-05-04 21:26:11.154 +08:00 [INF] 监测线程启动
2025-05-04 21:26:11.154 +08:00 [INF] 子目录列表：
2025-05-04 21:26:11.154 +08:00 [INF] 冰之打击武僧
2025-05-04 21:26:11.216 +08:00 [INF] 召唤
2025-05-04 21:26:11.216 +08:00 [INF] 召唤弓箭手
2025-05-04 21:26:11.244 +08:00 [INF] 囚神僧作者自己用的BD
2025-05-04 21:26:11.244 +08:00 [INF] 囚神杖武僧
2025-05-04 21:26:11.244 +08:00 [INF] 恶魔受伤释放
2025-05-04 21:26:11.244 +08:00 [INF] 恶魔受伤释放双祈愿
2025-05-04 21:26:11.244 +08:00 [INF] 游侠
2025-05-04 21:26:11.244 +08:00 [INF] 电武僧
2025-05-04 21:26:11.244 +08:00 [INF] 电球
2025-05-04 21:26:11.244 +08:00 [INF] 自己用
2025-05-04 21:26:11.244 +08:00 [INF] 闪电
2025-05-04 21:26:11.244 +08:00 [INF] 闪电箭
2025-05-04 21:26:11.244 +08:00 [INF] 阴抓BD模板
2025-05-04 21:26:11.244 +08:00 [INF] 陰爪BD先取消走A
2025-05-04 21:26:11.244 +08:00 [INF] 骑鸟电矛
2025-05-04 21:26:11.278 +08:00 [INF] DD -> 初始化用时: 107.8937 毫秒.
2025-05-04 21:26:11.278 +08:00 [INF] 加载地图数据耗时：2
2025-05-04 21:26:11.278 +08:00 [INF] 当前地图： (0)  N：-1 T：0
2025-05-04 21:27:12.275 +08:00 [INF] 加载地图数据耗时：152
2025-05-04 21:27:12.275 +08:00 [INF] 当前地图：藏身處：殞落 (65) HideoutFelled N：1 T：1
2025-05-04 21:28:01.792 +08:00 [INF] 加载地图数据耗时：141
2025-05-04 21:28:01.792 +08:00 [INF] 当前地图：藏身處：殞落 (65) HideoutFelled N：1 T：1
2025-05-04 21:29:11.779 +08:00 [ERR] 角色卡点
2025-05-04 21:29:11.779 +08:00 [INF] [脱困移动] 移动到随机点: X:216.8927 Y:237.5975
2025-05-04 21:29:12.012 +08:00 [ERR] 角色卡点
2025-05-04 21:29:12.012 +08:00 [INF] [脱困移动] 移动到随机点: X:234.1988 Y:248.9225
2025-05-04 21:29:12.246 +08:00 [ERR] 角色卡点
2025-05-04 21:29:12.478 +08:00 [ERR] 角色卡点
2025-05-04 21:29:12.727 +08:00 [ERR] 角色卡点
2025-05-04 21:29:12.946 +08:00 [ERR] 角色卡点
2025-05-04 21:29:13.178 +08:00 [ERR] 角色卡点
2025-05-04 21:29:13.378 +08:00 [ERR] 角色卡点
2025-05-04 21:29:13.613 +08:00 [ERR] 角色卡点
2025-05-04 21:29:13.879 +08:00 [ERR] 角色卡点
2025-05-04 21:29:14.112 +08:00 [ERR] 角色卡点
2025-05-04 21:29:14.345 +08:00 [ERR] 角色卡点
2025-05-04 21:29:14.579 +08:00 [ERR] 角色卡点
2025-05-04 21:29:14.812 +08:00 [ERR] 角色卡点
2025-05-04 21:29:15.046 +08:00 [ERR] 角色卡点
2025-05-04 21:29:15.312 +08:00 [ERR] 角色卡点
2025-05-04 21:29:15.513 +08:00 [ERR] 角色卡点
2025-05-04 21:29:15.712 +08:00 [ERR] 角色卡点
2025-05-04 21:29:15.945 +08:00 [ERR] 角色卡点
2025-05-04 21:29:16.145 +08:00 [ERR] 角色卡点
2025-05-04 21:29:16.379 +08:00 [ERR] 角色卡点
2025-05-04 21:29:16.612 +08:00 [ERR] 角色卡点
2025-05-04 21:29:16.851 +08:00 [ERR] 角色卡点
2025-05-04 21:29:17.080 +08:00 [ERR] 角色卡点
2025-05-04 21:29:17.311 +08:00 [ERR] 角色卡点
2025-05-04 21:29:17.546 +08:00 [ERR] 角色卡点
2025-05-04 21:29:17.746 +08:00 [ERR] 角色卡点
2025-05-04 21:29:17.979 +08:00 [ERR] 角色卡点
2025-05-04 21:29:18.179 +08:00 [ERR] 角色卡点
2025-05-04 21:29:18.411 +08:00 [ERR] 角色卡点
2025-05-04 21:29:18.646 +08:00 [ERR] 角色卡点
2025-05-04 21:29:18.845 +08:00 [ERR] 角色卡点
2025-05-04 21:29:19.045 +08:00 [ERR] 角色卡点
2025-05-04 21:29:23.811 +08:00 [ERR] 角色卡点
2025-05-04 21:29:24.046 +08:00 [ERR] 角色卡点
2025-05-04 21:29:24.278 +08:00 [ERR] 角色卡点
2025-05-04 21:29:24.545 +08:00 [ERR] 角色卡点
2025-05-04 21:29:24.778 +08:00 [ERR] 角色卡点
2025-05-04 21:29:25.012 +08:00 [ERR] 角色卡点
2025-05-04 21:29:25.212 +08:00 [ERR] 角色卡点
2025-05-04 21:29:25.445 +08:00 [ERR] 角色卡点
2025-05-04 21:29:25.679 +08:00 [ERR] 角色卡点
2025-05-04 21:29:33.479 +08:00 [ERR] 角色卡点
2025-05-04 21:29:33.511 +08:00 [INF] 翻滚
2025-05-04 21:29:33.679 +08:00 [ERR] 角色卡点
2025-05-04 21:29:33.712 +08:00 [INF] 翻滚
2025-05-04 21:29:33.878 +08:00 [ERR] 角色卡点
2025-05-04 21:29:34.078 +08:00 [ERR] 角色卡点
2025-05-04 21:29:34.279 +08:00 [ERR] 角色卡点
2025-05-04 21:29:34.512 +08:00 [ERR] 角色卡点
2025-05-04 21:29:34.745 +08:00 [ERR] 角色卡点
2025-05-04 21:29:34.945 +08:00 [ERR] 角色卡点
2025-05-04 21:29:35.145 +08:00 [ERR] 角色卡点
2025-05-04 21:29:35.345 +08:00 [ERR] 角色卡点
2025-05-04 21:29:35.546 +08:00 [ERR] 角色卡点
2025-05-04 21:29:35.745 +08:00 [ERR] 角色卡点
2025-05-04 21:29:35.944 +08:00 [ERR] 角色卡点
2025-05-04 21:29:36.146 +08:00 [ERR] 角色卡点
2025-05-04 21:29:36.345 +08:00 [ERR] 角色卡点
2025-05-04 21:29:36.547 +08:00 [ERR] 角色卡点
2025-05-04 21:29:36.745 +08:00 [ERR] 角色卡点
2025-05-04 21:29:36.945 +08:00 [ERR] 角色卡点
2025-05-04 21:29:37.145 +08:00 [ERR] 角色卡点
2025-05-04 21:29:37.345 +08:00 [ERR] 角色卡点
2025-05-04 21:29:37.545 +08:00 [ERR] 角色卡点
2025-05-04 21:29:39.945 +08:00 [ERR] 角色卡点
2025-05-04 21:29:40.178 +08:00 [ERR] 角色卡点
2025-05-04 21:29:40.378 +08:00 [ERR] 角色卡点
2025-05-04 21:29:40.579 +08:00 [ERR] 角色卡点
2025-05-04 21:29:40.779 +08:00 [ERR] 角色卡点
2025-05-04 21:29:40.978 +08:00 [ERR] 角色卡点
2025-05-04 21:29:41.212 +08:00 [ERR] 角色卡点
2025-05-04 21:29:41.446 +08:00 [ERR] 角色卡点
2025-05-04 21:29:41.646 +08:00 [ERR] 角色卡点
2025-05-04 21:29:41.879 +08:00 [ERR] 角色卡点
2025-05-04 21:29:42.078 +08:00 [ERR] 角色卡点
2025-05-04 21:29:42.311 +08:00 [ERR] 角色卡点
2025-05-04 21:29:42.344 +08:00 [INF] 翻滚
2025-05-04 21:29:42.545 +08:00 [ERR] 角色卡点
2025-05-04 21:29:42.811 +08:00 [ERR] 角色卡点
2025-05-04 21:29:43.045 +08:00 [ERR] 角色卡点
2025-05-04 21:29:43.245 +08:00 [ERR] 角色卡点
2025-05-04 21:29:51.611 +08:00 [ERR] 角色卡点
2025-05-04 21:29:51.847 +08:00 [ERR] 角色卡点
2025-05-04 21:29:52.079 +08:00 [ERR] 角色卡点
2025-05-04 21:29:52.313 +08:00 [ERR] 角色卡点
2025-05-04 21:29:52.511 +08:00 [ERR] 角色卡点
2025-05-04 21:29:52.744 +08:00 [ERR] 角色卡点
2025-05-04 21:29:52.979 +08:00 [ERR] 角色卡点
2025-05-04 21:29:53.213 +08:00 [ERR] 角色卡点
2025-05-04 21:29:53.445 +08:00 [ERR] 角色卡点
2025-05-04 21:29:53.679 +08:00 [ERR] 角色卡点
2025-05-04 21:29:53.911 +08:00 [ERR] 角色卡点
2025-05-04 21:29:54.144 +08:00 [ERR] 角色卡点
2025-05-04 21:29:54.380 +08:00 [ERR] 角色卡点
2025-05-04 21:29:54.611 +08:00 [ERR] 角色卡点
2025-05-04 21:29:54.845 +08:00 [ERR] 角色卡点
2025-05-04 21:29:55.079 +08:00 [ERR] 角色卡点
2025-05-04 21:29:55.312 +08:00 [ERR] 角色卡点
2025-05-04 21:29:55.545 +08:00 [ERR] 角色卡点
2025-05-04 21:29:55.778 +08:00 [ERR] 角色卡点
2025-05-04 21:29:56.012 +08:00 [ERR] 角色卡点
2025-05-04 21:29:56.246 +08:00 [ERR] 角色卡点
2025-05-04 21:29:56.479 +08:00 [ERR] 角色卡点
2025-05-04 21:29:56.712 +08:00 [ERR] 角色卡点
2025-05-04 21:29:56.945 +08:00 [ERR] 角色卡点
2025-05-04 21:29:57.178 +08:00 [ERR] 角色卡点
2025-05-04 21:29:57.412 +08:00 [ERR] 角色卡点
2025-05-04 21:29:57.645 +08:00 [ERR] 角色卡点
2025-05-04 21:29:57.912 +08:00 [ERR] 角色卡点
2025-05-04 21:29:58.145 +08:00 [ERR] 角色卡点
2025-05-04 21:29:58.346 +08:00 [ERR] 角色卡点
2025-05-04 21:29:58.611 +08:00 [ERR] 角色卡点
2025-05-04 21:29:58.844 +08:00 [ERR] 角色卡点
2025-05-04 21:29:59.079 +08:00 [ERR] 角色卡点
2025-05-04 21:29:59.311 +08:00 [ERR] 角色卡点
2025-05-04 21:29:59.544 +08:00 [ERR] 角色卡点
2025-05-04 21:29:59.778 +08:00 [ERR] 角色卡点
2025-05-04 21:30:00.011 +08:00 [ERR] 角色卡点
2025-05-04 21:30:00.278 +08:00 [ERR] 角色卡点
2025-05-04 21:30:40.845 +08:00 [ERR] 角色卡点
2025-05-04 21:30:41.078 +08:00 [ERR] 角色卡点
2025-05-04 21:30:41.278 +08:00 [ERR] 角色卡点
2025-05-04 21:30:41.513 +08:00 [ERR] 角色卡点
2025-05-04 21:30:41.711 +08:00 [ERR] 角色卡点
2025-05-04 21:30:41.945 +08:00 [ERR] 角色卡点
2025-05-04 21:30:42.145 +08:00 [ERR] 角色卡点
2025-05-04 21:30:42.344 +08:00 [ERR] 角色卡点
2025-05-04 21:30:42.578 +08:00 [ERR] 角色卡点
2025-05-04 21:30:42.813 +08:00 [ERR] 角色卡点
2025-05-04 21:30:43.011 +08:00 [ERR] 角色卡点
2025-05-04 21:30:43.245 +08:00 [ERR] 角色卡点
2025-05-04 21:30:43.445 +08:00 [ERR] 角色卡点
2025-05-04 21:30:43.678 +08:00 [ERR] 角色卡点
2025-05-04 21:30:43.911 +08:00 [ERR] 角色卡点
2025-05-04 21:30:44.145 +08:00 [ERR] 角色卡点
2025-05-04 21:30:46.646 +08:00 [ERR] 角色卡点
2025-05-04 21:30:46.878 +08:00 [ERR] 角色卡点
2025-05-04 21:30:47.112 +08:00 [ERR] 角色卡点
2025-05-04 21:30:47.345 +08:00 [ERR] 角色卡点
2025-05-04 21:30:47.579 +08:00 [ERR] 角色卡点
2025-05-04 21:30:47.812 +08:00 [ERR] 角色卡点
2025-05-04 21:30:48.045 +08:00 [ERR] 角色卡点
2025-05-04 21:30:48.244 +08:00 [ERR] 角色卡点
2025-05-04 21:30:48.478 +08:00 [ERR] 角色卡点
2025-05-04 21:30:48.679 +08:00 [ERR] 角色卡点
2025-05-04 21:30:48.878 +08:00 [ERR] 角色卡点
2025-05-04 21:30:49.112 +08:00 [ERR] 角色卡点
2025-05-04 21:30:49.346 +08:00 [ERR] 角色卡点
2025-05-04 21:30:49.579 +08:00 [ERR] 角色卡点
2025-05-04 21:30:49.813 +08:00 [ERR] 角色卡点
2025-05-04 21:30:50.048 +08:00 [ERR] 角色卡点
2025-05-04 21:31:12.179 +08:00 [ERR] 角色卡点
2025-05-04 21:31:12.412 +08:00 [ERR] 角色卡点
2025-05-04 21:31:12.646 +08:00 [ERR] 角色卡点
2025-05-04 21:31:12.844 +08:00 [ERR] 角色卡点
2025-05-04 21:31:13.079 +08:00 [ERR] 角色卡点
2025-05-04 21:31:13.312 +08:00 [ERR] 角色卡点
2025-05-04 21:31:13.512 +08:00 [ERR] 角色卡点
2025-05-04 21:31:13.745 +08:00 [ERR] 角色卡点
2025-05-04 21:31:13.978 +08:00 [ERR] 角色卡点
2025-05-04 21:31:14.212 +08:00 [ERR] 角色卡点
2025-05-04 21:31:14.446 +08:00 [ERR] 角色卡点
2025-05-04 21:31:14.645 +08:00 [ERR] 角色卡点
2025-05-04 21:31:14.878 +08:00 [ERR] 角色卡点
2025-05-04 21:31:15.111 +08:00 [ERR] 角色卡点
2025-05-04 21:31:15.378 +08:00 [ERR] 角色卡点
2025-05-04 21:31:15.612 +08:00 [ERR] 角色卡点
2025-05-04 21:31:15.845 +08:00 [ERR] 角色卡点
2025-05-04 21:31:16.078 +08:00 [ERR] 角色卡点
2025-05-04 21:31:16.312 +08:00 [ERR] 角色卡点
2025-05-04 21:31:16.545 +08:00 [ERR] 角色卡点
2025-05-04 21:31:16.779 +08:00 [ERR] 角色卡点
2025-05-04 21:31:17.045 +08:00 [ERR] 角色卡点
2025-05-04 21:31:17.279 +08:00 [ERR] 角色卡点
2025-05-04 21:31:17.479 +08:00 [ERR] 角色卡点
2025-05-04 21:31:17.731 +08:00 [ERR] 角色卡点
2025-05-04 21:31:17.945 +08:00 [ERR] 角色卡点
2025-05-04 21:31:18.212 +08:00 [ERR] 角色卡点
2025-05-04 21:31:18.411 +08:00 [ERR] 角色卡点
2025-05-04 21:31:18.647 +08:00 [ERR] 角色卡点
2025-05-04 21:31:18.878 +08:00 [ERR] 角色卡点
2025-05-04 21:31:19.112 +08:00 [ERR] 角色卡点
2025-05-04 21:31:19.313 +08:00 [ERR] 角色卡点
2025-05-04 21:31:19.545 +08:00 [ERR] 角色卡点
2025-05-04 21:31:19.778 +08:00 [ERR] 角色卡点
2025-05-04 21:31:20.012 +08:00 [ERR] 角色卡点
2025-05-04 21:31:20.246 +08:00 [ERR] 角色卡点
2025-05-04 21:31:20.479 +08:00 [ERR] 角色卡点
2025-05-04 21:31:20.678 +08:00 [ERR] 角色卡点
2025-05-04 21:31:20.912 +08:00 [ERR] 角色卡点
2025-05-04 21:31:21.445 +08:00 [ERR] 角色卡点
2025-05-04 21:31:21.679 +08:00 [ERR] 角色卡点
2025-05-04 21:31:21.912 +08:00 [ERR] 角色卡点
2025-05-04 21:31:22.111 +08:00 [INF] 子目录列表：
2025-05-04 21:31:22.111 +08:00 [INF] 冰之打击武僧
2025-05-04 21:31:22.111 +08:00 [INF] 召唤
2025-05-04 21:31:22.111 +08:00 [INF] 召唤弓箭手
2025-05-04 21:31:22.111 +08:00 [INF] 囚神僧作者自己用的BD
2025-05-04 21:31:22.111 +08:00 [INF] 囚神杖武僧
2025-05-04 21:31:22.111 +08:00 [INF] 恶魔受伤释放
2025-05-04 21:31:22.111 +08:00 [INF] 恶魔受伤释放双祈愿
2025-05-04 21:31:22.111 +08:00 [INF] 游侠
2025-05-04 21:31:22.111 +08:00 [INF] 电武僧
2025-05-04 21:31:22.111 +08:00 [INF] 电球
2025-05-04 21:31:22.111 +08:00 [INF] 自己用
2025-05-04 21:31:22.111 +08:00 [INF] 闪电
2025-05-04 21:31:22.111 +08:00 [INF] 闪电箭
2025-05-04 21:31:22.111 +08:00 [INF] 阴抓BD模板
2025-05-04 21:31:22.111 +08:00 [INF] 陰爪BD先取消走A
2025-05-04 21:31:22.111 +08:00 [INF] 骑鸟电矛
2025-05-04 21:31:22.144 +08:00 [ERR] 角色卡点
2025-05-04 21:31:22.244 +08:00 [INF] 子目录列表：
2025-05-04 21:31:22.244 +08:00 [INF] 冰之打击武僧
2025-05-04 21:31:22.244 +08:00 [INF] 召唤
2025-05-04 21:31:22.244 +08:00 [INF] 召唤弓箭手
2025-05-04 21:31:22.244 +08:00 [INF] 囚神僧作者自己用的BD
2025-05-04 21:31:22.244 +08:00 [INF] 囚神杖武僧
2025-05-04 21:31:22.244 +08:00 [INF] 恶魔受伤释放
2025-05-04 21:31:22.244 +08:00 [INF] 恶魔受伤释放双祈愿
2025-05-04 21:31:22.244 +08:00 [INF] 游侠
2025-05-04 21:31:22.244 +08:00 [INF] 电武僧
2025-05-04 21:31:22.244 +08:00 [INF] 电球
2025-05-04 21:31:22.244 +08:00 [INF] 自己用
2025-05-04 21:31:22.244 +08:00 [INF] 闪电
2025-05-04 21:31:22.244 +08:00 [INF] 闪电箭
2025-05-04 21:31:22.244 +08:00 [INF] 阴抓BD模板
2025-05-04 21:31:22.244 +08:00 [INF] 陰爪BD先取消走A
2025-05-04 21:31:22.244 +08:00 [INF] 骑鸟电矛
2025-05-04 21:31:22.378 +08:00 [ERR] 角色卡点
2025-05-04 21:31:22.412 +08:00 [INF] 子目录列表：
2025-05-04 21:31:22.412 +08:00 [INF] 冰之打击武僧
2025-05-04 21:31:22.412 +08:00 [INF] 召唤
2025-05-04 21:31:22.412 +08:00 [INF] 召唤弓箭手
2025-05-04 21:31:22.412 +08:00 [INF] 囚神僧作者自己用的BD
2025-05-04 21:31:22.412 +08:00 [INF] 囚神杖武僧
2025-05-04 21:31:22.412 +08:00 [INF] 恶魔受伤释放
2025-05-04 21:31:22.412 +08:00 [INF] 恶魔受伤释放双祈愿
2025-05-04 21:31:22.412 +08:00 [INF] 游侠
2025-05-04 21:31:22.412 +08:00 [INF] 电武僧
2025-05-04 21:31:22.412 +08:00 [INF] 电球
2025-05-04 21:31:22.412 +08:00 [INF] 自己用
2025-05-04 21:31:22.412 +08:00 [INF] 闪电
2025-05-04 21:31:22.412 +08:00 [INF] 闪电箭
2025-05-04 21:31:22.412 +08:00 [INF] 阴抓BD模板
2025-05-04 21:31:22.412 +08:00 [INF] 陰爪BD先取消走A
2025-05-04 21:31:22.412 +08:00 [INF] 骑鸟电矛
2025-05-04 21:31:22.545 +08:00 [INF] 子目录列表：
2025-05-04 21:31:22.545 +08:00 [INF] 冰之打击武僧
2025-05-04 21:31:22.545 +08:00 [INF] 召唤
2025-05-04 21:31:22.545 +08:00 [INF] 召唤弓箭手
2025-05-04 21:31:22.545 +08:00 [INF] 囚神僧作者自己用的BD
2025-05-04 21:31:22.545 +08:00 [INF] 囚神杖武僧
2025-05-04 21:31:22.545 +08:00 [INF] 恶魔受伤释放
2025-05-04 21:31:22.545 +08:00 [INF] 恶魔受伤释放双祈愿
2025-05-04 21:31:22.545 +08:00 [INF] 游侠
2025-05-04 21:31:22.545 +08:00 [INF] 电武僧
2025-05-04 21:31:22.545 +08:00 [INF] 电球
2025-05-04 21:31:22.545 +08:00 [INF] 自己用
2025-05-04 21:31:22.545 +08:00 [INF] 闪电
2025-05-04 21:31:22.545 +08:00 [INF] 闪电箭
2025-05-04 21:31:22.545 +08:00 [INF] 阴抓BD模板
2025-05-04 21:31:22.545 +08:00 [INF] 陰爪BD先取消走A
2025-05-04 21:31:22.545 +08:00 [INF] 骑鸟电矛
2025-05-04 21:31:22.611 +08:00 [ERR] 角色卡点
2025-05-04 21:31:22.845 +08:00 [ERR] 角色卡点
2025-05-04 21:31:23.078 +08:00 [ERR] 角色卡点
2025-05-04 21:31:23.312 +08:00 [ERR] 角色卡点
2025-05-04 21:31:23.546 +08:00 [ERR] 角色卡点
2025-05-04 21:31:23.779 +08:00 [ERR] 角色卡点
2025-05-04 21:31:24.012 +08:00 [ERR] 角色卡点
2025-05-04 21:31:24.245 +08:00 [ERR] 角色卡点
2025-05-04 21:31:24.479 +08:00 [ERR] 角色卡点
2025-05-04 21:31:24.712 +08:00 [ERR] 角色卡点
2025-05-04 21:31:24.945 +08:00 [ERR] 角色卡点
2025-05-04 21:31:25.179 +08:00 [ERR] 角色卡点
2025-05-04 21:31:25.412 +08:00 [ERR] 角色卡点
2025-05-04 21:31:25.678 +08:00 [ERR] 角色卡点
2025-05-04 21:31:25.911 +08:00 [ERR] 角色卡点
2025-05-04 21:31:26.145 +08:00 [ERR] 角色卡点
2025-05-04 21:31:26.346 +08:00 [ERR] 角色卡点
2025-05-04 21:31:26.578 +08:00 [ERR] 角色卡点
2025-05-04 21:31:26.811 +08:00 [ERR] 角色卡点
2025-05-04 21:31:27.046 +08:00 [ERR] 角色卡点
2025-05-04 21:31:27.279 +08:00 [ERR] 角色卡点
2025-05-04 21:31:27.512 +08:00 [ERR] 角色卡点
2025-05-04 21:31:27.727 +08:00 [ERR] 角色卡点
2025-05-04 21:31:27.945 +08:00 [ERR] 角色卡点
2025-05-04 21:31:28.180 +08:00 [ERR] 角色卡点
2025-05-04 21:31:28.412 +08:00 [ERR] 角色卡点
2025-05-04 21:31:28.612 +08:00 [ERR] 角色卡点
2025-05-04 21:31:28.844 +08:00 [ERR] 角色卡点
2025-05-04 21:31:29.078 +08:00 [ERR] 角色卡点
2025-05-04 21:31:29.312 +08:00 [ERR] 角色卡点
2025-05-04 21:31:29.544 +08:00 [ERR] 角色卡点
2025-05-04 21:31:29.779 +08:00 [ERR] 角色卡点
2025-05-04 21:31:30.044 +08:00 [ERR] 角色卡点
2025-05-04 21:31:30.278 +08:00 [ERR] 角色卡点
2025-05-04 21:31:30.478 +08:00 [ERR] 角色卡点
2025-05-04 21:31:30.678 +08:00 [ERR] 角色卡点
2025-05-04 21:31:30.911 +08:00 [ERR] 角色卡点
2025-05-04 21:31:31.112 +08:00 [ERR] 角色卡点
2025-05-04 21:31:31.345 +08:00 [ERR] 角色卡点
2025-05-04 21:31:31.578 +08:00 [ERR] 角色卡点
2025-05-04 21:31:31.811 +08:00 [ERR] 角色卡点
2025-05-04 21:31:32.012 +08:00 [ERR] 角色卡点
2025-05-04 21:31:32.246 +08:00 [ERR] 角色卡点
2025-05-04 21:31:32.478 +08:00 [ERR] 角色卡点
2025-05-04 21:31:32.726 +08:00 [ERR] 角色卡点
2025-05-04 21:31:32.945 +08:00 [ERR] 角色卡点
2025-05-04 21:31:33.178 +08:00 [ERR] 角色卡点
2025-05-04 21:31:33.412 +08:00 [ERR] 角色卡点
2025-05-04 21:31:33.645 +08:00 [ERR] 角色卡点
2025-05-04 21:31:33.845 +08:00 [ERR] 角色卡点
2025-05-04 21:31:34.078 +08:00 [ERR] 角色卡点
2025-05-04 21:31:34.278 +08:00 [ERR] 角色卡点
2025-05-04 21:31:34.478 +08:00 [ERR] 角色卡点
2025-05-04 21:31:34.712 +08:00 [ERR] 角色卡点
2025-05-04 21:31:34.946 +08:00 [ERR] 角色卡点
2025-05-04 21:31:35.211 +08:00 [ERR] 角色卡点
2025-05-04 21:31:35.411 +08:00 [ERR] 角色卡点
2025-05-04 21:31:35.646 +08:00 [ERR] 角色卡点
2025-05-04 21:31:35.879 +08:00 [ERR] 角色卡点
2025-05-04 21:31:36.112 +08:00 [ERR] 角色卡点
2025-05-04 21:31:36.345 +08:00 [ERR] 角色卡点
2025-05-04 21:31:36.579 +08:00 [ERR] 角色卡点
2025-05-04 21:31:36.846 +08:00 [ERR] 角色卡点
2025-05-04 21:31:37.046 +08:00 [ERR] 角色卡点
2025-05-04 21:31:37.278 +08:00 [ERR] 角色卡点
2025-05-04 21:31:37.511 +08:00 [ERR] 角色卡点
2025-05-04 21:31:37.746 +08:00 [ERR] 角色卡点
2025-05-04 21:31:37.979 +08:00 [ERR] 角色卡点
2025-05-04 21:31:38.212 +08:00 [ERR] 角色卡点
2025-05-04 21:31:38.446 +08:00 [ERR] 角色卡点
2025-05-04 21:31:38.712 +08:00 [ERR] 角色卡点
2025-05-04 21:31:38.944 +08:00 [ERR] 角色卡点
2025-05-04 21:31:39.179 +08:00 [ERR] 角色卡点
2025-05-04 21:31:39.412 +08:00 [ERR] 角色卡点
2025-05-04 21:31:39.644 +08:00 [ERR] 角色卡点
2025-05-04 21:31:39.844 +08:00 [ERR] 角色卡点
2025-05-04 21:31:40.081 +08:00 [ERR] 角色卡点
2025-05-04 21:31:40.312 +08:00 [ERR] 角色卡点
2025-05-04 21:31:40.579 +08:00 [ERR] 角色卡点
2025-05-04 21:31:40.813 +08:00 [ERR] 角色卡点
2025-05-04 21:31:41.046 +08:00 [ERR] 角色卡点
2025-05-04 21:31:41.246 +08:00 [ERR] 角色卡点
2025-05-04 21:31:41.479 +08:00 [ERR] 角色卡点
2025-05-04 21:31:41.678 +08:00 [ERR] 角色卡点
2025-05-04 21:31:41.878 +08:00 [ERR] 角色卡点
2025-05-04 21:31:42.111 +08:00 [ERR] 角色卡点
2025-05-04 21:31:42.312 +08:00 [ERR] 角色卡点
2025-05-04 21:31:42.545 +08:00 [ERR] 角色卡点
2025-05-04 21:31:42.778 +08:00 [ERR] 角色卡点
2025-05-04 21:31:43.012 +08:00 [ERR] 角色卡点
2025-05-04 21:31:43.245 +08:00 [ERR] 角色卡点
2025-05-04 21:31:43.479 +08:00 [ERR] 角色卡点
2025-05-04 21:31:43.745 +08:00 [ERR] 角色卡点
2025-05-04 21:31:43.978 +08:00 [ERR] 角色卡点
2025-05-04 21:31:44.212 +08:00 [ERR] 角色卡点
2025-05-04 21:31:44.412 +08:00 [ERR] 角色卡点
2025-05-04 21:31:44.646 +08:00 [ERR] 角色卡点
2025-05-04 21:31:44.880 +08:00 [ERR] 角色卡点
2025-05-04 21:31:45.079 +08:00 [ERR] 角色卡点
2025-05-04 21:31:45.312 +08:00 [ERR] 角色卡点
2025-05-04 21:31:45.578 +08:00 [ERR] 角色卡点
2025-05-04 21:31:45.812 +08:00 [ERR] 角色卡点
2025-05-04 21:31:46.045 +08:00 [ERR] 角色卡点
2025-05-04 21:31:46.279 +08:00 [ERR] 角色卡点
2025-05-04 21:31:46.479 +08:00 [ERR] 角色卡点
2025-05-04 21:31:46.713 +08:00 [ERR] 角色卡点
2025-05-04 21:31:46.911 +08:00 [ERR] 角色卡点
2025-05-04 21:31:47.144 +08:00 [ERR] 角色卡点
2025-05-04 21:31:47.379 +08:00 [ERR] 角色卡点
2025-05-04 21:31:47.611 +08:00 [ERR] 角色卡点
2025-05-04 21:31:47.812 +08:00 [ERR] 角色卡点
2025-05-04 21:31:48.045 +08:00 [ERR] 角色卡点
2025-05-04 21:31:48.278 +08:00 [ERR] 角色卡点
2025-05-04 21:31:48.545 +08:00 [ERR] 角色卡点
2025-05-04 21:31:48.779 +08:00 [ERR] 角色卡点
2025-05-04 21:31:49.012 +08:00 [ERR] 角色卡点
2025-05-04 21:31:49.246 +08:00 [ERR] 角色卡点
2025-05-04 21:31:49.479 +08:00 [ERR] 角色卡点
2025-05-04 21:31:49.711 +08:00 [ERR] 角色卡点
2025-05-04 21:31:49.946 +08:00 [ERR] 角色卡点
2025-05-04 21:31:50.212 +08:00 [ERR] 角色卡点
2025-05-04 21:31:50.412 +08:00 [ERR] 角色卡点
2025-05-04 21:31:50.646 +08:00 [ERR] 角色卡点
2025-05-04 21:31:50.878 +08:00 [ERR] 角色卡点
2025-05-04 21:31:51.112 +08:00 [ERR] 角色卡点
2025-05-04 21:31:51.345 +08:00 [ERR] 角色卡点
2025-05-04 21:31:51.546 +08:00 [ERR] 角色卡点
2025-05-04 21:31:51.778 +08:00 [ERR] 角色卡点
2025-05-04 21:31:52.046 +08:00 [ERR] 角色卡点
2025-05-04 21:31:52.246 +08:00 [ERR] 角色卡点
2025-05-04 21:31:52.479 +08:00 [ERR] 角色卡点
2025-05-04 21:31:52.678 +08:00 [ERR] 角色卡点
2025-05-04 21:31:52.879 +08:00 [ERR] 角色卡点
2025-05-04 21:31:53.146 +08:00 [ERR] 角色卡点
2025-05-04 21:31:53.378 +08:00 [ERR] 角色卡点
2025-05-04 21:31:53.612 +08:00 [ERR] 角色卡点
2025-05-04 21:31:53.844 +08:00 [ERR] 角色卡点
2025-05-04 21:31:54.078 +08:00 [ERR] 角色卡点
2025-05-04 21:31:54.278 +08:00 [ERR] 角色卡点
2025-05-04 21:31:54.512 +08:00 [ERR] 角色卡点
2025-05-04 21:31:54.778 +08:00 [ERR] 角色卡点
2025-05-04 21:31:55.011 +08:00 [ERR] 角色卡点
2025-05-04 21:31:55.245 +08:00 [ERR] 角色卡点
2025-05-04 21:31:55.478 +08:00 [ERR] 角色卡点
2025-05-04 21:31:55.712 +08:00 [ERR] 角色卡点
2025-05-04 21:31:55.912 +08:00 [ERR] 角色卡点
2025-05-04 21:31:56.112 +08:00 [ERR] 角色卡点
2025-05-04 21:31:56.378 +08:00 [ERR] 角色卡点
2025-05-04 21:31:56.611 +08:00 [ERR] 角色卡点
2025-05-04 21:31:56.845 +08:00 [ERR] 角色卡点
2025-05-04 21:31:57.078 +08:00 [ERR] 角色卡点
2025-05-04 21:31:57.312 +08:00 [ERR] 角色卡点
2025-05-04 21:31:57.546 +08:00 [ERR] 角色卡点
2025-05-04 21:31:57.779 +08:00 [ERR] 角色卡点
2025-05-04 21:31:57.979 +08:00 [ERR] 角色卡点
2025-05-04 21:31:58.245 +08:00 [ERR] 角色卡点
2025-05-04 21:31:58.479 +08:00 [ERR] 角色卡点
2025-05-04 21:31:58.713 +08:00 [ERR] 角色卡点
2025-05-04 21:31:58.944 +08:00 [ERR] 角色卡点
2025-05-04 21:31:59.179 +08:00 [ERR] 角色卡点
2025-05-04 21:31:59.411 +08:00 [ERR] 角色卡点
2025-05-04 21:31:59.646 +08:00 [ERR] 角色卡点
2025-05-04 21:31:59.912 +08:00 [ERR] 角色卡点
2025-05-04 21:32:00.146 +08:00 [ERR] 角色卡点
2025-05-04 21:32:00.379 +08:00 [ERR] 角色卡点
2025-05-04 21:32:00.612 +08:00 [ERR] 角色卡点
2025-05-04 21:32:00.846 +08:00 [ERR] 角色卡点
2025-05-04 21:32:01.081 +08:00 [ERR] 角色卡点
2025-05-04 21:32:01.278 +08:00 [ERR] 角色卡点
2025-05-04 21:32:01.512 +08:00 [ERR] 角色卡点
2025-05-04 21:32:01.712 +08:00 [ERR] 角色卡点
2025-05-04 21:32:01.945 +08:00 [ERR] 角色卡点
2025-05-04 21:32:02.179 +08:00 [ERR] 角色卡点
2025-05-04 21:32:02.411 +08:00 [ERR] 角色卡点
2025-05-04 21:32:02.645 +08:00 [ERR] 角色卡点
2025-05-04 21:32:02.912 +08:00 [ERR] 角色卡点
2025-05-04 21:32:03.145 +08:00 [ERR] 角色卡点
2025-05-04 21:32:03.379 +08:00 [ERR] 角色卡点
2025-05-04 21:32:03.645 +08:00 [ERR] 角色卡点
2025-05-04 21:32:03.844 +08:00 [ERR] 角色卡点
2025-05-04 21:32:04.079 +08:00 [ERR] 角色卡点
2025-05-04 21:32:04.312 +08:00 [ERR] 角色卡点
2025-05-04 21:32:04.546 +08:00 [ERR] 角色卡点
2025-05-04 21:32:04.780 +08:00 [ERR] 角色卡点
2025-05-04 21:32:05.012 +08:00 [ERR] 角色卡点
2025-05-04 21:32:07.578 +08:00 [ERR] 角色卡点
2025-05-04 21:32:07.612 +08:00 [INF] 翻滚
2025-05-04 21:32:07.778 +08:00 [ERR] 角色卡点
2025-05-04 21:32:07.813 +08:00 [INF] 翻滚
2025-05-04 21:32:08.012 +08:00 [ERR] 角色卡点
2025-05-04 21:32:08.046 +08:00 [INF] 翻滚
2025-05-04 21:32:08.245 +08:00 [ERR] 角色卡点
2025-05-04 21:32:08.479 +08:00 [ERR] 角色卡点
2025-05-04 21:32:08.679 +08:00 [ERR] 角色卡点
2025-05-04 21:32:08.878 +08:00 [ERR] 角色卡点
2025-05-04 21:32:21.646 +08:00 [ERR] 角色卡点
2025-05-04 21:32:21.844 +08:00 [ERR] 角色卡点
2025-05-04 21:32:22.078 +08:00 [ERR] 角色卡点
2025-05-04 21:32:22.312 +08:00 [ERR] 角色卡点
2025-05-04 21:32:22.511 +08:00 [ERR] 角色卡点
2025-05-04 21:32:22.725 +08:00 [ERR] 角色卡点
2025-05-04 21:32:22.946 +08:00 [ERR] 角色卡点
2025-05-04 21:32:23.179 +08:00 [ERR] 角色卡点
2025-05-04 21:32:23.412 +08:00 [ERR] 角色卡点
2025-05-04 21:32:23.646 +08:00 [ERR] 角色卡点
2025-05-04 21:32:23.878 +08:00 [ERR] 角色卡点
2025-05-04 21:32:23.878 +08:00 [INF] 翻滚
2025-05-04 21:32:24.111 +08:00 [ERR] 角色卡点
2025-05-04 21:32:24.111 +08:00 [INF] 翻滚
2025-05-04 21:32:24.343 +08:00 [ERR] 角色卡点
2025-05-04 21:32:24.512 +08:00 [ERR] 角色卡点
2025-05-04 21:32:24.747 +08:00 [ERR] 角色卡点
2025-05-04 21:32:24.946 +08:00 [ERR] 角色卡点
2025-05-04 21:32:25.180 +08:00 [ERR] 角色卡点
2025-05-04 21:32:25.413 +08:00 [ERR] 角色卡点
2025-05-04 21:32:25.646 +08:00 [ERR] 角色卡点
2025-05-04 21:32:25.847 +08:00 [ERR] 角色卡点
2025-05-04 21:32:26.046 +08:00 [ERR] 角色卡点
2025-05-04 21:32:26.245 +08:00 [ERR] 角色卡点
2025-05-04 21:32:26.445 +08:00 [ERR] 角色卡点
2025-05-04 21:32:26.646 +08:00 [ERR] 角色卡点
2025-05-04 21:32:26.882 +08:00 [ERR] 角色卡点
2025-05-04 21:32:27.112 +08:00 [ERR] 角色卡点
2025-05-04 21:32:27.345 +08:00 [ERR] 角色卡点
2025-05-04 21:32:27.345 +08:00 [INF] 翻滚
2025-05-04 21:32:27.579 +08:00 [ERR] 角色卡点
2025-05-04 21:32:27.579 +08:00 [INF] 翻滚
2025-05-04 21:32:27.812 +08:00 [ERR] 角色卡点
2025-05-04 21:32:27.846 +08:00 [INF] 翻滚
2025-05-04 21:32:28.046 +08:00 [ERR] 角色卡点
2025-05-04 21:32:28.279 +08:00 [ERR] 角色卡点
2025-05-04 21:32:28.545 +08:00 [ERR] 角色卡点
2025-05-04 21:32:28.745 +08:00 [ERR] 角色卡点
2025-05-04 21:32:28.946 +08:00 [ERR] 角色卡点
2025-05-04 21:32:29.180 +08:00 [ERR] 角色卡点
2025-05-04 21:32:29.412 +08:00 [ERR] 角色卡点
2025-05-04 21:32:29.647 +08:00 [ERR] 角色卡点
2025-05-04 21:32:29.879 +08:00 [ERR] 角色卡点
2025-05-04 21:32:30.079 +08:00 [ERR] 角色卡点
2025-05-04 21:32:30.312 +08:00 [ERR] 角色卡点
2025-05-04 21:32:30.545 +08:00 [ERR] 角色卡点
2025-05-04 21:32:30.747 +08:00 [ERR] 角色卡点
2025-05-04 21:32:30.946 +08:00 [ERR] 角色卡点
2025-05-04 21:32:31.146 +08:00 [ERR] 角色卡点
2025-05-04 21:32:31.345 +08:00 [ERR] 角色卡点
2025-05-04 21:32:31.546 +08:00 [ERR] 角色卡点
2025-05-04 21:32:31.746 +08:00 [ERR] 角色卡点
2025-05-04 21:32:31.979 +08:00 [ERR] 角色卡点
2025-05-04 21:32:32.212 +08:00 [ERR] 角色卡点
2025-05-04 21:32:32.446 +08:00 [ERR] 角色卡点
2025-05-04 21:32:32.645 +08:00 [ERR] 角色卡点
2025-05-04 21:32:32.846 +08:00 [ERR] 角色卡点
2025-05-04 21:32:33.046 +08:00 [ERR] 角色卡点
2025-05-04 21:32:33.279 +08:00 [ERR] 角色卡点
2025-05-04 21:32:33.513 +08:00 [ERR] 角色卡点
2025-05-04 21:32:33.546 +08:00 [INF] 翻滚
2025-05-04 21:32:33.746 +08:00 [ERR] 角色卡点
2025-05-04 21:32:33.746 +08:00 [INF] 翻滚
2025-05-04 21:32:33.946 +08:00 [ERR] 角色卡点
2025-05-04 21:32:34.179 +08:00 [ERR] 角色卡点
2025-05-04 21:32:34.413 +08:00 [ERR] 角色卡点
2025-05-04 21:32:34.645 +08:00 [ERR] 角色卡点
2025-05-04 21:32:34.879 +08:00 [ERR] 角色卡点
2025-05-04 21:32:35.112 +08:00 [ERR] 角色卡点
2025-05-04 21:32:41.045 +08:00 [ERR] 角色卡点
2025-05-04 21:32:41.279 +08:00 [ERR] 角色卡点
2025-05-04 21:32:41.512 +08:00 [ERR] 角色卡点
2025-05-04 21:32:41.745 +08:00 [ERR] 角色卡点
2025-05-04 21:32:41.979 +08:00 [ERR] 角色卡点
2025-05-04 21:32:42.012 +08:00 [INF] 翻滚
2025-05-04 21:32:42.212 +08:00 [ERR] 角色卡点
2025-05-04 21:32:42.246 +08:00 [INF] 翻滚
2025-05-04 21:32:42.446 +08:00 [ERR] 角色卡点
2025-05-04 21:32:42.478 +08:00 [INF] 翻滚
2025-05-04 21:32:42.678 +08:00 [ERR] 角色卡点
2025-05-04 21:32:42.726 +08:00 [INF] 翻滚
2025-05-04 21:32:42.913 +08:00 [ERR] 角色卡点
2025-05-04 21:32:42.946 +08:00 [INF] 翻滚
2025-05-04 21:32:43.145 +08:00 [ERR] 角色卡点
2025-05-04 21:32:43.178 +08:00 [INF] 翻滚
2025-05-04 21:32:43.379 +08:00 [ERR] 角色卡点
2025-05-04 21:32:43.413 +08:00 [INF] 翻滚
2025-05-04 21:32:43.613 +08:00 [ERR] 角色卡点
2025-05-04 21:32:43.646 +08:00 [INF] 翻滚
2025-05-04 21:32:43.845 +08:00 [ERR] 角色卡点
2025-05-04 21:32:44.078 +08:00 [ERR] 角色卡点
2025-05-04 21:32:44.313 +08:00 [ERR] 角色卡点
2025-05-04 21:32:44.547 +08:00 [ERR] 角色卡点
2025-05-04 21:32:44.779 +08:00 [ERR] 角色卡点
2025-05-04 21:32:45.011 +08:00 [ERR] 角色卡点
2025-05-04 21:32:45.245 +08:00 [ERR] 角色卡点
2025-05-04 21:32:45.479 +08:00 [ERR] 角色卡点
2025-05-04 21:32:45.712 +08:00 [ERR] 角色卡点
2025-05-04 21:32:45.912 +08:00 [ERR] 角色卡点
2025-05-04 21:32:46.152 +08:00 [ERR] 角色卡点
2025-05-04 21:32:46.378 +08:00 [ERR] 角色卡点
2025-05-04 21:32:46.612 +08:00 [ERR] 角色卡点
2025-05-04 21:32:46.878 +08:00 [ERR] 角色卡点
2025-05-04 21:32:47.079 +08:00 [ERR] 角色卡点
2025-05-04 21:32:47.312 +08:00 [ERR] 角色卡点
2025-05-04 21:32:47.511 +08:00 [ERR] 角色卡点
2025-05-04 21:32:47.746 +08:00 [ERR] 角色卡点
2025-05-04 21:32:47.978 +08:00 [ERR] 角色卡点
2025-05-04 21:32:48.212 +08:00 [ERR] 角色卡点
2025-05-04 21:32:48.446 +08:00 [ERR] 角色卡点
2025-05-04 21:32:48.679 +08:00 [ERR] 角色卡点
2025-05-04 21:32:48.913 +08:00 [ERR] 角色卡点
2025-05-04 21:32:49.145 +08:00 [ERR] 角色卡点
2025-05-04 21:32:49.378 +08:00 [ERR] 角色卡点
2025-05-04 21:32:49.612 +08:00 [ERR] 角色卡点
2025-05-04 21:32:49.846 +08:00 [ERR] 角色卡点
2025-05-04 21:32:50.078 +08:00 [ERR] 角色卡点
2025-05-04 21:32:50.312 +08:00 [ERR] 角色卡点
2025-05-04 21:32:50.545 +08:00 [ERR] 角色卡点
2025-05-04 21:32:50.779 +08:00 [ERR] 角色卡点
2025-05-04 21:32:50.911 +08:00 [INF] Resize from: {X=0,Y=0,Width=800,Height=600} to {X=8,Y=31,Width=784,Height=561}
2025-05-04 21:32:51.211 +08:00 [INF] Resize from: {X=8,Y=31,Width=784,Height=561} to {X=8,Y=31,Width=784,Height=521}
2025-05-04 21:32:52.111 +08:00 [INF] Resize from: {X=8,Y=31,Width=784,Height=521} to {X=9,Y=31,Width=784,Height=521}
2025-05-04 21:32:53.733 +08:00 [ERR] 角色卡点
2025-05-04 21:32:53.878 +08:00 [ERR] 角色卡点
2025-05-04 21:32:54.079 +08:00 [ERR] 角色卡点
2025-05-04 21:32:54.312 +08:00 [ERR] 角色卡点
2025-05-04 21:32:54.546 +08:00 [ERR] 角色卡点
2025-05-04 21:32:54.779 +08:00 [ERR] 角色卡点
2025-05-04 21:32:55.012 +08:00 [ERR] 角色卡点
2025-05-04 21:32:55.245 +08:00 [ERR] 角色卡点
2025-05-04 21:32:55.446 +08:00 [INF] Resize from: {X=9,Y=31,Width=784,Height=521} to {X=1,Y=0,Width=800,Height=560}
2025-05-04 21:32:57.245 +08:00 [ERR] 角色卡点
2025-05-04 21:32:57.545 +08:00 [ERR] 角色卡点
2025-05-04 21:32:57.746 +08:00 [ERR] 角色卡点
2025-05-04 21:32:57.979 +08:00 [ERR] 角色卡点
2025-05-04 21:32:58.212 +08:00 [ERR] 角色卡点
2025-05-04 21:32:58.446 +08:00 [ERR] 角色卡点
2025-05-04 21:32:58.779 +08:00 [ERR] 角色卡点
2025-05-04 21:32:59.012 +08:00 [ERR] 角色卡点
2025-05-04 21:32:59.244 +08:00 [ERR] 角色卡点
2025-05-04 21:32:59.478 +08:00 [ERR] 角色卡点
2025-05-04 21:32:59.711 +08:00 [ERR] 角色卡点
2025-05-04 21:32:59.945 +08:00 [ERR] 角色卡点
2025-05-04 21:33:00.179 +08:00 [ERR] 角色卡点
2025-05-04 21:33:00.412 +08:00 [ERR] 角色卡点
2025-05-04 21:33:00.646 +08:00 [ERR] 角色卡点
2025-05-04 21:33:00.878 +08:00 [ERR] 角色卡点
2025-05-04 21:33:01.179 +08:00 [INF] Resize from: {X=1,Y=0,Width=800,Height=560} to {X=8,Y=31,Width=784,Height=521}
2025-05-04 21:33:01.479 +08:00 [INF] Resize from: {X=8,Y=31,Width=784,Height=521} to {X=9,Y=31,Width=784,Height=521}
2025-05-04 21:33:02.090 +08:00 [ERR] 角色卡点
2025-05-04 21:33:02.212 +08:00 [ERR] 角色卡点
2025-05-04 21:33:02.444 +08:00 [ERR] 角色卡点
2025-05-04 21:33:02.678 +08:00 [ERR] 角色卡点
2025-05-04 21:33:02.912 +08:00 [ERR] 角色卡点
2025-05-04 21:33:03.112 +08:00 [ERR] 角色卡点
2025-05-04 21:33:03.312 +08:00 [ERR] 角色卡点
2025-05-04 21:33:03.544 +08:00 [ERR] 角色卡点
2025-05-04 21:33:03.778 +08:00 [ERR] 角色卡点
2025-05-04 21:33:04.013 +08:00 [ERR] 角色卡点
2025-05-04 21:33:04.013 +08:00 [INF] 翻滚
2025-05-04 21:33:04.245 +08:00 [ERR] 角色卡点
2025-05-04 21:33:04.245 +08:00 [INF] 翻滚
2025-05-04 21:33:04.478 +08:00 [ERR] 角色卡点
2025-05-04 21:33:04.711 +08:00 [ERR] 角色卡点
2025-05-04 21:33:04.945 +08:00 [ERR] 角色卡点
2025-05-04 21:33:05.179 +08:00 [ERR] 角色卡点
2025-05-04 21:33:05.412 +08:00 [ERR] 角色卡点
2025-05-04 21:33:05.646 +08:00 [ERR] 角色卡点
2025-05-04 21:33:05.878 +08:00 [ERR] 角色卡点
2025-05-04 21:33:06.111 +08:00 [ERR] 角色卡点
2025-05-04 21:33:06.345 +08:00 [ERR] 角色卡点
2025-05-04 21:33:06.579 +08:00 [ERR] 角色卡点
2025-05-04 21:33:06.579 +08:00 [INF] 翻滚
2025-05-04 21:33:06.813 +08:00 [ERR] 角色卡点
2025-05-04 21:33:06.813 +08:00 [INF] 翻滚
2025-05-04 21:33:07.045 +08:00 [ERR] 角色卡点
2025-05-04 21:33:07.045 +08:00 [INF] 翻滚
2025-05-04 21:33:07.278 +08:00 [ERR] 角色卡点
2025-05-04 21:33:07.278 +08:00 [INF] 翻滚
2025-05-04 21:33:40.231 +08:00 [ERR] Core -> Loading SoundController failed.
2025-05-04 21:33:40.284 +08:00 [ERR] Core -> SharpDX.SharpDXException: HRESULT: [0x80070490], Module: [Unknown], ApiCode: [Unknown/Unknown], Message: 找不到元素。

   在 SharpDX.Result.CheckError()
   在 SharpDX.XAudio2.XAudio2.CreateMasteringVoice(MasteringVoice masteringVoiceOut, Int32 inputChannels, Int32 inputSampleRate, Int32 flags, String szDeviceId, Nullable`1 effectChainRef, AudioStreamCategory streamCategory)
   在 SharpDX.XAudio2.MasteringVoice..ctor(XAudio2 device, Int32 inputChannels, Int32 inputSampleRate)
   在 ExileCore.SoundController..ctor(String dir)
   在 ExileCore.Core..ctor(RenderForm form)
2025-05-04 21:33:40.284 +08:00 [INF] 模块: Atlas Helper -> : Time: 35.7002 ms. 地址:[937344] Started searching offset with:0
2025-05-04 21:33:40.284 +08:00 [INF] 模块: Area change -> : Time: 37.6835 ms. 地址:[1037465] Started searching offset with:0
2025-05-04 21:33:40.284 +08:00 [INF] 模块: Game State -> : Time: 37.7025 ms. 地址:[934055] Started searching offset with:0
2025-05-04 21:33:40.284 +08:00 [INF] 模块: BlackBarSize -> : Time: 169.358 ms. 地址:[4510023] Started searching offset with:0
2025-05-04 21:33:40.284 +08:00 [INF] 模块: Terrain Rotator Helper -> : Time: 935.441 ms. 地址:[25114927] Started searching offset with:0
2025-05-04 21:33:40.284 +08:00 [INF] 模块: Terrain Rotation Selector -> : Time: 938.6577 ms. 地址:[25114927] Started searching offset with:0
2025-05-04 21:33:40.284 +08:00 [INF] 模块: File Root -> : Time: 1195.004 ms. 地址:[29539156] Started searching offset with:0
2025-05-04 21:33:40.284 +08:00 [INF] 模块: DiagnosticInfoType -> : Time: 2824.4556 ms. 地址:[0] Started searching offset with:600000
2025-05-04 21:33:40.284 +08:00 [INF] 初始化用时 7483.5508 毫秒.
2025-05-04 21:33:40.284 +08:00 [INF] Resize from: {X=0,Y=0,Width=804,Height=581} to {X=8,Y=31,Width=784,Height=521}
2025-05-04 21:33:40.339 +08:00 [INF] DD loaded in 134.7122 ms.
2025-05-04 21:33:40.339 +08:00 [ERR] 线程1启动
2025-05-04 21:33:40.339 +08:00 [ERR] 线程2启动
2025-05-04 21:33:40.339 +08:00 [INF] 监测线程启动
2025-05-04 21:33:40.339 +08:00 [INF] 子目录列表：
2025-05-04 21:33:40.339 +08:00 [INF] 冰之打击武僧
2025-05-04 21:33:40.403 +08:00 [INF] 召唤
2025-05-04 21:33:40.403 +08:00 [INF] 召唤弓箭手
2025-05-04 21:33:40.403 +08:00 [INF] 囚神僧作者自己用的BD
2025-05-04 21:33:40.403 +08:00 [INF] 囚神杖武僧
2025-05-04 21:33:40.403 +08:00 [INF] 恶魔受伤释放
2025-05-04 21:33:40.403 +08:00 [INF] 恶魔受伤释放双祈愿
2025-05-04 21:33:40.403 +08:00 [INF] 游侠
2025-05-04 21:33:40.403 +08:00 [INF] 电武僧
2025-05-04 21:33:40.403 +08:00 [INF] 电球
2025-05-04 21:33:40.403 +08:00 [INF] 自己用
2025-05-04 21:33:40.403 +08:00 [INF] 闪电
2025-05-04 21:33:40.403 +08:00 [INF] 闪电箭
2025-05-04 21:33:40.403 +08:00 [INF] 阴抓BD模板
2025-05-04 21:33:40.403 +08:00 [INF] 陰爪BD先取消走A
2025-05-04 21:33:40.436 +08:00 [INF] 骑鸟电矛
2025-05-04 21:33:40.436 +08:00 [INF] DD -> 初始化用时: 85.7398 毫秒.
2025-05-04 21:33:40.436 +08:00 [INF] 加载地图数据耗时：2
2025-05-04 21:33:40.470 +08:00 [INF] 当前地图： (0)  N：-1 T：0
2025-05-04 21:34:30.003 +08:00 [ERR] Core -> Loading SoundController failed.
2025-05-04 21:34:30.052 +08:00 [ERR] Core -> SharpDX.SharpDXException: HRESULT: [0x80070490], Module: [Unknown], ApiCode: [Unknown/Unknown], Message: 找不到元素。

   在 SharpDX.Result.CheckError()
   在 SharpDX.XAudio2.XAudio2.CreateMasteringVoice(MasteringVoice masteringVoiceOut, Int32 inputChannels, Int32 inputSampleRate, Int32 flags, String szDeviceId, Nullable`1 effectChainRef, AudioStreamCategory streamCategory)
   在 SharpDX.XAudio2.MasteringVoice..ctor(XAudio2 device, Int32 inputChannels, Int32 inputSampleRate)
   在 ExileCore.SoundController..ctor(String dir)
   在 ExileCore.Core..ctor(RenderForm form)
2025-05-04 21:34:30.052 +08:00 [INF] 模块: Game State -> : Time: 37.3074 ms. 地址:[934055] Started searching offset with:0
2025-05-04 21:34:30.052 +08:00 [INF] 模块: Area change -> : Time: 38.5432 ms. 地址:[1037465] Started searching offset with:0
2025-05-04 21:34:30.052 +08:00 [INF] 模块: Atlas Helper -> : Time: 65.2287 ms. 地址:[937344] Started searching offset with:0
2025-05-04 21:34:30.052 +08:00 [INF] 模块: BlackBarSize -> : Time: 193.9488 ms. 地址:[4510023] Started searching offset with:0
2025-05-04 21:34:30.052 +08:00 [INF] 模块: Terrain Rotator Helper -> : Time: 959.0809 ms. 地址:[25114927] Started searching offset with:0
2025-05-04 21:34:30.052 +08:00 [INF] 模块: Terrain Rotation Selector -> : Time: 965.445 ms. 地址:[25114927] Started searching offset with:0
2025-05-04 21:34:30.052 +08:00 [INF] 模块: File Root -> : Time: 1205.4544 ms. 地址:[29539156] Started searching offset with:0
2025-05-04 21:34:30.052 +08:00 [INF] 模块: DiagnosticInfoType -> : Time: 2662.7267 ms. 地址:[0] Started searching offset with:600000
2025-05-04 21:34:30.052 +08:00 [INF] 初始化用时 7228.7555 毫秒.
2025-05-04 21:34:30.103 +08:00 [INF] DD loaded in 130.6078 ms.
2025-05-04 21:34:30.105 +08:00 [ERR] 线程1启动
2025-05-04 21:34:30.106 +08:00 [ERR] 线程2启动
2025-05-04 21:34:30.108 +08:00 [INF] 监测线程启动
2025-05-04 21:34:30.121 +08:00 [INF] 子目录列表：
2025-05-04 21:34:30.121 +08:00 [INF] 冰之打击武僧
2025-05-04 21:34:30.188 +08:00 [INF] 召唤
2025-05-04 21:34:30.188 +08:00 [INF] 召唤弓箭手
2025-05-04 21:34:30.188 +08:00 [INF] 囚神僧作者自己用的BD
2025-05-04 21:34:30.188 +08:00 [INF] 囚神杖武僧
2025-05-04 21:34:30.188 +08:00 [INF] 恶魔受伤释放
2025-05-04 21:34:30.188 +08:00 [INF] 恶魔受伤释放双祈愿
2025-05-04 21:34:30.188 +08:00 [INF] 游侠
2025-05-04 21:34:30.188 +08:00 [INF] 电武僧
2025-05-04 21:34:30.188 +08:00 [INF] 电球
2025-05-04 21:34:30.188 +08:00 [INF] 自己用
2025-05-04 21:34:30.188 +08:00 [INF] 闪电
2025-05-04 21:34:30.188 +08:00 [INF] 闪电箭
2025-05-04 21:34:30.188 +08:00 [INF] 阴抓BD模板
2025-05-04 21:34:30.188 +08:00 [INF] 陰爪BD先取消走A
2025-05-04 21:34:30.188 +08:00 [INF] 骑鸟电矛
2025-05-04 21:34:30.188 +08:00 [INF] DD -> 初始化用时: 83.2366 毫秒.
2025-05-04 21:34:30.226 +08:00 [INF] 加载地图数据耗时：2
2025-05-04 21:34:30.226 +08:00 [INF] 当前地图： (0)  N：-1 T：0
2025-05-04 21:35:08.703 +08:00 [INF] Resize from: {X=0,Y=0,Width=804,Height=581} to {X=8,Y=31,Width=800,Height=600}
2025-05-04 21:35:28.837 +08:00 [ERR] Core -> Loading SoundController failed.
2025-05-04 21:35:28.879 +08:00 [ERR] Core -> SharpDX.SharpDXException: HRESULT: [0x80070490], Module: [Unknown], ApiCode: [Unknown/Unknown], Message: 找不到元素。

   在 SharpDX.Result.CheckError()
   在 SharpDX.XAudio2.XAudio2.CreateMasteringVoice(MasteringVoice masteringVoiceOut, Int32 inputChannels, Int32 inputSampleRate, Int32 flags, String szDeviceId, Nullable`1 effectChainRef, AudioStreamCategory streamCategory)
   在 SharpDX.XAudio2.MasteringVoice..ctor(XAudio2 device, Int32 inputChannels, Int32 inputSampleRate)
   在 ExileCore.SoundController..ctor(String dir)
   在 ExileCore.Core..ctor(RenderForm form)
2025-05-04 21:35:28.879 +08:00 [INF] 模块: Atlas Helper -> : Time: 34.9071 ms. 地址:[937344] Started searching offset with:0
2025-05-04 21:35:28.879 +08:00 [INF] 模块: Game State -> : Time: 36.5587 ms. 地址:[934055] Started searching offset with:0
2025-05-04 21:35:28.879 +08:00 [INF] 模块: Area change -> : Time: 37.027 ms. 地址:[1037465] Started searching offset with:0
2025-05-04 21:35:28.879 +08:00 [INF] 模块: BlackBarSize -> : Time: 156.5249 ms. 地址:[4510023] Started searching offset with:0
2025-05-04 21:35:28.879 +08:00 [INF] 模块: Terrain Rotation Selector -> : Time: 896.3092 ms. 地址:[25114927] Started searching offset with:0
2025-05-04 21:35:28.879 +08:00 [INF] 模块: Terrain Rotator Helper -> : Time: 906.7867 ms. 地址:[25114927] Started searching offset with:0
2025-05-04 21:35:28.879 +08:00 [INF] 模块: File Root -> : Time: 1154.3254 ms. 地址:[29539156] Started searching offset with:0
2025-05-04 21:35:28.879 +08:00 [INF] 模块: DiagnosticInfoType -> : Time: 2447.3559 ms. 地址:[0] Started searching offset with:600000
2025-05-04 21:35:28.879 +08:00 [ERR] System.AggregateException: 发生一个或多个错误。 ---> System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)
   --- 内部异常堆栈跟踪的结尾 ---
   在 System.Threading.Tasks.Task.ThrowIfExceptional(Boolean includeTaskCanceledExceptions)
   在 System.Threading.Tasks.Task.Wait(Int32 millisecondsTimeout, CancellationToken cancellationToken)
   在 System.Threading.Tasks.Parallel.ForWorker[TLocal](Int32 fromInclusive, Int32 toExclusive, ParallelOptions parallelOptions, Action`1 body, Action`2 bodyWithState, Func`4 bodyWithLocal, Func`1 localInit, Action`1 localFinally)
   在 System.Threading.Tasks.Parallel.For(Int32 fromInclusive, Int32 toExclusive, Action`1 body)
   在 ExileCore.PoEMemory.FilesFromMemory.GetAllFiles()
   在 ExileCore.PoEMemory.FilesContainer.ReloadFiles()
   在 ExileCore.PoEMemory.FilesContainer..ctor(IMemory memory)
   在 ExileCore.PoEMemory.MemoryObjects.TheGame..ctor(IMemory m, Cache cache, CoreSettings settings)
   在 ExileCore.GameController..ctor(Memory memory, SoundController soundController, SettingsContainer settings, MultiThreadManager multiThreadManager)
---> (内部异常 #0) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #1) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #2) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #3) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #4) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #5) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #6) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #7) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #8) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #9) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #10) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #11) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #12) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #13) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #14) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #15) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

2025-05-04 21:35:28.879 +08:00 [ERR] Inject -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.GameController..ctor(Memory memory, SoundController soundController, SettingsContainer settings, MultiThreadManager multiThreadManager)
   在 ExileCore.Core.Inject()
2025-05-04 21:35:28.880 +08:00 [INF] 初始化用时 6106.1442 毫秒.
2025-05-04 21:35:28.880 +08:00 [ERR] System.AggregateException: 发生一个或多个错误。 ---> System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)
   --- 内部异常堆栈跟踪的结尾 ---
   在 System.Threading.Tasks.Task.ThrowIfExceptional(Boolean includeTaskCanceledExceptions)
   在 System.Threading.Tasks.Task.Wait(Int32 millisecondsTimeout, CancellationToken cancellationToken)
   在 System.Threading.Tasks.Parallel.ForWorker[TLocal](Int32 fromInclusive, Int32 toExclusive, ParallelOptions parallelOptions, Action`1 body, Action`2 bodyWithState, Func`4 bodyWithLocal, Func`1 localInit, Action`1 localFinally)
   在 System.Threading.Tasks.Parallel.For(Int32 fromInclusive, Int32 toExclusive, Action`1 body)
   在 ExileCore.PoEMemory.FilesFromMemory.GetAllFiles()
   在 ExileCore.PoEMemory.FilesContainer.ReloadFiles()
   在 ExileCore.PoEMemory.FilesContainer..ctor(IMemory memory)
   在 ExileCore.PoEMemory.MemoryObjects.TheGame..ctor(IMemory m, Cache cache, CoreSettings settings)
   在 ExileCore.GameController..ctor(Memory memory, SoundController soundController, SettingsContainer settings, MultiThreadManager multiThreadManager)
---> (内部异常 #0) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #1) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

2025-05-04 21:35:30.798 +08:00 [ERR] System.AggregateException: 发生一个或多个错误。 ---> System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)
   --- 内部异常堆栈跟踪的结尾 ---
   在 System.Threading.Tasks.Task.ThrowIfExceptional(Boolean includeTaskCanceledExceptions)
   在 System.Threading.Tasks.Task.Wait(Int32 millisecondsTimeout, CancellationToken cancellationToken)
   在 System.Threading.Tasks.Parallel.ForWorker[TLocal](Int32 fromInclusive, Int32 toExclusive, ParallelOptions parallelOptions, Action`1 body, Action`2 bodyWithState, Func`4 bodyWithLocal, Func`1 localInit, Action`1 localFinally)
   在 System.Threading.Tasks.Parallel.For(Int32 fromInclusive, Int32 toExclusive, Action`1 body)
   在 ExileCore.PoEMemory.FilesFromMemory.GetAllFiles()
   在 ExileCore.PoEMemory.FilesContainer.ReloadFiles()
   在 ExileCore.PoEMemory.FilesContainer..ctor(IMemory memory)
   在 ExileCore.PoEMemory.MemoryObjects.TheGame..ctor(IMemory m, Cache cache, CoreSettings settings)
   在 ExileCore.GameController..ctor(Memory memory, SoundController soundController, SettingsContainer settings, MultiThreadManager multiThreadManager)
---> (内部异常 #0) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #1) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #2) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

2025-05-04 21:35:30.798 +08:00 [ERR] Inject -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.GameController..ctor(Memory memory, SoundController soundController, SettingsContainer settings, MultiThreadManager multiThreadManager)
   在 ExileCore.Core.Inject()
2025-05-04 21:35:32.864 +08:00 [ERR] System.AggregateException: 发生一个或多个错误。 ---> System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)
   --- 内部异常堆栈跟踪的结尾 ---
   在 System.Threading.Tasks.Task.ThrowIfExceptional(Boolean includeTaskCanceledExceptions)
   在 System.Threading.Tasks.Task.Wait(Int32 millisecondsTimeout, CancellationToken cancellationToken)
   在 System.Threading.Tasks.Parallel.ForWorker[TLocal](Int32 fromInclusive, Int32 toExclusive, ParallelOptions parallelOptions, Action`1 body, Action`2 bodyWithState, Func`4 bodyWithLocal, Func`1 localInit, Action`1 localFinally)
   在 System.Threading.Tasks.Parallel.For(Int32 fromInclusive, Int32 toExclusive, Action`1 body)
   在 ExileCore.PoEMemory.FilesFromMemory.GetAllFiles()
   在 ExileCore.PoEMemory.FilesContainer.ReloadFiles()
   在 ExileCore.PoEMemory.FilesContainer..ctor(IMemory memory)
   在 ExileCore.PoEMemory.MemoryObjects.TheGame..ctor(IMemory m, Cache cache, CoreSettings settings)
   在 ExileCore.GameController..ctor(Memory memory, SoundController soundController, SettingsContainer settings, MultiThreadManager multiThreadManager)
---> (内部异常 #0) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #1) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #2) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

2025-05-04 21:35:32.864 +08:00 [ERR] Inject -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.GameController..ctor(Memory memory, SoundController soundController, SettingsContainer settings, MultiThreadManager multiThreadManager)
   在 ExileCore.Core.Inject()
2025-05-04 21:35:34.899 +08:00 [ERR] System.AggregateException: 发生一个或多个错误。 ---> System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)
   --- 内部异常堆栈跟踪的结尾 ---
   在 System.Threading.Tasks.Task.ThrowIfExceptional(Boolean includeTaskCanceledExceptions)
   在 System.Threading.Tasks.Task.Wait(Int32 millisecondsTimeout, CancellationToken cancellationToken)
   在 System.Threading.Tasks.Parallel.ForWorker[TLocal](Int32 fromInclusive, Int32 toExclusive, ParallelOptions parallelOptions, Action`1 body, Action`2 bodyWithState, Func`4 bodyWithLocal, Func`1 localInit, Action`1 localFinally)
   在 System.Threading.Tasks.Parallel.For(Int32 fromInclusive, Int32 toExclusive, Action`1 body)
   在 ExileCore.PoEMemory.FilesFromMemory.GetAllFiles()
   在 ExileCore.PoEMemory.FilesContainer.ReloadFiles()
   在 ExileCore.PoEMemory.FilesContainer..ctor(IMemory memory)
   在 ExileCore.PoEMemory.MemoryObjects.TheGame..ctor(IMemory m, Cache cache, CoreSettings settings)
   在 ExileCore.GameController..ctor(Memory memory, SoundController soundController, SettingsContainer settings, MultiThreadManager multiThreadManager)
---> (内部异常 #0) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #1) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #2) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #3) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #4) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

2025-05-04 21:35:34.899 +08:00 [ERR] Inject -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.GameController..ctor(Memory memory, SoundController soundController, SettingsContainer settings, MultiThreadManager multiThreadManager)
   在 ExileCore.Core.Inject()
2025-05-04 21:35:36.965 +08:00 [ERR] System.AggregateException: 发生一个或多个错误。 ---> System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)
   --- 内部异常堆栈跟踪的结尾 ---
   在 System.Threading.Tasks.Task.ThrowIfExceptional(Boolean includeTaskCanceledExceptions)
   在 System.Threading.Tasks.Task.Wait(Int32 millisecondsTimeout, CancellationToken cancellationToken)
   在 System.Threading.Tasks.Parallel.ForWorker[TLocal](Int32 fromInclusive, Int32 toExclusive, ParallelOptions parallelOptions, Action`1 body, Action`2 bodyWithState, Func`4 bodyWithLocal, Func`1 localInit, Action`1 localFinally)
   在 System.Threading.Tasks.Parallel.For(Int32 fromInclusive, Int32 toExclusive, Action`1 body)
   在 ExileCore.PoEMemory.FilesFromMemory.GetAllFiles()
   在 ExileCore.PoEMemory.FilesContainer.ReloadFiles()
   在 ExileCore.PoEMemory.FilesContainer..ctor(IMemory memory)
   在 ExileCore.PoEMemory.MemoryObjects.TheGame..ctor(IMemory m, Cache cache, CoreSettings settings)
   在 ExileCore.GameController..ctor(Memory memory, SoundController soundController, SettingsContainer settings, MultiThreadManager multiThreadManager)
---> (内部异常 #0) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #1) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #2) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #3) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

2025-05-04 21:35:36.965 +08:00 [ERR] Inject -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.GameController..ctor(Memory memory, SoundController soundController, SettingsContainer settings, MultiThreadManager multiThreadManager)
   在 ExileCore.Core.Inject()
2025-05-04 21:35:38.999 +08:00 [ERR] System.AggregateException: 发生一个或多个错误。 ---> System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)
   --- 内部异常堆栈跟踪的结尾 ---
   在 System.Threading.Tasks.Task.ThrowIfExceptional(Boolean includeTaskCanceledExceptions)
   在 System.Threading.Tasks.Task.Wait(Int32 millisecondsTimeout, CancellationToken cancellationToken)
   在 System.Threading.Tasks.Parallel.ForWorker[TLocal](Int32 fromInclusive, Int32 toExclusive, ParallelOptions parallelOptions, Action`1 body, Action`2 bodyWithState, Func`4 bodyWithLocal, Func`1 localInit, Action`1 localFinally)
   在 System.Threading.Tasks.Parallel.For(Int32 fromInclusive, Int32 toExclusive, Action`1 body)
   在 ExileCore.PoEMemory.FilesFromMemory.GetAllFiles()
   在 ExileCore.PoEMemory.FilesContainer.ReloadFiles()
   在 ExileCore.PoEMemory.FilesContainer..ctor(IMemory memory)
   在 ExileCore.PoEMemory.MemoryObjects.TheGame..ctor(IMemory m, Cache cache, CoreSettings settings)
   在 ExileCore.GameController..ctor(Memory memory, SoundController soundController, SettingsContainer settings, MultiThreadManager multiThreadManager)
---> (内部异常 #0) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #1) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #2) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #3) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

2025-05-04 21:35:38.999 +08:00 [ERR] Inject -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.GameController..ctor(Memory memory, SoundController soundController, SettingsContainer settings, MultiThreadManager multiThreadManager)
   在 ExileCore.Core.Inject()
2025-05-04 21:35:41.066 +08:00 [ERR] System.AggregateException: 发生一个或多个错误。 ---> System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)
   --- 内部异常堆栈跟踪的结尾 ---
   在 System.Threading.Tasks.Task.ThrowIfExceptional(Boolean includeTaskCanceledExceptions)
   在 System.Threading.Tasks.Task.Wait(Int32 millisecondsTimeout, CancellationToken cancellationToken)
   在 System.Threading.Tasks.Parallel.ForWorker[TLocal](Int32 fromInclusive, Int32 toExclusive, ParallelOptions parallelOptions, Action`1 body, Action`2 bodyWithState, Func`4 bodyWithLocal, Func`1 localInit, Action`1 localFinally)
   在 System.Threading.Tasks.Parallel.For(Int32 fromInclusive, Int32 toExclusive, Action`1 body)
   在 ExileCore.PoEMemory.FilesFromMemory.GetAllFiles()
   在 ExileCore.PoEMemory.FilesContainer.ReloadFiles()
   在 ExileCore.PoEMemory.FilesContainer..ctor(IMemory memory)
   在 ExileCore.PoEMemory.MemoryObjects.TheGame..ctor(IMemory m, Cache cache, CoreSettings settings)
   在 ExileCore.GameController..ctor(Memory memory, SoundController soundController, SettingsContainer settings, MultiThreadManager multiThreadManager)
---> (内部异常 #0) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #1) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #2) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #3) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #4) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #5) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

2025-05-04 21:35:41.066 +08:00 [ERR] Inject -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.GameController..ctor(Memory memory, SoundController soundController, SettingsContainer settings, MultiThreadManager multiThreadManager)
   在 ExileCore.Core.Inject()
2025-05-04 21:35:43.099 +08:00 [ERR] System.AggregateException: 发生一个或多个错误。 ---> System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)
   --- 内部异常堆栈跟踪的结尾 ---
   在 System.Threading.Tasks.Task.ThrowIfExceptional(Boolean includeTaskCanceledExceptions)
   在 System.Threading.Tasks.Task.Wait(Int32 millisecondsTimeout, CancellationToken cancellationToken)
   在 System.Threading.Tasks.Parallel.ForWorker[TLocal](Int32 fromInclusive, Int32 toExclusive, ParallelOptions parallelOptions, Action`1 body, Action`2 bodyWithState, Func`4 bodyWithLocal, Func`1 localInit, Action`1 localFinally)
   在 System.Threading.Tasks.Parallel.For(Int32 fromInclusive, Int32 toExclusive, Action`1 body)
   在 ExileCore.PoEMemory.FilesFromMemory.GetAllFiles()
   在 ExileCore.PoEMemory.FilesContainer.ReloadFiles()
   在 ExileCore.PoEMemory.FilesContainer..ctor(IMemory memory)
   在 ExileCore.PoEMemory.MemoryObjects.TheGame..ctor(IMemory m, Cache cache, CoreSettings settings)
   在 ExileCore.GameController..ctor(Memory memory, SoundController soundController, SettingsContainer settings, MultiThreadManager multiThreadManager)
---> (内部异常 #0) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #1) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #2) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #3) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

2025-05-04 21:35:43.099 +08:00 [ERR] Inject -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.GameController..ctor(Memory memory, SoundController soundController, SettingsContainer settings, MultiThreadManager multiThreadManager)
   在 ExileCore.Core.Inject()
2025-05-04 21:35:45.165 +08:00 [ERR] System.AggregateException: 发生一个或多个错误。 ---> System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)
   --- 内部异常堆栈跟踪的结尾 ---
   在 System.Threading.Tasks.Task.ThrowIfExceptional(Boolean includeTaskCanceledExceptions)
   在 System.Threading.Tasks.Task.Wait(Int32 millisecondsTimeout, CancellationToken cancellationToken)
   在 System.Threading.Tasks.Parallel.ForWorker[TLocal](Int32 fromInclusive, Int32 toExclusive, ParallelOptions parallelOptions, Action`1 body, Action`2 bodyWithState, Func`4 bodyWithLocal, Func`1 localInit, Action`1 localFinally)
   在 System.Threading.Tasks.Parallel.For(Int32 fromInclusive, Int32 toExclusive, Action`1 body)
   在 ExileCore.PoEMemory.FilesFromMemory.GetAllFiles()
   在 ExileCore.PoEMemory.FilesContainer.ReloadFiles()
   在 ExileCore.PoEMemory.FilesContainer..ctor(IMemory memory)
   在 ExileCore.PoEMemory.MemoryObjects.TheGame..ctor(IMemory m, Cache cache, CoreSettings settings)
   在 ExileCore.GameController..ctor(Memory memory, SoundController soundController, SettingsContainer settings, MultiThreadManager multiThreadManager)
---> (内部异常 #0) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #1) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #2) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #3) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

2025-05-04 21:35:45.165 +08:00 [ERR] Inject -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.GameController..ctor(Memory memory, SoundController soundController, SettingsContainer settings, MultiThreadManager multiThreadManager)
   在 ExileCore.Core.Inject()
2025-05-04 21:35:47.198 +08:00 [ERR] System.AggregateException: 发生一个或多个错误。 ---> System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)
   --- 内部异常堆栈跟踪的结尾 ---
   在 System.Threading.Tasks.Task.ThrowIfExceptional(Boolean includeTaskCanceledExceptions)
   在 System.Threading.Tasks.Task.Wait(Int32 millisecondsTimeout, CancellationToken cancellationToken)
   在 System.Threading.Tasks.Parallel.ForWorker[TLocal](Int32 fromInclusive, Int32 toExclusive, ParallelOptions parallelOptions, Action`1 body, Action`2 bodyWithState, Func`4 bodyWithLocal, Func`1 localInit, Action`1 localFinally)
   在 System.Threading.Tasks.Parallel.For(Int32 fromInclusive, Int32 toExclusive, Action`1 body)
   在 ExileCore.PoEMemory.FilesFromMemory.GetAllFiles()
   在 ExileCore.PoEMemory.FilesContainer.ReloadFiles()
   在 ExileCore.PoEMemory.FilesContainer..ctor(IMemory memory)
   在 ExileCore.PoEMemory.MemoryObjects.TheGame..ctor(IMemory m, Cache cache, CoreSettings settings)
   在 ExileCore.GameController..ctor(Memory memory, SoundController soundController, SettingsContainer settings, MultiThreadManager multiThreadManager)
---> (内部异常 #0) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #1) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #2) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #3) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

2025-05-04 21:35:47.198 +08:00 [ERR] Inject -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.GameController..ctor(Memory memory, SoundController soundController, SettingsContainer settings, MultiThreadManager multiThreadManager)
   在 ExileCore.Core.Inject()
2025-05-04 21:35:49.266 +08:00 [ERR] System.AggregateException: 发生一个或多个错误。 ---> System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)
   --- 内部异常堆栈跟踪的结尾 ---
   在 System.Threading.Tasks.Task.ThrowIfExceptional(Boolean includeTaskCanceledExceptions)
   在 System.Threading.Tasks.Task.Wait(Int32 millisecondsTimeout, CancellationToken cancellationToken)
   在 System.Threading.Tasks.Parallel.ForWorker[TLocal](Int32 fromInclusive, Int32 toExclusive, ParallelOptions parallelOptions, Action`1 body, Action`2 bodyWithState, Func`4 bodyWithLocal, Func`1 localInit, Action`1 localFinally)
   在 System.Threading.Tasks.Parallel.For(Int32 fromInclusive, Int32 toExclusive, Action`1 body)
   在 ExileCore.PoEMemory.FilesFromMemory.GetAllFiles()
   在 ExileCore.PoEMemory.FilesContainer.ReloadFiles()
   在 ExileCore.PoEMemory.FilesContainer..ctor(IMemory memory)
   在 ExileCore.PoEMemory.MemoryObjects.TheGame..ctor(IMemory m, Cache cache, CoreSettings settings)
   在 ExileCore.GameController..ctor(Memory memory, SoundController soundController, SettingsContainer settings, MultiThreadManager multiThreadManager)
---> (内部异常 #0) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #1) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #2) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #3) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #4) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #5) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #6) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

2025-05-04 21:35:49.266 +08:00 [ERR] Inject -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.GameController..ctor(Memory memory, SoundController soundController, SettingsContainer settings, MultiThreadManager multiThreadManager)
   在 ExileCore.Core.Inject()
2025-05-04 21:35:51.333 +08:00 [ERR] System.AggregateException: 发生一个或多个错误。 ---> System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)
   --- 内部异常堆栈跟踪的结尾 ---
   在 System.Threading.Tasks.Task.ThrowIfExceptional(Boolean includeTaskCanceledExceptions)
   在 System.Threading.Tasks.Task.Wait(Int32 millisecondsTimeout, CancellationToken cancellationToken)
   在 System.Threading.Tasks.Parallel.ForWorker[TLocal](Int32 fromInclusive, Int32 toExclusive, ParallelOptions parallelOptions, Action`1 body, Action`2 bodyWithState, Func`4 bodyWithLocal, Func`1 localInit, Action`1 localFinally)
   在 System.Threading.Tasks.Parallel.For(Int32 fromInclusive, Int32 toExclusive, Action`1 body)
   在 ExileCore.PoEMemory.FilesFromMemory.GetAllFiles()
   在 ExileCore.PoEMemory.FilesContainer.ReloadFiles()
   在 ExileCore.PoEMemory.FilesContainer..ctor(IMemory memory)
   在 ExileCore.PoEMemory.MemoryObjects.TheGame..ctor(IMemory m, Cache cache, CoreSettings settings)
   在 ExileCore.GameController..ctor(Memory memory, SoundController soundController, SettingsContainer settings, MultiThreadManager multiThreadManager)
---> (内部异常 #0) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #1) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #2) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #3) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #4) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

---> (内部异常 #5) System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: startIndex
   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.BitConverter.ToInt64(Byte[] value, Int32 startIndex)
   在 ExileCore.PoEMemory.FilesFromMemory.<>c__DisplayClass2_0.<GetAllFiles>b__0(Int32 i)
   在 System.Threading.Tasks.Parallel.<>c__DisplayClass17_0`1.<ForWorker>b__1()
   在 System.Threading.Tasks.Task.InnerInvokeWithArg(Task childTask)
   在 System.Threading.Tasks.Task.<>c__DisplayClass176_0.<ExecuteSelfReplicating>b__0(Object <p0>)<---

2025-05-04 21:35:51.333 +08:00 [ERR] Inject -> System.NullReferenceException: 未将对象引用设置到对象的实例。
   在 ExileCore.GameController..ctor(Memory memory, SoundController soundController, SettingsContainer settings, MultiThreadManager multiThreadManager)
   在 ExileCore.Core.Inject()
2025-05-04 21:36:25.514 +08:00 [ERR] Core -> Loading SoundController failed.
2025-05-04 21:36:25.567 +08:00 [ERR] Core -> SharpDX.SharpDXException: HRESULT: [0x80070490], Module: [Unknown], ApiCode: [Unknown/Unknown], Message: 找不到元素。

   在 SharpDX.Result.CheckError()
   在 SharpDX.XAudio2.XAudio2.CreateMasteringVoice(MasteringVoice masteringVoiceOut, Int32 inputChannels, Int32 inputSampleRate, Int32 flags, String szDeviceId, Nullable`1 effectChainRef, AudioStreamCategory streamCategory)
   在 SharpDX.XAudio2.MasteringVoice..ctor(XAudio2 device, Int32 inputChannels, Int32 inputSampleRate)
   在 ExileCore.SoundController..ctor(String dir)
   在 ExileCore.Core..ctor(RenderForm form)
2025-05-04 21:36:25.567 +08:00 [INF] 模块: Atlas Helper -> : Time: 36.2938 ms. 地址:[937344] Started searching offset with:0
2025-05-04 21:36:25.567 +08:00 [INF] 模块: Game State -> : Time: 37.2565 ms. 地址:[934055] Started searching offset with:0
2025-05-04 21:36:25.567 +08:00 [INF] 模块: Area change -> : Time: 42.7476 ms. 地址:[1037465] Started searching offset with:0
2025-05-04 21:36:25.567 +08:00 [INF] 模块: BlackBarSize -> : Time: 178.7449 ms. 地址:[4510023] Started searching offset with:0
2025-05-04 21:36:25.567 +08:00 [INF] 模块: Terrain Rotation Selector -> : Time: 961.4543 ms. 地址:[25114927] Started searching offset with:0
2025-05-04 21:36:25.567 +08:00 [INF] 模块: Terrain Rotator Helper -> : Time: 971.4835 ms. 地址:[25114927] Started searching offset with:0
2025-05-04 21:36:25.567 +08:00 [INF] 模块: File Root -> : Time: 1285.2539 ms. 地址:[29539156] Started searching offset with:0
2025-05-04 21:36:25.567 +08:00 [INF] 模块: DiagnosticInfoType -> : Time: 2901.8332 ms. 地址:[0] Started searching offset with:600000
2025-05-04 21:36:25.567 +08:00 [INF] 初始化用时 7007.3024 毫秒.
2025-05-04 21:36:25.567 +08:00 [INF] Resize from: {X=0,Y=0,Width=1284,Height=701} to {X=240,Y=71,Width=800,Height=560}
2025-05-04 21:36:25.606 +08:00 [INF] DD loaded in 136.2237 ms.
2025-05-04 21:36:25.622 +08:00 [ERR] 线程1启动
2025-05-04 21:36:25.623 +08:00 [ERR] 线程2启动
2025-05-04 21:36:25.623 +08:00 [INF] 监测线程启动
2025-05-04 21:36:25.623 +08:00 [INF] 子目录列表：
2025-05-04 21:36:25.623 +08:00 [INF] 冰之打击武僧
2025-05-04 21:36:25.715 +08:00 [INF] 召唤
2025-05-04 21:36:25.715 +08:00 [INF] 召唤弓箭手
2025-05-04 21:36:25.715 +08:00 [INF] 囚神僧作者自己用的BD
2025-05-04 21:36:25.715 +08:00 [INF] 囚神杖武僧
2025-05-04 21:36:25.715 +08:00 [INF] 恶魔受伤释放
2025-05-04 21:36:25.715 +08:00 [INF] 恶魔受伤释放双祈愿
2025-05-04 21:36:25.715 +08:00 [INF] 游侠
2025-05-04 21:36:25.715 +08:00 [INF] 电武僧
2025-05-04 21:36:25.715 +08:00 [INF] 电球
2025-05-04 21:36:25.715 +08:00 [INF] 自己用
2025-05-04 21:36:25.715 +08:00 [INF] 闪电
2025-05-04 21:36:25.715 +08:00 [INF] 闪电箭
2025-05-04 21:36:25.715 +08:00 [INF] 阴抓BD模板
2025-05-04 21:36:25.716 +08:00 [INF] 陰爪BD先取消走A
2025-05-04 21:36:25.716 +08:00 [INF] 骑鸟电矛
2025-05-04 21:36:25.716 +08:00 [INF] DD -> 初始化用时: 92.4072 毫秒.
2025-05-04 21:36:25.749 +08:00 [INF] 加载地图数据耗时：2
2025-05-04 21:36:25.749 +08:00 [INF] 当前地图： (0)  N：-1 T：0
2025-05-04 21:37:25.119 +08:00 [ERR] Core -> Loading SoundController failed.
2025-05-04 21:37:25.171 +08:00 [ERR] Core -> SharpDX.SharpDXException: HRESULT: [0x80070490], Module: [Unknown], ApiCode: [Unknown/Unknown], Message: 找不到元素。

   在 SharpDX.Result.CheckError()
   在 SharpDX.XAudio2.XAudio2.CreateMasteringVoice(MasteringVoice masteringVoiceOut, Int32 inputChannels, Int32 inputSampleRate, Int32 flags, String szDeviceId, Nullable`1 effectChainRef, AudioStreamCategory streamCategory)
   在 SharpDX.XAudio2.MasteringVoice..ctor(XAudio2 device, Int32 inputChannels, Int32 inputSampleRate)
   在 ExileCore.SoundController..ctor(String dir)
   在 ExileCore.Core..ctor(RenderForm form)
2025-05-04 21:37:25.171 +08:00 [INF] 模块: Area change -> : Time: 40.4761 ms. 地址:[1037465] Started searching offset with:0
2025-05-04 21:37:25.171 +08:00 [INF] 模块: Atlas Helper -> : Time: 59.4274 ms. 地址:[937344] Started searching offset with:0
2025-05-04 21:37:25.171 +08:00 [INF] 模块: Game State -> : Time: 60.7559 ms. 地址:[934055] Started searching offset with:0
2025-05-04 21:37:25.171 +08:00 [INF] 模块: BlackBarSize -> : Time: 194.3007 ms. 地址:[4510023] Started searching offset with:0
2025-05-04 21:37:25.171 +08:00 [INF] 模块: Terrain Rotator Helper -> : Time: 998.3217 ms. 地址:[25114927] Started searching offset with:0
2025-05-04 21:37:25.171 +08:00 [INF] 模块: Terrain Rotation Selector -> : Time: 1035.7328 ms. 地址:[25114927] Started searching offset with:0
2025-05-04 21:37:25.171 +08:00 [INF] 模块: File Root -> : Time: 1262.5964 ms. 地址:[29539156] Started searching offset with:0
2025-05-04 21:37:25.171 +08:00 [INF] 模块: DiagnosticInfoType -> : Time: 2704.9937 ms. 地址:[0] Started searching offset with:600000
2025-05-04 21:37:25.171 +08:00 [INF] Resize from: {X=0,Y=0,Width=1284,Height=701} to {X=240,Y=71,Width=800,Height=560}
2025-05-04 21:37:25.171 +08:00 [INF] 初始化用时 6660.2073 毫秒.
2025-05-04 21:37:25.223 +08:00 [INF] DD loaded in 129.0018 ms.
2025-05-04 21:37:25.223 +08:00 [ERR] 线程1启动
2025-05-04 21:37:25.223 +08:00 [ERR] 线程2启动
2025-05-04 21:37:25.225 +08:00 [INF] 监测线程启动
2025-05-04 21:37:25.227 +08:00 [INF] 子目录列表：
2025-05-04 21:37:25.227 +08:00 [INF] 冰之打击武僧
2025-05-04 21:37:25.305 +08:00 [INF] 召唤
2025-05-04 21:37:25.305 +08:00 [INF] 召唤弓箭手
2025-05-04 21:37:25.305 +08:00 [INF] 囚神僧作者自己用的BD
2025-05-04 21:37:25.305 +08:00 [INF] 囚神杖武僧
2025-05-04 21:37:25.305 +08:00 [INF] 恶魔受伤释放
2025-05-04 21:37:25.305 +08:00 [INF] 恶魔受伤释放双祈愿
2025-05-04 21:37:25.305 +08:00 [INF] 游侠
2025-05-04 21:37:25.305 +08:00 [INF] 电武僧
2025-05-04 21:37:25.305 +08:00 [INF] 电球
2025-05-04 21:37:25.305 +08:00 [INF] 自己用
2025-05-04 21:37:25.305 +08:00 [INF] 闪电
2025-05-04 21:37:25.305 +08:00 [INF] 闪电箭
2025-05-04 21:37:25.305 +08:00 [INF] 阴抓BD模板
2025-05-04 21:37:25.306 +08:00 [INF] 陰爪BD先取消走A
2025-05-04 21:37:25.306 +08:00 [INF] 骑鸟电矛
2025-05-04 21:37:25.338 +08:00 [INF] DD -> 初始化用时: 91.8368 毫秒.
2025-05-04 21:37:25.339 +08:00 [INF] 加载地图数据耗时：2
2025-05-04 21:37:25.339 +08:00 [INF] 当前地图： (0)  N：-1 T：0
2025-05-04 21:39:43.029 +08:00 [ERR] Core -> Loading SoundController failed.
2025-05-04 21:39:43.081 +08:00 [ERR] Core -> SharpDX.SharpDXException: HRESULT: [0x80070490], Module: [Unknown], ApiCode: [Unknown/Unknown], Message: 找不到元素。

   在 SharpDX.Result.CheckError()
   在 SharpDX.XAudio2.XAudio2.CreateMasteringVoice(MasteringVoice masteringVoiceOut, Int32 inputChannels, Int32 inputSampleRate, Int32 flags, String szDeviceId, Nullable`1 effectChainRef, AudioStreamCategory streamCategory)
   在 SharpDX.XAudio2.MasteringVoice..ctor(XAudio2 device, Int32 inputChannels, Int32 inputSampleRate)
   在 ExileCore.SoundController..ctor(String dir)
   在 ExileCore.Core..ctor(RenderForm form)
2025-05-04 21:39:43.081 +08:00 [INF] 模块: Atlas Helper -> : Time: 37.372 ms. 地址:[937344] Started searching offset with:0
2025-05-04 21:39:43.081 +08:00 [INF] 模块: Game State -> : Time: 40.1813 ms. 地址:[934055] Started searching offset with:0
2025-05-04 21:39:43.081 +08:00 [INF] 模块: Area change -> : Time: 42.3649 ms. 地址:[1037465] Started searching offset with:0
2025-05-04 21:39:43.081 +08:00 [INF] 模块: BlackBarSize -> : Time: 165.5862 ms. 地址:[4510023] Started searching offset with:0
2025-05-04 21:39:43.081 +08:00 [INF] 模块: Terrain Rotator Helper -> : Time: 960.9319 ms. 地址:[25114927] Started searching offset with:0
2025-05-04 21:39:43.081 +08:00 [INF] 模块: Terrain Rotation Selector -> : Time: 973.0591 ms. 地址:[25114927] Started searching offset with:0
2025-05-04 21:39:43.081 +08:00 [INF] 模块: File Root -> : Time: 1225.706 ms. 地址:[29539156] Started searching offset with:0
2025-05-04 21:39:43.081 +08:00 [INF] 模块: DiagnosticInfoType -> : Time: 2591.3191 ms. 地址:[0] Started searching offset with:600000
2025-05-04 21:39:43.081 +08:00 [INF] 初始化用时 6871.3662 毫秒.
2025-05-04 21:39:43.081 +08:00 [INF] Resize from: {X=0,Y=0,Width=1284,Height=701} to {X=240,Y=71,Width=800,Height=560}
2025-05-04 21:39:43.127 +08:00 [INF] DD loaded in 137.6642 ms.
2025-05-04 21:39:43.129 +08:00 [ERR] 线程1启动
2025-05-04 21:39:43.129 +08:00 [ERR] 线程2启动
2025-05-04 21:39:43.134 +08:00 [INF] 监测线程启动
2025-05-04 21:39:43.134 +08:00 [INF] 子目录列表：
2025-05-04 21:39:43.134 +08:00 [INF] 冰之打击武僧
2025-05-04 21:39:43.234 +08:00 [INF] 召唤
2025-05-04 21:39:43.234 +08:00 [INF] 召唤弓箭手
2025-05-04 21:39:43.234 +08:00 [INF] 囚神僧作者自己用的BD
2025-05-04 21:39:43.234 +08:00 [INF] 囚神杖武僧
2025-05-04 21:39:43.234 +08:00 [INF] 恶魔受伤释放
2025-05-04 21:39:43.234 +08:00 [INF] 恶魔受伤释放双祈愿
2025-05-04 21:39:43.234 +08:00 [INF] 游侠
2025-05-04 21:39:43.234 +08:00 [INF] 电武僧
2025-05-04 21:39:43.234 +08:00 [INF] 电球
2025-05-04 21:39:43.235 +08:00 [INF] 自己用
2025-05-04 21:39:43.235 +08:00 [INF] 闪电
2025-05-04 21:39:43.235 +08:00 [INF] 闪电箭
2025-05-04 21:39:43.235 +08:00 [INF] 阴抓BD模板
2025-05-04 21:39:43.235 +08:00 [INF] 陰爪BD先取消走A
2025-05-04 21:39:43.235 +08:00 [INF] 骑鸟电矛
2025-05-04 21:39:43.235 +08:00 [INF] DD -> 初始化用时: 99.3532 毫秒.
2025-05-04 21:39:43.267 +08:00 [INF] 加载地图数据耗时：2
2025-05-04 21:39:43.267 +08:00 [INF] 当前地图： (0)  N：-1 T：0
2025-05-04 21:40:41.583 +08:00 [ERR] Core -> Loading SoundController failed.
2025-05-04 21:40:41.649 +08:00 [ERR] Core -> SharpDX.SharpDXException: HRESULT: [0x80070490], Module: [Unknown], ApiCode: [Unknown/Unknown], Message: 找不到元素。

   在 SharpDX.Result.CheckError()
   在 SharpDX.XAudio2.XAudio2.CreateMasteringVoice(MasteringVoice masteringVoiceOut, Int32 inputChannels, Int32 inputSampleRate, Int32 flags, String szDeviceId, Nullable`1 effectChainRef, AudioStreamCategory streamCategory)
   在 SharpDX.XAudio2.MasteringVoice..ctor(XAudio2 device, Int32 inputChannels, Int32 inputSampleRate)
   在 ExileCore.SoundController..ctor(String dir)
   在 ExileCore.Core..ctor(RenderForm form)
2025-05-04 21:40:41.649 +08:00 [INF] 模块: Atlas Helper -> : Time: 36.3113 ms. 地址:[937344] Started searching offset with:0
2025-05-04 21:40:41.649 +08:00 [INF] 模块: Game State -> : Time: 36.3487 ms. 地址:[934055] Started searching offset with:0
2025-05-04 21:40:41.649 +08:00 [INF] 模块: Area change -> : Time: 38.0594 ms. 地址:[1037465] Started searching offset with:0
2025-05-04 21:40:41.649 +08:00 [INF] 模块: BlackBarSize -> : Time: 160.3707 ms. 地址:[4510023] Started searching offset with:0
2025-05-04 21:40:41.649 +08:00 [INF] 模块: Terrain Rotator Helper -> : Time: 923.4984 ms. 地址:[25114927] Started searching offset with:0
2025-05-04 21:40:41.649 +08:00 [INF] 模块: Terrain Rotation Selector -> : Time: 969.9439 ms. 地址:[25114927] Started searching offset with:0
2025-05-04 21:40:41.649 +08:00 [INF] 模块: File Root -> : Time: 1197.9046 ms. 地址:[29539156] Started searching offset with:0
2025-05-04 21:40:41.649 +08:00 [INF] 模块: DiagnosticInfoType -> : Time: 2779.4231 ms. 地址:[0] Started searching offset with:600000
2025-05-04 21:40:41.649 +08:00 [INF] 初始化用时 6761.3881 毫秒.
2025-05-04 21:40:41.649 +08:00 [INF] Resize from: {X=0,Y=0,Width=1284,Height=701} to {X=240,Y=71,Width=800,Height=560}
2025-05-04 21:40:41.701 +08:00 [INF] DD loaded in 146.0959 ms.
2025-05-04 21:40:41.701 +08:00 [ERR] 线程1启动
2025-05-04 21:40:41.701 +08:00 [ERR] 线程2启动
2025-05-04 21:40:41.701 +08:00 [INF] 监测线程启动
2025-05-04 21:40:41.701 +08:00 [INF] 子目录列表：
2025-05-04 21:40:41.701 +08:00 [INF] 冰之打击武僧
2025-05-04 21:40:41.777 +08:00 [INF] 召唤
2025-05-04 21:40:41.777 +08:00 [INF] 召唤弓箭手
2025-05-04 21:40:41.777 +08:00 [INF] 囚神僧作者自己用的BD
2025-05-04 21:40:41.777 +08:00 [INF] 囚神杖武僧
2025-05-04 21:40:41.777 +08:00 [INF] 恶魔受伤释放
2025-05-04 21:40:41.777 +08:00 [INF] 恶魔受伤释放双祈愿
2025-05-04 21:40:41.777 +08:00 [INF] 游侠
2025-05-04 21:40:41.777 +08:00 [INF] 电武僧
2025-05-04 21:40:41.777 +08:00 [INF] 电球
2025-05-04 21:40:41.777 +08:00 [INF] 自己用
2025-05-04 21:40:41.777 +08:00 [INF] 闪电
2025-05-04 21:40:41.777 +08:00 [INF] 闪电箭
2025-05-04 21:40:41.777 +08:00 [INF] 阴抓BD模板
2025-05-04 21:40:41.777 +08:00 [INF] 陰爪BD先取消走A
2025-05-04 21:40:41.777 +08:00 [INF] 骑鸟电矛
2025-05-04 21:40:41.810 +08:00 [INF] DD -> 初始化用时: 97.3904 毫秒.
2025-05-04 21:40:41.810 +08:00 [INF] 加载地图数据耗时：2
2025-05-04 21:40:41.810 +08:00 [INF] 当前地图： (0)  N：-1 T：0
2025-05-04 21:50:31.515 +08:00 [ERR] Core -> Loading SoundController failed.
2025-05-04 21:50:31.586 +08:00 [ERR] Core -> SharpDX.SharpDXException: HRESULT: [0x80070490], Module: [Unknown], ApiCode: [Unknown/Unknown], Message: 找不到元素。

   在 SharpDX.Result.CheckError()
   在 SharpDX.XAudio2.XAudio2.CreateMasteringVoice(MasteringVoice masteringVoiceOut, Int32 inputChannels, Int32 inputSampleRate, Int32 flags, String szDeviceId, Nullable`1 effectChainRef, AudioStreamCategory streamCategory)
   在 SharpDX.XAudio2.MasteringVoice..ctor(XAudio2 device, Int32 inputChannels, Int32 inputSampleRate)
   在 ExileCore.SoundController..ctor(String dir)
   在 ExileCore.Core..ctor(RenderForm form)
2025-05-04 21:50:31.586 +08:00 [INF] 模块: Atlas Helper -> : Time: 40.6756 ms. 地址:[937344] Started searching offset with:0
2025-05-04 21:50:31.586 +08:00 [INF] 模块: Game State -> : Time: 42.4434 ms. 地址:[934055] Started searching offset with:0
2025-05-04 21:50:31.586 +08:00 [INF] 模块: Area change -> : Time: 63.1229 ms. 地址:[1037465] Started searching offset with:0
2025-05-04 21:50:31.586 +08:00 [INF] 模块: BlackBarSize -> : Time: 179.1579 ms. 地址:[4510023] Started searching offset with:0
2025-05-04 21:50:31.586 +08:00 [INF] 模块: Terrain Rotator Helper -> : Time: 1037.7345 ms. 地址:[25114927] Started searching offset with:0
2025-05-04 21:50:31.586 +08:00 [INF] 模块: Terrain Rotation Selector -> : Time: 1042.0032 ms. 地址:[25114927] Started searching offset with:0
2025-05-04 21:50:31.586 +08:00 [INF] 模块: File Root -> : Time: 1295.7039 ms. 地址:[29539156] Started searching offset with:0
2025-05-04 21:50:31.586 +08:00 [INF] 模块: DiagnosticInfoType -> : Time: 2751.7568 ms. 地址:[0] Started searching offset with:600000
2025-05-04 21:50:31.586 +08:00 [INF] 初始化用时 8074.1065 毫秒.
2025-05-04 21:50:31.586 +08:00 [INF] Resize from: {X=0,Y=0,Width=1584,Height=861} to {X=560,Y=251,Width=800,Height=560}
2025-05-04 21:50:31.640 +08:00 [INF] DD loaded in 148.6552 ms.
2025-05-04 21:50:31.640 +08:00 [ERR] 线程1启动
2025-05-04 21:50:31.640 +08:00 [ERR] 线程2启动
2025-05-04 21:50:31.640 +08:00 [INF] 监测线程启动
2025-05-04 21:50:31.640 +08:00 [INF] 子目录列表：
2025-05-04 21:50:31.640 +08:00 [INF] 冰之打击武僧
2025-05-04 21:50:31.732 +08:00 [INF] 召唤
2025-05-04 21:50:31.732 +08:00 [INF] 召唤弓箭手
2025-05-04 21:50:31.732 +08:00 [INF] 囚神僧作者自己用的BD
2025-05-04 21:50:31.732 +08:00 [INF] 囚神杖武僧
2025-05-04 21:50:31.732 +08:00 [INF] 恶魔受伤释放
2025-05-04 21:50:31.732 +08:00 [INF] 恶魔受伤释放双祈愿
2025-05-04 21:50:31.732 +08:00 [INF] 游侠
2025-05-04 21:50:31.732 +08:00 [INF] 电武僧
2025-05-04 21:50:31.732 +08:00 [INF] 电球
2025-05-04 21:50:31.732 +08:00 [INF] 自己用
2025-05-04 21:50:31.732 +08:00 [INF] 闪电
2025-05-04 21:50:31.732 +08:00 [INF] 闪电箭
2025-05-04 21:50:31.732 +08:00 [INF] 阴抓BD模板
2025-05-04 21:50:31.764 +08:00 [INF] 陰爪BD先取消走A
2025-05-04 21:50:31.764 +08:00 [INF] 骑鸟电矛
2025-05-04 21:50:31.764 +08:00 [INF] DD -> 初始化用时: 132.8793 毫秒.
2025-05-04 21:50:31.797 +08:00 [INF] 加载地图数据耗时：4
2025-05-04 21:50:31.797 +08:00 [INF] 当前地图： (0)  N：-1 T：0
2025-05-04 21:51:31.731 +08:00 [ERR] Core -> Loading SoundController failed.
2025-05-04 21:51:31.788 +08:00 [ERR] Core -> SharpDX.SharpDXException: HRESULT: [0x80070490], Module: [Unknown], ApiCode: [Unknown/Unknown], Message: 找不到元素。

   在 SharpDX.Result.CheckError()
   在 SharpDX.XAudio2.XAudio2.CreateMasteringVoice(MasteringVoice masteringVoiceOut, Int32 inputChannels, Int32 inputSampleRate, Int32 flags, String szDeviceId, Nullable`1 effectChainRef, AudioStreamCategory streamCategory)
   在 SharpDX.XAudio2.MasteringVoice..ctor(XAudio2 device, Int32 inputChannels, Int32 inputSampleRate)
   在 ExileCore.SoundController..ctor(String dir)
   在 ExileCore.Core..ctor(RenderForm form)
2025-05-04 21:51:31.789 +08:00 [INF] 模块: Atlas Helper -> : Time: 38.3411 ms. 地址:[937344] Started searching offset with:0
2025-05-04 21:51:31.789 +08:00 [INF] 模块: Game State -> : Time: 38.6093 ms. 地址:[934055] Started searching offset with:0
2025-05-04 21:51:31.789 +08:00 [INF] 模块: Area change -> : Time: 40.6747 ms. 地址:[1037465] Started searching offset with:0
2025-05-04 21:51:31.789 +08:00 [INF] 模块: BlackBarSize -> : Time: 183.4712 ms. 地址:[4510023] Started searching offset with:0
2025-05-04 21:51:31.789 +08:00 [INF] 模块: Terrain Rotation Selector -> : Time: 966.7128 ms. 地址:[25114927] Started searching offset with:0
2025-05-04 21:51:31.789 +08:00 [INF] 模块: Terrain Rotator Helper -> : Time: 1016.8189 ms. 地址:[25114927] Started searching offset with:0
2025-05-04 21:51:31.789 +08:00 [INF] 模块: File Root -> : Time: 1304.9116 ms. 地址:[29539156] Started searching offset with:0
2025-05-04 21:51:31.789 +08:00 [INF] 模块: DiagnosticInfoType -> : Time: 2750.0273 ms. 地址:[0] Started searching offset with:600000
2025-05-04 21:51:31.789 +08:00 [INF] 初始化用时 6917.6727 毫秒.
2025-05-04 21:51:31.789 +08:00 [INF] Resize from: {X=0,Y=0,Width=1584,Height=861} to {X=560,Y=251,Width=800,Height=560}
2025-05-04 21:51:31.836 +08:00 [INF] DD loaded in 133.7019 ms.
2025-05-04 21:51:31.836 +08:00 [ERR] 线程1启动
2025-05-04 21:51:31.836 +08:00 [ERR] 线程2启动
2025-05-04 21:51:31.836 +08:00 [INF] 监测线程启动
2025-05-04 21:51:31.836 +08:00 [INF] 子目录列表：
2025-05-04 21:51:31.836 +08:00 [INF] 冰之打击武僧
2025-05-04 21:51:31.927 +08:00 [INF] 召唤
2025-05-04 21:51:31.927 +08:00 [INF] 召唤弓箭手
2025-05-04 21:51:31.927 +08:00 [INF] 囚神僧作者自己用的BD
2025-05-04 21:51:31.928 +08:00 [INF] 囚神杖武僧
2025-05-04 21:51:31.928 +08:00 [INF] 恶魔受伤释放
2025-05-04 21:51:31.928 +08:00 [INF] 恶魔受伤释放双祈愿
2025-05-04 21:51:31.928 +08:00 [INF] 游侠
2025-05-04 21:51:31.928 +08:00 [INF] 电武僧
2025-05-04 21:51:31.928 +08:00 [INF] 电球
2025-05-04 21:51:31.928 +08:00 [INF] 自己用
2025-05-04 21:51:31.928 +08:00 [INF] 闪电
2025-05-04 21:51:31.928 +08:00 [INF] 闪电箭
2025-05-04 21:51:31.928 +08:00 [INF] 阴抓BD模板
2025-05-04 21:51:31.928 +08:00 [INF] 陰爪BD先取消走A
2025-05-04 21:51:31.928 +08:00 [INF] 骑鸟电矛
2025-05-04 21:51:31.928 +08:00 [INF] DD -> 初始化用时: 102.5185 毫秒.
2025-05-04 21:51:31.961 +08:00 [INF] 加载地图数据耗时：2
2025-05-04 21:51:31.961 +08:00 [INF] 当前地图： (0)  N：-1 T：0
2025-05-04 21:52:11.629 +08:00 [ERR] Core -> Loading SoundController failed.
2025-05-04 21:52:11.679 +08:00 [ERR] Core -> SharpDX.SharpDXException: HRESULT: [0x80070490], Module: [Unknown], ApiCode: [Unknown/Unknown], Message: 找不到元素。

   在 SharpDX.Result.CheckError()
   在 SharpDX.XAudio2.XAudio2.CreateMasteringVoice(MasteringVoice masteringVoiceOut, Int32 inputChannels, Int32 inputSampleRate, Int32 flags, String szDeviceId, Nullable`1 effectChainRef, AudioStreamCategory streamCategory)
   在 SharpDX.XAudio2.MasteringVoice..ctor(XAudio2 device, Int32 inputChannels, Int32 inputSampleRate)
   在 ExileCore.SoundController..ctor(String dir)
   在 ExileCore.Core..ctor(RenderForm form)
2025-05-04 21:52:11.680 +08:00 [INF] 模块: Area change -> : Time: 39.4993 ms. 地址:[1037465] Started searching offset with:0
2025-05-04 21:52:11.680 +08:00 [INF] 模块: Game State -> : Time: 41.9529 ms. 地址:[934055] Started searching offset with:0
2025-05-04 21:52:11.680 +08:00 [INF] 模块: Atlas Helper -> : Time: 43.5238 ms. 地址:[937344] Started searching offset with:0
2025-05-04 21:52:11.680 +08:00 [INF] 模块: BlackBarSize -> : Time: 168.4375 ms. 地址:[4510023] Started searching offset with:0
2025-05-04 21:52:11.680 +08:00 [INF] 模块: Terrain Rotation Selector -> : Time: 994.3941 ms. 地址:[25114927] Started searching offset with:0
2025-05-04 21:52:11.680 +08:00 [INF] 模块: Terrain Rotator Helper -> : Time: 1003.2899 ms. 地址:[25114927] Started searching offset with:0
2025-05-04 21:52:11.680 +08:00 [INF] 模块: File Root -> : Time: 1301.9695 ms. 地址:[29539156] Started searching offset with:0
2025-05-04 21:52:11.680 +08:00 [INF] 模块: DiagnosticInfoType -> : Time: 2747.574 ms. 地址:[0] Started searching offset with:600000
2025-05-04 21:52:11.680 +08:00 [INF] 初始化用时 6841.74 毫秒.
2025-05-04 21:52:11.680 +08:00 [INF] Resize from: {X=0,Y=0,Width=1584,Height=861} to {X=560,Y=251,Width=800,Height=560}
2025-05-04 21:52:11.731 +08:00 [INF] DD loaded in 137.37 ms.
2025-05-04 21:52:11.731 +08:00 [ERR] 线程1启动
2025-05-04 21:52:11.731 +08:00 [ERR] 线程2启动
2025-05-04 21:52:11.733 +08:00 [INF] 监测线程启动
2025-05-04 21:52:11.734 +08:00 [INF] 子目录列表：
2025-05-04 21:52:11.734 +08:00 [INF] 冰之打击武僧
2025-05-04 21:52:11.827 +08:00 [INF] 召唤
2025-05-04 21:52:11.827 +08:00 [INF] 召唤弓箭手
2025-05-04 21:52:11.827 +08:00 [INF] 囚神僧作者自己用的BD
2025-05-04 21:52:11.827 +08:00 [INF] 囚神杖武僧
2025-05-04 21:52:11.827 +08:00 [INF] 恶魔受伤释放
2025-05-04 21:52:11.827 +08:00 [INF] 恶魔受伤释放双祈愿
2025-05-04 21:52:11.827 +08:00 [INF] 游侠
2025-05-04 21:52:11.827 +08:00 [INF] 电武僧
2025-05-04 21:52:11.827 +08:00 [INF] 电球
2025-05-04 21:52:11.827 +08:00 [INF] 自己用
2025-05-04 21:52:11.827 +08:00 [INF] 闪电
2025-05-04 21:52:11.827 +08:00 [INF] 闪电箭
2025-05-04 21:52:11.827 +08:00 [INF] 阴抓BD模板
2025-05-04 21:52:11.827 +08:00 [INF] 陰爪BD先取消走A
2025-05-04 21:52:11.827 +08:00 [INF] 骑鸟电矛
2025-05-04 21:52:11.827 +08:00 [INF] DD -> 初始化用时: 89.6816 毫秒.
2025-05-04 21:52:11.861 +08:00 [INF] 加载地图数据耗时：2
2025-05-04 21:52:11.861 +08:00 [INF] 当前地图： (0)  N：-1 T：0
2025-05-04 21:53:11.885 +08:00 [ERR] Core -> Loading SoundController failed.
2025-05-04 21:53:11.937 +08:00 [ERR] Core -> SharpDX.SharpDXException: HRESULT: [0x80070490], Module: [Unknown], ApiCode: [Unknown/Unknown], Message: 找不到元素。

   在 SharpDX.Result.CheckError()
   在 SharpDX.XAudio2.XAudio2.CreateMasteringVoice(MasteringVoice masteringVoiceOut, Int32 inputChannels, Int32 inputSampleRate, Int32 flags, String szDeviceId, Nullable`1 effectChainRef, AudioStreamCategory streamCategory)
   在 SharpDX.XAudio2.MasteringVoice..ctor(XAudio2 device, Int32 inputChannels, Int32 inputSampleRate)
   在 ExileCore.SoundController..ctor(String dir)
   在 ExileCore.Core..ctor(RenderForm form)
2025-05-04 21:53:11.937 +08:00 [INF] 模块: Game State -> : Time: 36.5773 ms. 地址:[934055] Started searching offset with:0
2025-05-04 21:53:11.937 +08:00 [INF] 模块: Atlas Helper -> : Time: 39.9993 ms. 地址:[937344] Started searching offset with:0
2025-05-04 21:53:11.937 +08:00 [INF] 模块: Area change -> : Time: 64.4431 ms. 地址:[1037465] Started searching offset with:0
2025-05-04 21:53:11.937 +08:00 [INF] 模块: BlackBarSize -> : Time: 198.2324 ms. 地址:[4510023] Started searching offset with:0
2025-05-04 21:53:11.937 +08:00 [INF] 模块: Terrain Rotation Selector -> : Time: 978.2851 ms. 地址:[25114927] Started searching offset with:0
2025-05-04 21:53:11.937 +08:00 [INF] 模块: Terrain Rotator Helper -> : Time: 994.0968 ms. 地址:[25114927] Started searching offset with:0
2025-05-04 21:53:11.937 +08:00 [INF] 模块: File Root -> : Time: 1273.2224 ms. 地址:[29539156] Started searching offset with:0
2025-05-04 21:53:11.937 +08:00 [INF] 模块: DiagnosticInfoType -> : Time: 2733.6303 ms. 地址:[0] Started searching offset with:600000
2025-05-04 21:53:11.937 +08:00 [INF] 初始化用时 7060.136 毫秒.
2025-05-04 21:53:11.937 +08:00 [INF] Resize from: {X=0,Y=0,Width=1584,Height=861} to {X=560,Y=251,Width=800,Height=560}
2025-05-04 21:53:11.987 +08:00 [INF] DD loaded in 139.7646 ms.
2025-05-04 21:53:11.987 +08:00 [ERR] 线程1启动
2025-05-04 21:53:11.987 +08:00 [ERR] 线程2启动
2025-05-04 21:53:11.988 +08:00 [INF] 监测线程启动
2025-05-04 21:53:11.990 +08:00 [INF] 子目录列表：
2025-05-04 21:53:11.991 +08:00 [INF] 冰之打击武僧
2025-05-04 21:53:12.081 +08:00 [INF] 召唤
2025-05-04 21:53:12.082 +08:00 [INF] 召唤弓箭手
2025-05-04 21:53:12.082 +08:00 [INF] 囚神僧作者自己用的BD
2025-05-04 21:53:12.082 +08:00 [INF] 囚神杖武僧
2025-05-04 21:53:12.082 +08:00 [INF] 恶魔受伤释放
2025-05-04 21:53:12.082 +08:00 [INF] 恶魔受伤释放双祈愿
2025-05-04 21:53:12.082 +08:00 [INF] 游侠
2025-05-04 21:53:12.082 +08:00 [INF] 电武僧
2025-05-04 21:53:12.082 +08:00 [INF] 电球
2025-05-04 21:53:12.082 +08:00 [INF] 自己用
2025-05-04 21:53:12.082 +08:00 [INF] 闪电
2025-05-04 21:53:12.082 +08:00 [INF] 闪电箭
2025-05-04 21:53:12.082 +08:00 [INF] 阴抓BD模板
2025-05-04 21:53:12.082 +08:00 [INF] 陰爪BD先取消走A
2025-05-04 21:53:12.082 +08:00 [INF] 骑鸟电矛
2025-05-04 21:53:12.082 +08:00 [INF] DD -> 初始化用时: 87.488 毫秒.
2025-05-04 21:53:12.115 +08:00 [INF] 加载地图数据耗时：2
2025-05-04 21:53:12.115 +08:00 [INF] 当前地图： (0)  N：-1 T：0
2025-05-04 22:00:13.659 +08:00 [ERR] Core -> Loading SoundController failed.
2025-05-04 22:00:13.710 +08:00 [ERR] Core -> SharpDX.SharpDXException: HRESULT: [0x80070490], Module: [Unknown], ApiCode: [Unknown/Unknown], Message: 找不到元素。

   在 SharpDX.Result.CheckError()
   在 SharpDX.XAudio2.XAudio2.CreateMasteringVoice(MasteringVoice masteringVoiceOut, Int32 inputChannels, Int32 inputSampleRate, Int32 flags, String szDeviceId, Nullable`1 effectChainRef, AudioStreamCategory streamCategory)
   在 SharpDX.XAudio2.MasteringVoice..ctor(XAudio2 device, Int32 inputChannels, Int32 inputSampleRate)
   在 ExileCore.SoundController..ctor(String dir)
   在 ExileCore.Core..ctor(RenderForm form)
2025-05-04 22:00:13.710 +08:00 [INF] 模块: Atlas Helper -> : Time: 35.7695 ms. 地址:[937344] Started searching offset with:0
2025-05-04 22:00:13.710 +08:00 [INF] 模块: Game State -> : Time: 36.6257 ms. 地址:[934055] Started searching offset with:0
2025-05-04 22:00:13.710 +08:00 [INF] 模块: Area change -> : Time: 59.6923 ms. 地址:[1037465] Started searching offset with:0
2025-05-04 22:00:13.710 +08:00 [INF] 模块: BlackBarSize -> : Time: 160.7684 ms. 地址:[4510023] Started searching offset with:0
2025-05-04 22:00:13.710 +08:00 [INF] 模块: Terrain Rotator Helper -> : Time: 938.0957 ms. 地址:[25114927] Started searching offset with:0
2025-05-04 22:00:13.710 +08:00 [INF] 模块: Terrain Rotation Selector -> : Time: 945.664 ms. 地址:[25114927] Started searching offset with:0
2025-05-04 22:00:13.710 +08:00 [INF] 模块: File Root -> : Time: 1308.4865 ms. 地址:[29539156] Started searching offset with:0
2025-05-04 22:00:13.710 +08:00 [INF] 模块: DiagnosticInfoType -> : Time: 2610.4325 ms. 地址:[0] Started searching offset with:600000
2025-05-04 22:00:13.710 +08:00 [INF] 初始化用时 6679.6035 毫秒.
2025-05-04 22:00:13.710 +08:00 [INF] Resize from: {X=0,Y=0,Width=1584,Height=861} to {X=560,Y=251,Width=800,Height=560}
2025-05-04 22:00:13.756 +08:00 [INF] DD loaded in 140.0785 ms.
2025-05-04 22:00:13.775 +08:00 [ERR] 线程1启动
2025-05-04 22:00:13.775 +08:00 [ERR] 线程2启动
2025-05-04 22:00:13.775 +08:00 [INF] 监测线程启动
2025-05-04 22:00:13.775 +08:00 [INF] 子目录列表：
2025-05-04 22:00:13.775 +08:00 [INF] 冰之打击武僧
2025-05-04 22:00:13.842 +08:00 [INF] 召唤
2025-05-04 22:00:13.842 +08:00 [INF] 召唤弓箭手
2025-05-04 22:00:13.842 +08:00 [INF] 囚神僧作者自己用的BD
2025-05-04 22:00:13.842 +08:00 [INF] 囚神杖武僧
2025-05-04 22:00:13.842 +08:00 [INF] 恶魔受伤释放
2025-05-04 22:00:13.842 +08:00 [INF] 恶魔受伤释放双祈愿
2025-05-04 22:00:13.842 +08:00 [INF] 游侠
2025-05-04 22:00:13.842 +08:00 [INF] 电武僧
2025-05-04 22:00:13.842 +08:00 [INF] 电球
2025-05-04 22:00:13.842 +08:00 [INF] 自己用
2025-05-04 22:00:13.842 +08:00 [INF] 闪电
2025-05-04 22:00:13.842 +08:00 [INF] 闪电箭
2025-05-04 22:00:13.842 +08:00 [INF] 阴抓BD模板
2025-05-04 22:00:13.842 +08:00 [INF] 陰爪BD先取消走A
2025-05-04 22:00:13.842 +08:00 [INF] 骑鸟电矛
2025-05-04 22:00:13.875 +08:00 [INF] DD -> 初始化用时: 85.718 毫秒.
2025-05-04 22:00:13.875 +08:00 [INF] 加载地图数据耗时：2
2025-05-04 22:00:13.875 +08:00 [INF] 当前地图： (0)  N：-1 T：0
2025-05-04 22:01:16.668 +08:00 [ERR] Core -> Loading SoundController failed.
2025-05-04 22:01:16.717 +08:00 [ERR] Core -> SharpDX.SharpDXException: HRESULT: [0x80070490], Module: [Unknown], ApiCode: [Unknown/Unknown], Message: 找不到元素。

   在 SharpDX.Result.CheckError()
   在 SharpDX.XAudio2.XAudio2.CreateMasteringVoice(MasteringVoice masteringVoiceOut, Int32 inputChannels, Int32 inputSampleRate, Int32 flags, String szDeviceId, Nullable`1 effectChainRef, AudioStreamCategory streamCategory)
   在 SharpDX.XAudio2.MasteringVoice..ctor(XAudio2 device, Int32 inputChannels, Int32 inputSampleRate)
   在 ExileCore.SoundController..ctor(String dir)
   在 ExileCore.Core..ctor(RenderForm form)
2025-05-04 22:01:16.717 +08:00 [INF] 模块: Atlas Helper -> : Time: 36.2836 ms. 地址:[937344] Started searching offset with:0
2025-05-04 22:01:16.717 +08:00 [INF] 模块: Area change -> : Time: 37.9579 ms. 地址:[1037465] Started searching offset with:0
2025-05-04 22:01:16.717 +08:00 [INF] 模块: Game State -> : Time: 39.616 ms. 地址:[934055] Started searching offset with:0
2025-05-04 22:01:16.717 +08:00 [INF] 模块: BlackBarSize -> : Time: 166.6836 ms. 地址:[4510023] Started searching offset with:0
2025-05-04 22:01:16.717 +08:00 [INF] 模块: Terrain Rotation Selector -> : Time: 954.0178 ms. 地址:[25114927] Started searching offset with:0
2025-05-04 22:01:16.717 +08:00 [INF] 模块: Terrain Rotator Helper -> : Time: 957.0912 ms. 地址:[25114927] Started searching offset with:0
2025-05-04 22:01:16.717 +08:00 [INF] 模块: File Root -> : Time: 1191.3287 ms. 地址:[29539156] Started searching offset with:0
2025-05-04 22:01:16.718 +08:00 [INF] 模块: DiagnosticInfoType -> : Time: 2593.9626 ms. 地址:[0] Started searching offset with:600000
2025-05-04 22:01:16.718 +08:00 [INF] 初始化用时 6609.9363 毫秒.
2025-05-04 22:01:16.718 +08:00 [INF] Resize from: {X=0,Y=0,Width=1584,Height=861} to {X=562,Y=154,Width=800,Height=600}
2025-05-04 22:01:16.765 +08:00 [INF] DD loaded in 150.6525 ms.
2025-05-04 22:01:16.795 +08:00 [ERR] 线程1启动
2025-05-04 22:01:16.795 +08:00 [ERR] 线程2启动
2025-05-04 22:01:16.795 +08:00 [INF] 监测线程启动
2025-05-04 22:01:16.795 +08:00 [INF] 子目录列表：
2025-05-04 22:01:16.795 +08:00 [INF] 冰之打击武僧
2025-05-04 22:01:16.827 +08:00 [ERR] 跳过动画
2025-05-04 22:01:16.861 +08:00 [INF] 召唤
2025-05-04 22:01:16.861 +08:00 [INF] 召唤弓箭手
2025-05-04 22:01:16.861 +08:00 [INF] 囚神僧作者自己用的BD
2025-05-04 22:01:16.861 +08:00 [INF] 囚神杖武僧
2025-05-04 22:01:16.861 +08:00 [INF] 恶魔受伤释放
2025-05-04 22:01:16.861 +08:00 [INF] 恶魔受伤释放双祈愿
2025-05-04 22:01:16.861 +08:00 [INF] 游侠
2025-05-04 22:01:16.861 +08:00 [INF] 电武僧
2025-05-04 22:01:16.861 +08:00 [INF] 电球
2025-05-04 22:01:16.861 +08:00 [INF] 自己用
2025-05-04 22:01:16.861 +08:00 [INF] 闪电
2025-05-04 22:01:16.861 +08:00 [INF] 闪电箭
2025-05-04 22:01:16.861 +08:00 [INF] 阴抓BD模板
2025-05-04 22:01:16.861 +08:00 [INF] 陰爪BD先取消走A
2025-05-04 22:01:16.861 +08:00 [INF] 骑鸟电矛
2025-05-04 22:01:16.861 +08:00 [INF] DD -> 初始化用时: 77.2841 毫秒.
2025-05-04 22:01:16.895 +08:00 [INF] 加载地图数据耗时：2
2025-05-04 22:01:16.895 +08:00 [INF] 当前地图： (0)  N：-1 T：0
2025-05-04 22:01:19.862 +08:00 [ERR] 跳过动画
2025-05-04 22:01:22.927 +08:00 [ERR] 跳过动画
2025-05-04 22:01:25.995 +08:00 [ERR] 跳过动画
2025-05-04 22:01:29.061 +08:00 [ERR] 跳过动画
2025-05-04 22:01:32.094 +08:00 [ERR] 跳过动画
2025-05-04 22:01:42.362 +08:00 [ERR] 跳过动画
2025-05-04 22:01:43.227 +08:00 [INF] Resize from: {X=562,Y=154,Width=800,Height=600} to {X=0,Y=23,Width=1920,Height=1017}
2025-05-04 22:01:45.594 +08:00 [ERR] 跳过动画
2025-05-04 22:01:48.995 +08:00 [INF] Resize from: {X=0,Y=23,Width=1920,Height=1017} to {X=562,Y=154,Width=800,Height=600}
2025-05-04 22:01:51.327 +08:00 [INF] 加载地图数据耗时：199
2025-05-04 22:01:51.327 +08:00 [INF] 当前地图：藏身處：殞落 (65) HideoutFelled N：1 T：1
2025-05-04 22:01:53.328 +08:00 [ERR] 角色卡点
2025-05-04 22:01:53.360 +08:00 [INF] [脱困移动] 移动到随机点: X:226.0544 Y:151.2409
2025-05-04 22:01:53.395 +08:00 [INF] 移动
2025-05-04 22:01:53.595 +08:00 [ERR] 角色卡点
2025-05-04 22:01:53.627 +08:00 [INF] [脱困移动] 移动到随机点: X:225.7339 Y:173.3243
2025-05-04 22:01:53.627 +08:00 [INF] 移动
2025-05-04 22:01:53.795 +08:00 [ERR] 角色卡点
2025-05-04 22:01:53.827 +08:00 [INF] 移动
2025-05-04 22:01:54.028 +08:00 [ERR] 角色卡点
2025-05-04 22:01:54.060 +08:00 [INF] 移动
2025-05-04 22:01:54.228 +08:00 [ERR] 角色卡点
2025-05-04 22:01:54.260 +08:00 [INF] 移动
2025-05-04 22:01:54.427 +08:00 [ERR] 角色卡点
2025-05-04 22:01:54.462 +08:00 [INF] 移动
2025-05-04 22:01:54.627 +08:00 [ERR] 角色卡点
2025-05-04 22:01:54.660 +08:00 [INF] 移动
2025-05-04 22:01:54.828 +08:00 [ERR] 角色卡点
2025-05-04 22:01:54.861 +08:00 [INF] 移动
2025-05-04 22:01:55.061 +08:00 [ERR] 角色卡点
2025-05-04 22:01:55.094 +08:00 [INF] 移动
2025-05-04 22:01:55.262 +08:00 [ERR] 角色卡点
2025-05-04 22:01:55.294 +08:00 [INF] 移动
2025-05-04 22:01:55.495 +08:00 [ERR] 角色卡点
2025-05-04 22:01:55.528 +08:00 [INF] 移动
2025-05-04 22:01:55.695 +08:00 [ERR] 角色卡点
2025-05-04 22:01:55.728 +08:00 [INF] 移动
2025-05-04 22:01:55.928 +08:00 [ERR] 角色卡点
2025-05-04 22:01:55.961 +08:00 [INF] 移动
2025-05-04 22:01:56.128 +08:00 [ERR] 角色卡点
2025-05-04 22:01:56.161 +08:00 [INF] 移动
2025-05-04 22:01:56.328 +08:00 [ERR] 角色卡点
2025-05-04 22:01:56.362 +08:00 [INF] 移动
2025-05-04 22:01:56.562 +08:00 [ERR] 角色卡点
2025-05-04 22:01:56.594 +08:00 [INF] 移动
2025-05-04 22:01:57.294 +08:00 [ERR] 角色卡点
2025-05-04 22:01:57.327 +08:00 [INF] 移动
2025-05-04 22:01:57.494 +08:00 [ERR] 角色卡点
2025-05-04 22:01:57.528 +08:00 [INF] 移动
2025-05-04 22:01:57.694 +08:00 [ERR] 角色卡点
2025-05-04 22:01:57.728 +08:00 [INF] 移动
2025-05-04 22:01:57.927 +08:00 [ERR] 角色卡点
2025-05-04 22:01:57.963 +08:00 [INF] 移动
2025-05-04 22:01:58.128 +08:00 [ERR] 角色卡点
2025-05-04 22:01:58.161 +08:00 [INF] 移动
2025-05-04 22:01:58.328 +08:00 [ERR] 角色卡点
2025-05-04 22:01:58.361 +08:00 [INF] 移动
2025-05-04 22:01:58.594 +08:00 [ERR] 角色卡点
2025-05-04 22:01:58.594 +08:00 [INF] 移动
2025-05-04 22:01:58.795 +08:00 [ERR] 角色卡点
2025-05-04 22:01:58.795 +08:00 [INF] 移动
2025-05-04 22:01:58.994 +08:00 [ERR] 角色卡点
2025-05-04 22:01:58.994 +08:00 [INF] 移动
2025-05-04 22:01:59.228 +08:00 [ERR] 角色卡点
2025-05-04 22:01:59.228 +08:00 [INF] 移动
2025-05-04 22:01:59.427 +08:00 [ERR] 角色卡点
2025-05-04 22:01:59.427 +08:00 [INF] 移动
2025-05-04 22:01:59.628 +08:00 [ERR] 角色卡点
2025-05-04 22:01:59.661 +08:00 [INF] 移动
2025-05-04 22:01:59.861 +08:00 [ERR] 角色卡点
2025-05-04 22:01:59.895 +08:00 [INF] 移动
2025-05-04 22:02:00.094 +08:00 [ERR] 角色卡点
2025-05-04 22:02:00.094 +08:00 [INF] 移动
2025-05-04 22:02:00.295 +08:00 [ERR] 角色卡点
2025-05-04 22:02:00.295 +08:00 [INF] 移动
2025-05-04 22:02:00.527 +08:00 [ERR] 角色卡点
2025-05-04 22:02:00.527 +08:00 [INF] 移动
2025-05-04 22:02:00.727 +08:00 [ERR] 角色卡点
2025-05-04 22:02:00.727 +08:00 [INF] 移动
2025-05-04 22:02:00.928 +08:00 [ERR] 角色卡点
2025-05-04 22:02:00.961 +08:00 [INF] 移动
2025-05-04 22:02:01.162 +08:00 [ERR] 角色卡点
2025-05-04 22:02:01.195 +08:00 [INF] 移动
2025-05-04 22:02:01.361 +08:00 [ERR] 角色卡点
2025-05-04 22:02:01.394 +08:00 [INF] 移动
2025-05-04 22:02:01.628 +08:00 [ERR] 角色卡点
2025-05-04 22:02:01.628 +08:00 [INF] 移动
2025-05-04 22:02:01.827 +08:00 [ERR] 角色卡点
2025-05-04 22:02:01.828 +08:00 [INF] 移动
2025-05-04 22:02:02.028 +08:00 [ERR] 角色卡点
2025-05-04 22:02:02.028 +08:00 [INF] 移动
2025-05-04 22:02:02.228 +08:00 [ERR] 角色卡点
2025-05-04 22:02:02.228 +08:00 [INF] 移动
2025-05-04 22:02:02.427 +08:00 [ERR] 角色卡点
2025-05-04 22:02:02.427 +08:00 [INF] 移动
2025-05-04 22:02:02.628 +08:00 [ERR] 角色卡点
2025-05-04 22:02:02.660 +08:00 [INF] 移动
2025-05-04 22:02:02.828 +08:00 [ERR] 角色卡点
2025-05-04 22:02:02.862 +08:00 [INF] 移动
2025-05-04 22:02:03.029 +08:00 [ERR] 角色卡点
2025-05-04 22:02:03.060 +08:00 [INF] 移动
2025-05-04 22:02:03.229 +08:00 [ERR] 角色卡点
2025-05-04 22:02:03.261 +08:00 [INF] 移动
2025-05-04 22:02:05.562 +08:00 [ERR] 角色卡点
2025-05-04 22:02:05.562 +08:00 [INF] 移动
2025-05-04 22:02:05.761 +08:00 [ERR] 角色卡点
2025-05-04 22:02:05.761 +08:00 [INF] 移动
2025-05-04 22:02:05.994 +08:00 [ERR] 角色卡点
2025-05-04 22:02:05.994 +08:00 [INF] 移动
2025-05-04 22:02:06.228 +08:00 [ERR] 角色卡点
2025-05-04 22:02:06.228 +08:00 [INF] 移动
2025-05-04 22:02:06.461 +08:00 [ERR] 角色卡点
2025-05-04 22:02:06.461 +08:00 [INF] 移动
2025-05-04 22:02:06.694 +08:00 [ERR] 角色卡点
2025-05-04 22:02:06.694 +08:00 [INF] 移动
2025-05-04 22:02:06.927 +08:00 [ERR] 角色卡点
2025-05-04 22:02:06.927 +08:00 [INF] 移动
2025-05-04 22:02:07.160 +08:00 [ERR] 角色卡点
2025-05-04 22:02:07.160 +08:00 [INF] 移动
2025-05-04 22:02:07.394 +08:00 [ERR] 角色卡点
2025-05-04 22:02:07.394 +08:00 [INF] 移动
2025-05-04 22:02:07.627 +08:00 [ERR] 角色卡点
2025-05-04 22:02:07.627 +08:00 [INF] 移动
2025-05-04 22:02:07.861 +08:00 [ERR] 角色卡点
2025-05-04 22:02:07.861 +08:00 [INF] 移动
2025-05-04 22:02:08.095 +08:00 [ERR] 角色卡点
2025-05-04 22:02:08.095 +08:00 [INF] 移动
2025-05-04 22:02:08.328 +08:00 [ERR] 角色卡点
2025-05-04 22:02:08.328 +08:00 [INF] 移动
2025-05-04 22:02:08.561 +08:00 [ERR] 角色卡点
2025-05-04 22:02:08.561 +08:00 [INF] 移动
2025-05-04 22:02:08.794 +08:00 [ERR] 角色卡点
2025-05-04 22:02:08.794 +08:00 [INF] 移动
2025-05-04 22:02:09.027 +08:00 [ERR] 角色卡点
2025-05-04 22:02:09.027 +08:00 [INF] 移动
2025-05-04 22:02:09.260 +08:00 [ERR] 角色卡点
2025-05-04 22:02:09.294 +08:00 [INF] 移动
2025-05-04 22:02:09.494 +08:00 [ERR] 角色卡点
2025-05-04 22:02:09.494 +08:00 [INF] 移动
2025-05-04 22:02:09.728 +08:00 [ERR] 角色卡点
2025-05-04 22:02:09.728 +08:00 [INF] 移动
2025-05-04 22:02:09.960 +08:00 [ERR] 角色卡点
2025-05-04 22:02:09.995 +08:00 [INF] 移动
2025-05-04 22:02:10.195 +08:00 [ERR] 角色卡点
2025-05-04 22:02:10.195 +08:00 [INF] 移动
2025-05-04 22:02:10.428 +08:00 [ERR] 角色卡点
2025-05-04 22:02:10.428 +08:00 [INF] 移动
2025-05-04 22:02:10.628 +08:00 [ERR] 角色卡点
2025-05-04 22:02:10.628 +08:00 [INF] 移动
2025-05-04 22:02:10.861 +08:00 [ERR] 角色卡点
2025-05-04 22:02:10.861 +08:00 [INF] 移动
2025-05-04 22:02:11.095 +08:00 [ERR] 角色卡点
2025-05-04 22:02:11.095 +08:00 [INF] 移动
2025-05-04 22:02:11.328 +08:00 [ERR] 角色卡点
2025-05-04 22:02:11.328 +08:00 [INF] 移动
2025-05-04 22:02:11.561 +08:00 [ERR] 角色卡点
2025-05-04 22:02:11.561 +08:00 [INF] 移动
2025-05-04 22:02:11.794 +08:00 [ERR] 角色卡点
2025-05-04 22:02:11.794 +08:00 [INF] 移动
2025-05-04 22:02:12.027 +08:00 [ERR] 角色卡点
2025-05-04 22:02:12.027 +08:00 [INF] 移动
2025-05-04 22:02:12.261 +08:00 [ERR] 角色卡点
2025-05-04 22:02:12.261 +08:00 [INF] 移动
2025-05-04 22:02:12.494 +08:00 [ERR] 角色卡点
2025-05-04 22:02:12.494 +08:00 [INF] 移动
2025-05-04 22:02:12.727 +08:00 [ERR] 角色卡点
2025-05-04 22:02:12.727 +08:00 [INF] 移动
2025-05-04 22:02:12.961 +08:00 [ERR] 角色卡点
2025-05-04 22:02:12.961 +08:00 [INF] 移动
2025-05-04 22:02:13.195 +08:00 [ERR] 角色卡点
2025-05-04 22:02:13.195 +08:00 [INF] 移动
2025-05-04 22:02:13.429 +08:00 [ERR] 角色卡点
2025-05-04 22:02:13.429 +08:00 [INF] 移动
2025-05-04 22:02:13.661 +08:00 [ERR] 角色卡点
2025-05-04 22:02:13.661 +08:00 [INF] 移动
2025-05-04 22:02:13.894 +08:00 [ERR] 角色卡点
2025-05-04 22:02:13.927 +08:00 [INF] 移动
2025-05-04 22:02:14.127 +08:00 [ERR] 角色卡点
2025-05-04 22:02:14.127 +08:00 [INF] 移动
2025-05-04 22:02:14.360 +08:00 [ERR] 角色卡点
2025-05-04 22:02:14.394 +08:00 [INF] 移动
2025-05-04 22:02:14.594 +08:00 [ERR] 角色卡点
2025-05-04 22:02:14.628 +08:00 [INF] 移动
2025-05-04 22:02:14.828 +08:00 [ERR] 角色卡点
2025-05-04 22:02:14.861 +08:00 [INF] 移动
2025-05-04 22:02:15.060 +08:00 [ERR] 角色卡点
2025-05-04 22:02:15.094 +08:00 [INF] 移动
2025-05-04 22:02:15.295 +08:00 [ERR] 角色卡点
2025-05-04 22:02:15.327 +08:00 [INF] 移动
2025-05-04 22:02:15.527 +08:00 [ERR] 角色卡点
2025-05-04 22:02:15.561 +08:00 [INF] 移动
2025-05-04 22:02:15.760 +08:00 [ERR] 角色卡点
2025-05-04 22:02:15.794 +08:00 [INF] 移动
2025-05-04 22:02:15.995 +08:00 [ERR] 角色卡点
2025-05-04 22:02:16.028 +08:00 [INF] 移动
2025-05-04 22:02:16.228 +08:00 [ERR] 角色卡点
2025-05-04 22:02:16.261 +08:00 [INF] 移动
2025-05-04 22:02:16.461 +08:00 [ERR] 角色卡点
2025-05-04 22:02:16.495 +08:00 [INF] 移动
2025-05-04 22:02:16.695 +08:00 [ERR] 角色卡点
2025-05-04 22:02:16.729 +08:00 [INF] 移动
2025-05-04 22:02:16.928 +08:00 [ERR] 角色卡点
2025-05-04 22:02:16.961 +08:00 [INF] 移动
2025-05-04 22:02:17.160 +08:00 [ERR] 角色卡点
2025-05-04 22:02:17.195 +08:00 [INF] 移动
2025-05-04 22:02:17.394 +08:00 [ERR] 角色卡点
2025-05-04 22:02:17.427 +08:00 [INF] 移动
2025-05-04 22:02:17.629 +08:00 [ERR] 角色卡点
2025-05-04 22:02:17.661 +08:00 [INF] 移动
2025-05-04 22:02:17.861 +08:00 [ERR] 角色卡点
2025-05-04 22:02:17.894 +08:00 [INF] 移动
2025-05-04 22:02:18.095 +08:00 [ERR] 角色卡点
2025-05-04 22:02:18.128 +08:00 [INF] 移动
2025-05-04 22:02:18.328 +08:00 [ERR] 角色卡点
2025-05-04 22:02:18.360 +08:00 [INF] 移动
2025-05-04 22:02:18.561 +08:00 [ERR] 角色卡点
2025-05-04 22:02:18.594 +08:00 [INF] 移动
2025-05-04 22:02:18.794 +08:00 [ERR] 角色卡点
2025-05-04 22:02:18.828 +08:00 [INF] 移动
2025-05-04 22:02:19.027 +08:00 [ERR] 角色卡点
2025-05-04 22:02:19.062 +08:00 [INF] 移动
2025-05-04 22:02:19.261 +08:00 [ERR] 角色卡点
2025-05-04 22:02:19.294 +08:00 [INF] 移动
2025-05-04 22:02:21.760 +08:00 [ERR] 角色卡点
2025-05-04 22:02:21.794 +08:00 [INF] 移动
2025-05-04 22:02:21.961 +08:00 [ERR] 角色卡点
2025-05-04 22:02:21.995 +08:00 [INF] 移动
2025-05-04 22:02:22.194 +08:00 [ERR] 角色卡点
2025-05-04 22:02:22.227 +08:00 [INF] 移动
2025-05-04 22:02:22.427 +08:00 [ERR] 角色卡点
2025-05-04 22:02:22.461 +08:00 [INF] 移动
2025-05-04 22:02:22.661 +08:00 [ERR] 角色卡点
2025-05-04 22:02:22.694 +08:00 [INF] 移动
2025-05-04 22:02:22.894 +08:00 [ERR] 角色卡点
2025-05-04 22:02:22.894 +08:00 [INF] 移动
2025-05-04 22:02:23.127 +08:00 [ERR] 角色卡点
2025-05-04 22:02:23.127 +08:00 [INF] 移动
2025-05-04 22:02:23.362 +08:00 [ERR] 角色卡点
2025-05-04 22:02:23.362 +08:00 [INF] 移动
2025-05-04 22:02:23.595 +08:00 [ERR] 角色卡点
2025-05-04 22:02:23.629 +08:00 [INF] 移动
2025-05-04 22:02:23.827 +08:00 [ERR] 角色卡点
2025-05-04 22:02:23.861 +08:00 [INF] 移动
2025-05-04 22:02:24.060 +08:00 [ERR] 角色卡点
2025-05-04 22:02:24.094 +08:00 [INF] 移动
2025-05-04 22:02:24.295 +08:00 [ERR] 角色卡点
2025-05-04 22:02:24.328 +08:00 [INF] 移动
2025-05-04 22:02:24.528 +08:00 [ERR] 角色卡点
2025-05-04 22:02:24.561 +08:00 [INF] 移动
2025-05-04 22:02:24.761 +08:00 [ERR] 角色卡点
2025-05-04 22:02:24.795 +08:00 [INF] 移动
2025-05-04 22:02:24.961 +08:00 [ERR] 角色卡点
2025-05-04 22:02:24.994 +08:00 [INF] 移动
2025-05-04 22:02:27.795 +08:00 [ERR] 角色卡点
2025-05-04 22:02:27.795 +08:00 [INF] 移动
2025-05-04 22:02:28.027 +08:00 [ERR] 角色卡点
2025-05-04 22:02:28.027 +08:00 [INF] 移动
2025-05-04 22:02:28.261 +08:00 [ERR] 角色卡点
2025-05-04 22:02:28.261 +08:00 [INF] 移动
2025-05-04 22:02:28.495 +08:00 [ERR] 角色卡点
2025-05-04 22:02:28.529 +08:00 [INF] 移动
2025-05-04 22:02:28.728 +08:00 [ERR] 角色卡点
2025-05-04 22:02:28.761 +08:00 [INF] 移动
2025-05-04 22:02:28.960 +08:00 [ERR] 角色卡点
2025-05-04 22:02:28.994 +08:00 [INF] 移动
2025-05-04 22:02:29.194 +08:00 [ERR] 角色卡点
2025-05-04 22:02:29.228 +08:00 [INF] 移动
2025-05-04 22:02:29.428 +08:00 [ERR] 角色卡点
2025-05-04 22:02:29.428 +08:00 [INF] 移动
2025-05-04 22:02:29.627 +08:00 [ERR] 角色卡点
2025-05-04 22:02:29.627 +08:00 [INF] 移动
2025-05-04 22:02:29.861 +08:00 [ERR] 角色卡点
2025-05-04 22:02:29.861 +08:00 [INF] 移动
2025-05-04 22:02:30.094 +08:00 [ERR] 角色卡点
2025-05-04 22:02:30.094 +08:00 [INF] 移动
2025-05-04 22:02:30.327 +08:00 [ERR] 角色卡点
2025-05-04 22:02:30.361 +08:00 [INF] 移动
2025-05-04 22:02:30.560 +08:00 [ERR] 角色卡点
2025-05-04 22:02:30.595 +08:00 [INF] 移动
2025-05-04 22:02:30.761 +08:00 [ERR] 角色卡点
2025-05-04 22:02:30.794 +08:00 [INF] 移动
2025-05-04 22:02:30.995 +08:00 [ERR] 角色卡点
2025-05-04 22:02:31.027 +08:00 [INF] 移动
2025-05-04 22:02:31.261 +08:00 [ERR] 角色卡点
2025-05-04 22:02:31.262 +08:00 [INF] 移动
2025-05-04 22:02:31.461 +08:00 [ERR] 角色卡点
2025-05-04 22:02:31.461 +08:00 [INF] 移动
2025-05-04 22:02:31.694 +08:00 [ERR] 角色卡点
2025-05-04 22:02:31.694 +08:00 [INF] 移动
2025-05-04 22:02:31.927 +08:00 [ERR] 角色卡点
2025-05-04 22:02:31.928 +08:00 [INF] 移动
2025-05-04 22:02:32.161 +08:00 [ERR] 角色卡点
2025-05-04 22:02:32.194 +08:00 [INF] 移动
2025-05-04 22:02:32.395 +08:00 [ERR] 角色卡点
2025-05-04 22:02:32.427 +08:00 [INF] 移动
2025-05-04 22:02:32.629 +08:00 [ERR] 角色卡点
2025-05-04 22:02:32.661 +08:00 [INF] 移动
2025-05-04 22:02:32.828 +08:00 [ERR] 角色卡点
2025-05-04 22:02:32.860 +08:00 [INF] 移动
2025-05-04 22:02:33.094 +08:00 [ERR] 角色卡点
2025-05-04 22:02:33.094 +08:00 [INF] 移动
2025-05-04 22:02:33.329 +08:00 [ERR] 角色卡点
2025-05-04 22:02:33.329 +08:00 [INF] 移动
2025-05-04 22:02:33.562 +08:00 [ERR] 角色卡点
2025-05-04 22:02:33.562 +08:00 [INF] 移动
2025-05-04 22:02:33.794 +08:00 [ERR] 角色卡点
2025-05-04 22:02:33.794 +08:00 [INF] 移动
2025-05-04 22:02:35.195 +08:00 [ERR] 角色卡点
2025-05-04 22:02:35.195 +08:00 [INF] 移动
2025-05-04 22:02:35.428 +08:00 [ERR] 角色卡点
2025-05-04 22:02:35.428 +08:00 [INF] 移动
2025-05-04 22:02:35.661 +08:00 [ERR] 角色卡点
2025-05-04 22:02:35.661 +08:00 [INF] 移动
2025-05-04 22:02:35.894 +08:00 [ERR] 角色卡点
2025-05-04 22:02:35.894 +08:00 [INF] 移动
2025-05-04 22:02:36.127 +08:00 [ERR] 角色卡点
2025-05-04 22:02:36.127 +08:00 [INF] 移动
2025-05-04 22:02:36.361 +08:00 [ERR] 角色卡点
2025-05-04 22:02:36.361 +08:00 [INF] 移动
2025-05-04 22:02:37.295 +08:00 [ERR] 角色卡点
2025-05-04 22:02:37.327 +08:00 [INF] 移动
2025-05-04 22:02:37.495 +08:00 [ERR] 角色卡点
2025-05-04 22:02:37.528 +08:00 [INF] 移动
2025-05-04 22:02:37.728 +08:00 [ERR] 角色卡点
2025-05-04 22:02:37.761 +08:00 [INF] 移动
2025-05-04 22:02:37.961 +08:00 [ERR] 角色卡点
2025-05-04 22:02:37.995 +08:00 [INF] 移动
2025-05-04 22:02:38.328 +08:00 [ERR] 角色卡点
2025-05-04 22:02:38.328 +08:00 [INF] 移动
2025-05-04 22:02:38.628 +08:00 [ERR] 角色卡点
2025-05-04 22:02:38.861 +08:00 [ERR] 角色卡点
2025-05-04 22:02:39.095 +08:00 [ERR] 角色卡点
2025-05-04 22:02:39.328 +08:00 [ERR] 角色卡点
2025-05-04 22:02:39.595 +08:00 [ERR] 角色卡点
2025-05-04 22:02:39.828 +08:00 [ERR] 角色卡点
2025-05-04 22:02:40.094 +08:00 [ERR] 角色卡点
2025-05-04 22:02:40.294 +08:00 [ERR] 角色卡点
2025-05-04 22:02:40.528 +08:00 [ERR] 角色卡点
2025-05-04 22:02:40.762 +08:00 [ERR] 角色卡点
2025-05-04 22:02:40.961 +08:00 [ERR] 角色卡点
2025-05-04 22:02:41.195 +08:00 [ERR] 角色卡点
2025-05-04 22:02:41.528 +08:00 [ERR] 角色卡点
2025-05-04 22:02:41.794 +08:00 [ERR] 角色卡点
2025-05-04 22:02:42.028 +08:00 [ERR] 角色卡点
2025-05-04 22:02:42.228 +08:00 [ERR] 角色卡点
2025-05-04 22:02:42.461 +08:00 [ERR] 角色卡点
2025-05-04 22:02:42.695 +08:00 [ERR] 角色卡点
2025-05-04 22:02:42.961 +08:00 [ERR] 角色卡点
2025-05-04 22:02:43.162 +08:00 [ERR] 角色卡点
2025-05-04 22:02:43.362 +08:00 [ERR] 角色卡点
2025-05-04 22:02:43.594 +08:00 [ERR] 角色卡点
2025-05-04 22:02:43.827 +08:00 [ERR] 角色卡点
2025-05-04 22:02:44.060 +08:00 [ERR] 角色卡点
2025-05-04 22:02:44.294 +08:00 [ERR] 角色卡点
2025-05-04 22:02:44.594 +08:00 [ERR] 角色卡点
2025-05-04 22:02:44.827 +08:00 [ERR] 角色卡点
2025-05-04 22:02:45.061 +08:00 [ERR] 角色卡点
2025-05-04 22:02:45.261 +08:00 [ERR] 角色卡点
2025-05-04 22:02:45.729 +08:00 [ERR] 角色卡点
2025-05-04 22:02:45.927 +08:00 [ERR] 角色卡点
2025-05-04 22:02:46.162 +08:00 [ERR] 角色卡点
2025-05-04 22:02:46.395 +08:00 [ERR] 角色卡点
2025-05-04 22:02:46.627 +08:00 [ERR] 角色卡点
2025-05-04 22:02:46.861 +08:00 [ERR] 角色卡点
2025-05-04 22:02:47.095 +08:00 [ERR] 角色卡点
2025-05-04 22:02:47.261 +08:00 [ERR] 角色卡点
2025-05-04 22:02:47.531 +08:00 [ERR] 角色卡点
2025-05-04 22:02:47.760 +08:00 [ERR] 角色卡点
2025-05-04 22:02:47.994 +08:00 [ERR] 角色卡点
2025-05-04 22:02:48.228 +08:00 [ERR] 角色卡点
2025-05-04 22:02:48.462 +08:00 [ERR] 角色卡点
2025-05-04 22:02:48.695 +08:00 [ERR] 角色卡点
2025-05-04 22:02:48.927 +08:00 [ERR] 角色卡点
2025-05-04 22:02:49.162 +08:00 [ERR] 角色卡点
2025-05-04 22:02:49.394 +08:00 [ERR] 角色卡点
2025-05-04 22:02:49.594 +08:00 [ERR] 角色卡点
2025-05-04 22:02:49.827 +08:00 [ERR] 角色卡点
2025-05-04 22:02:50.061 +08:00 [ERR] 角色卡点
2025-05-04 22:02:50.294 +08:00 [ERR] 角色卡点
2025-05-04 22:02:57.128 +08:00 [ERR] 角色卡点
2025-05-04 22:02:57.361 +08:00 [ERR] 角色卡点
2025-05-04 22:02:57.595 +08:00 [ERR] 角色卡点
2025-05-04 22:02:57.827 +08:00 [ERR] 角色卡点
2025-05-04 22:02:58.061 +08:00 [ERR] 角色卡点
2025-05-04 22:02:58.294 +08:00 [ERR] 角色卡点
2025-05-04 22:02:58.561 +08:00 [ERR] 角色卡点
2025-05-04 22:02:58.794 +08:00 [ERR] 角色卡点
2025-05-04 22:02:58.994 +08:00 [ERR] 角色卡点
2025-05-04 22:02:59.394 +08:00 [ERR] 角色卡点
2025-05-04 22:02:59.594 +08:00 [ERR] 角色卡点
2025-05-04 22:02:59.827 +08:00 [ERR] 角色卡点
2025-05-04 22:03:00.060 +08:00 [ERR] 角色卡点
2025-05-04 22:03:00.295 +08:00 [ERR] 角色卡点
2025-05-04 22:03:00.527 +08:00 [ERR] 角色卡点
2025-05-04 22:03:00.760 +08:00 [ERR] 角色卡点
2025-05-04 22:03:00.995 +08:00 [ERR] 角色卡点
2025-05-04 22:03:01.228 +08:00 [ERR] 角色卡点
2025-05-04 22:03:01.461 +08:00 [ERR] 角色卡点
2025-05-04 22:03:01.695 +08:00 [ERR] 角色卡点
2025-05-04 22:03:01.928 +08:00 [ERR] 角色卡点
2025-05-04 22:03:02.162 +08:00 [ERR] 角色卡点
2025-05-04 22:03:02.394 +08:00 [ERR] 角色卡点
2025-05-04 22:03:02.629 +08:00 [ERR] 角色卡点
2025-05-04 22:03:02.860 +08:00 [ERR] 角色卡点
2025-05-04 22:03:03.095 +08:00 [ERR] 角色卡点
2025-05-04 22:03:03.328 +08:00 [ERR] 角色卡点
2025-05-04 22:03:03.561 +08:00 [ERR] 角色卡点
2025-05-04 22:03:03.761 +08:00 [ERR] 角色卡点
2025-05-04 22:03:03.994 +08:00 [ERR] 角色卡点
2025-05-04 22:03:04.228 +08:00 [ERR] 角色卡点
2025-05-04 22:03:04.462 +08:00 [ERR] 角色卡点
2025-05-04 22:03:04.662 +08:00 [ERR] 角色卡点
2025-05-04 22:03:04.895 +08:00 [ERR] 角色卡点
2025-05-04 22:03:05.128 +08:00 [ERR] 角色卡点
2025-05-04 22:03:05.362 +08:00 [ERR] 角色卡点
2025-05-04 22:03:05.594 +08:00 [ERR] 角色卡点
2025-05-04 22:03:05.828 +08:00 [ERR] 角色卡点
2025-05-04 22:03:06.027 +08:00 [ERR] 角色卡点
2025-05-04 22:03:06.260 +08:00 [ERR] 角色卡点
2025-05-04 22:03:06.494 +08:00 [ERR] 角色卡点
2025-05-04 22:03:06.727 +08:00 [ERR] 角色卡点
2025-05-04 22:03:06.961 +08:00 [ERR] 角色卡点
2025-05-04 22:03:07.194 +08:00 [ERR] 角色卡点
2025-05-04 22:03:07.428 +08:00 [ERR] 角色卡点
2025-05-04 22:03:07.660 +08:00 [ERR] 角色卡点
2025-05-04 22:03:07.896 +08:00 [ERR] 角色卡点
2025-05-04 22:03:08.127 +08:00 [ERR] 角色卡点
2025-05-04 22:03:08.360 +08:00 [ERR] 角色卡点
2025-05-04 22:03:08.595 +08:00 [ERR] 角色卡点
2025-05-04 22:03:08.828 +08:00 [ERR] 角色卡点
2025-05-04 22:03:09.027 +08:00 [ERR] 角色卡点
2025-05-04 22:03:09.261 +08:00 [ERR] 角色卡点
2025-05-04 22:03:09.494 +08:00 [ERR] 角色卡点
2025-05-04 22:03:09.727 +08:00 [ERR] 角色卡点
2025-05-04 22:03:09.961 +08:00 [ERR] 角色卡点
2025-05-04 22:03:10.194 +08:00 [ERR] 角色卡点
2025-05-04 22:03:10.429 +08:00 [ERR] 角色卡点
2025-05-04 22:03:10.664 +08:00 [ERR] 角色卡点
2025-05-04 22:03:10.894 +08:00 [ERR] 角色卡点
2025-05-04 22:03:11.127 +08:00 [ERR] 角色卡点
2025-05-04 22:03:11.361 +08:00 [ERR] 角色卡点
2025-05-04 22:03:11.595 +08:00 [ERR] 角色卡点
2025-05-04 22:03:11.828 +08:00 [ERR] 角色卡点
2025-05-04 22:03:12.064 +08:00 [ERR] 角色卡点
2025-05-04 22:03:12.295 +08:00 [ERR] 角色卡点
2025-05-04 22:03:12.527 +08:00 [ERR] 角色卡点
2025-05-04 22:03:12.762 +08:00 [ERR] 角色卡点
2025-05-04 22:03:12.995 +08:00 [ERR] 角色卡点
2025-05-04 22:03:13.227 +08:00 [ERR] 角色卡点
2025-05-04 22:06:05.927 +08:00 [ERR] 角色卡点
