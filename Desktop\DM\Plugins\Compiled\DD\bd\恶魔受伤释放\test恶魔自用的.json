{"Rules": [{"Conditions": [{"$type": "DD.ProfileManager.Conditions.StatusEffectCondition, DD", "buffId": "demon_form_spell_gem_buff", "checkType": "层数", "operator": "不包含", "threshold": 0.0, "component": null}], "delayBetweenRuns": 1.0, "Enabled": true, "Name": "变身", "SkillName": "惡魔變身", "Key": 0, "IsHold": false, "IsEvade": false, "EvadeRange": 0, "HoldTime": 1.0, "IsUserCooldown": true, "IsWalkable": true, "IsMove": false, "Rarity": []}, {"Conditions": [{"$type": "DD.ProfileManager.Conditions.StatusEffectCondition, DD", "buffId": "demon_form_spell_gem_buff", "checkType": "层数", "operator": "包含", "threshold": 0.0, "component": null}, {"$type": "DD.ProfileManager.Conditions.StatusEffectCondition, DD", "buffId": "屏障祈願", "checkType": "层数", "operator": "小于", "threshold": 95.0, "component": {"$type": "DD.ProfileManager.Component.Wait, DD", "duration": 1.0}}], "delayBetweenRuns": 0.2, "Enabled": true, "Name": "导电", "SkillName": "導電", "Key": 0, "IsHold": false, "IsEvade": true, "EvadeRange": 20, "HoldTime": 1.0, "IsUserCooldown": true, "IsWalkable": false, "IsMove": false, "Rarity": [0, 1, 2, 3]}, {"Conditions": [{"$type": "DD.ProfileManager.Conditions.StatusEffectCondition, DD", "buffId": "屏障祈願", "checkType": "层数", "operator": "大于", "threshold": 90.0, "component": null}, {"$type": "DD.ProfileManager.Conditions.VitalsCondition, DD", "operator": "大于", "vitalType": "能量护盾", "threshold": 2500, "component": null}], "delayBetweenRuns": 0.2, "Enabled": true, "Name": "祈願", "SkillName": "屏障祈願_80C00001", "Key": 0, "IsHold": false, "IsEvade": true, "EvadeRange": 20, "HoldTime": 1.0, "IsUserCooldown": true, "IsWalkable": true, "IsMove": false, "Rarity": [0, 1, 2, 3]}, {"Conditions": [], "delayBetweenRuns": 0.0, "Enabled": true, "Name": "3", "SkillName": "電球_80C00101", "Key": 0, "IsHold": false, "IsEvade": true, "EvadeRange": 0, "HoldTime": 1.0, "IsUserCooldown": false, "IsWalkable": true, "IsMove": false, "Rarity": [3, 2]}]}