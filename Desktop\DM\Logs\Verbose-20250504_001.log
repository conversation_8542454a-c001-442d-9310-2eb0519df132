2025-05-04 22:08:56.951 +08:00 [ERR] Core -> Loading SoundController failed.
2025-05-04 22:08:57.008 +08:00 [ERR] Core -> SharpDX.SharpDXException: HRESULT: [0x80070490], Module: [Unknown], ApiCode: [Unknown/Unknown], Message: 找不到元素。

   在 SharpDX.Result.CheckError()
   在 SharpDX.XAudio2.XAudio2.CreateMasteringVoice(MasteringVoice masteringVoiceOut, Int32 inputChannels, Int32 inputSampleRate, Int32 flags, String szDeviceId, Nullable`1 effectChainRef, AudioStreamCategory streamCategory)
   在 SharpDX.XAudio2.MasteringVoice..ctor(XAudio2 device, Int32 inputChannels, Int32 inputSampleRate)
   在 ExileCore.SoundController..ctor(String dir)
   在 ExileCore.Core..ctor(RenderForm form)
2025-05-04 22:08:57.009 +08:00 [INF] 模块: Atlas Helper -> : Time: 37.0964 ms. 地址:[937344] Started searching offset with:0
2025-05-04 22:08:57.009 +08:00 [INF] 模块: Game State -> : Time: 36.2274 ms. 地址:[934055] Started searching offset with:0
2025-05-04 22:08:57.009 +08:00 [INF] 模块: Area change -> : Time: 62.6576 ms. 地址:[1037465] Started searching offset with:0
2025-05-04 22:08:57.009 +08:00 [INF] 模块: BlackBarSize -> : Time: 163.9305 ms. 地址:[4510023] Started searching offset with:0
2025-05-04 22:08:57.009 +08:00 [INF] 模块: Terrain Rotator Helper -> : Time: 950.9593 ms. 地址:[25114927] Started searching offset with:0
2025-05-04 22:08:57.009 +08:00 [INF] 模块: Terrain Rotation Selector -> : Time: 988.348 ms. 地址:[25114927] Started searching offset with:0
2025-05-04 22:08:57.009 +08:00 [INF] 模块: File Root -> : Time: 1218.773 ms. 地址:[29539156] Started searching offset with:0
2025-05-04 22:08:57.009 +08:00 [INF] 模块: DiagnosticInfoType -> : Time: 2663.9771 ms. 地址:[0] Started searching offset with:600000
2025-05-04 22:08:57.009 +08:00 [INF] 初始化用时 6708.5404 毫秒.
2025-05-04 22:08:57.009 +08:00 [INF] Resize from: {X=0,Y=0,Width=1584,Height=861} to {X=560,Y=251,Width=800,Height=560}
2025-05-04 22:08:57.046 +08:00 [INF] DD loaded in 129.7861 ms.
2025-05-04 22:08:57.062 +08:00 [ERR] 线程1启动
2025-05-04 22:08:57.062 +08:00 [ERR] 线程2启动
2025-05-04 22:08:57.062 +08:00 [INF] 监测线程启动
2025-05-04 22:08:57.062 +08:00 [INF] 子目录列表：
2025-05-04 22:08:57.062 +08:00 [INF] 冰之打击武僧
2025-05-04 22:08:57.135 +08:00 [INF] 召唤
2025-05-04 22:08:57.135 +08:00 [INF] 召唤弓箭手
2025-05-04 22:08:57.135 +08:00 [INF] 囚神僧作者自己用的BD
2025-05-04 22:08:57.135 +08:00 [INF] 囚神杖武僧
2025-05-04 22:08:57.135 +08:00 [INF] 恶魔受伤释放
2025-05-04 22:08:57.135 +08:00 [INF] 恶魔受伤释放双祈愿
2025-05-04 22:08:57.135 +08:00 [INF] 游侠
2025-05-04 22:08:57.135 +08:00 [INF] 电武僧
2025-05-04 22:08:57.135 +08:00 [INF] 电球
2025-05-04 22:08:57.135 +08:00 [INF] 自己用
2025-05-04 22:08:57.135 +08:00 [INF] 闪电
2025-05-04 22:08:57.135 +08:00 [INF] 闪电箭
2025-05-04 22:08:57.135 +08:00 [INF] 阴抓BD模板
2025-05-04 22:08:57.135 +08:00 [INF] 陰爪BD先取消走A
2025-05-04 22:08:57.135 +08:00 [INF] 骑鸟电矛
2025-05-04 22:08:57.169 +08:00 [INF] DD -> 初始化用时: 90.8282 毫秒.
2025-05-04 22:08:57.169 +08:00 [INF] 加载地图数据耗时：2
2025-05-04 22:08:57.169 +08:00 [INF] 当前地图： (0)  N：-1 T：0
2025-05-04 22:09:46.861 +08:00 [ERR] Core -> Loading SoundController failed.
2025-05-04 22:09:46.913 +08:00 [ERR] Core -> SharpDX.SharpDXException: HRESULT: [0x80070490], Module: [Unknown], ApiCode: [Unknown/Unknown], Message: 找不到元素。

   在 SharpDX.Result.CheckError()
   在 SharpDX.XAudio2.XAudio2.CreateMasteringVoice(MasteringVoice masteringVoiceOut, Int32 inputChannels, Int32 inputSampleRate, Int32 flags, String szDeviceId, Nullable`1 effectChainRef, AudioStreamCategory streamCategory)
   在 SharpDX.XAudio2.MasteringVoice..ctor(XAudio2 device, Int32 inputChannels, Int32 inputSampleRate)
   在 ExileCore.SoundController..ctor(String dir)
   在 ExileCore.Core..ctor(RenderForm form)
2025-05-04 22:09:46.913 +08:00 [INF] 模块: Area change -> : Time: 39.6568 ms. 地址:[1037465] Started searching offset with:0
2025-05-04 22:09:46.913 +08:00 [INF] 模块: Atlas Helper -> : Time: 59.5499 ms. 地址:[937344] Started searching offset with:0
2025-05-04 22:09:46.913 +08:00 [INF] 模块: Game State -> : Time: 62.7629 ms. 地址:[934055] Started searching offset with:0
2025-05-04 22:09:46.913 +08:00 [INF] 模块: BlackBarSize -> : Time: 190.5608 ms. 地址:[4510023] Started searching offset with:0
2025-05-04 22:09:46.913 +08:00 [INF] 模块: Terrain Rotation Selector -> : Time: 961.3918 ms. 地址:[25114927] Started searching offset with:0
2025-05-04 22:09:46.913 +08:00 [INF] 模块: Terrain Rotator Helper -> : Time: 974.3457 ms. 地址:[25114927] Started searching offset with:0
2025-05-04 22:09:46.913 +08:00 [INF] 模块: File Root -> : Time: 1249.5632 ms. 地址:[29539156] Started searching offset with:0
2025-05-04 22:09:46.913 +08:00 [INF] 模块: DiagnosticInfoType -> : Time: 2668.062 ms. 地址:[0] Started searching offset with:600000
2025-05-04 22:09:46.913 +08:00 [INF] 初始化用时 6625.1805 毫秒.
2025-05-04 22:09:46.913 +08:00 [INF] Resize from: {X=0,Y=0,Width=1584,Height=861} to {X=560,Y=251,Width=800,Height=560}
2025-05-04 22:09:46.977 +08:00 [INF] DD loaded in 145.4357 ms.
2025-05-04 22:09:47.012 +08:00 [ERR] 线程1启动
2025-05-04 22:09:47.012 +08:00 [ERR] 线程2启动
2025-05-04 22:09:47.012 +08:00 [INF] 监测线程启动
2025-05-04 22:09:47.012 +08:00 [INF] 子目录列表：
2025-05-04 22:09:47.012 +08:00 [INF] 冰之打击武僧
2025-05-04 22:09:47.077 +08:00 [INF] 召唤
2025-05-04 22:09:47.077 +08:00 [INF] 召唤弓箭手
2025-05-04 22:09:47.077 +08:00 [INF] 囚神僧作者自己用的BD
2025-05-04 22:09:47.077 +08:00 [INF] 囚神杖武僧
2025-05-04 22:09:47.077 +08:00 [INF] 恶魔受伤释放
2025-05-04 22:09:47.077 +08:00 [INF] 恶魔受伤释放双祈愿
2025-05-04 22:09:47.077 +08:00 [INF] 游侠
2025-05-04 22:09:47.077 +08:00 [INF] 电武僧
2025-05-04 22:09:47.077 +08:00 [INF] 电球
2025-05-04 22:09:47.077 +08:00 [INF] 自己用
2025-05-04 22:09:47.077 +08:00 [INF] 闪电
2025-05-04 22:09:47.077 +08:00 [INF] 闪电箭
2025-05-04 22:09:47.077 +08:00 [INF] 阴抓BD模板
2025-05-04 22:09:47.077 +08:00 [INF] 陰爪BD先取消走A
2025-05-04 22:09:47.077 +08:00 [INF] 骑鸟电矛
2025-05-04 22:09:47.077 +08:00 [INF] DD -> 初始化用时: 92.5139 毫秒.
2025-05-04 22:09:47.110 +08:00 [INF] 加载地图数据耗时：2
2025-05-04 22:09:47.110 +08:00 [INF] 当前地图： (0)  N：-1 T：0
2025-05-04 22:10:36.885 +08:00 [ERR] Core -> Loading SoundController failed.
2025-05-04 22:10:36.936 +08:00 [ERR] Core -> SharpDX.SharpDXException: HRESULT: [0x80070490], Module: [Unknown], ApiCode: [Unknown/Unknown], Message: 找不到元素。

   在 SharpDX.Result.CheckError()
   在 SharpDX.XAudio2.XAudio2.CreateMasteringVoice(MasteringVoice masteringVoiceOut, Int32 inputChannels, Int32 inputSampleRate, Int32 flags, String szDeviceId, Nullable`1 effectChainRef, AudioStreamCategory streamCategory)
   在 SharpDX.XAudio2.MasteringVoice..ctor(XAudio2 device, Int32 inputChannels, Int32 inputSampleRate)
   在 ExileCore.SoundController..ctor(String dir)
   在 ExileCore.Core..ctor(RenderForm form)
2025-05-04 22:10:36.936 +08:00 [INF] 模块: Game State -> : Time: 35.9699 ms. 地址:[934055] Started searching offset with:0
2025-05-04 22:10:36.936 +08:00 [INF] 模块: Atlas Helper -> : Time: 36.7643 ms. 地址:[937344] Started searching offset with:0
2025-05-04 22:10:36.936 +08:00 [INF] 模块: Area change -> : Time: 38.4856 ms. 地址:[1037465] Started searching offset with:0
2025-05-04 22:10:36.936 +08:00 [INF] 模块: BlackBarSize -> : Time: 159.9987 ms. 地址:[4510023] Started searching offset with:0
2025-05-04 22:10:36.936 +08:00 [INF] 模块: Terrain Rotation Selector -> : Time: 975.3782 ms. 地址:[25114927] Started searching offset with:0
2025-05-04 22:10:36.936 +08:00 [INF] 模块: Terrain Rotator Helper -> : Time: 984.9114 ms. 地址:[25114927] Started searching offset with:0
2025-05-04 22:10:36.936 +08:00 [INF] 模块: File Root -> : Time: 1198.5899 ms. 地址:[29539156] Started searching offset with:0
2025-05-04 22:10:36.936 +08:00 [INF] 模块: DiagnosticInfoType -> : Time: 2629.1732 ms. 地址:[0] Started searching offset with:600000
2025-05-04 22:10:36.936 +08:00 [INF] 初始化用时 6623.2475 毫秒.
2025-05-04 22:10:36.936 +08:00 [INF] Resize from: {X=0,Y=0,Width=1584,Height=861} to {X=560,Y=251,Width=800,Height=560}
2025-05-04 22:10:36.986 +08:00 [INF] DD loaded in 132.9384 ms.
2025-05-04 22:10:37.005 +08:00 [ERR] 线程1启动
2025-05-04 22:10:37.005 +08:00 [ERR] 线程2启动
2025-05-04 22:10:37.005 +08:00 [INF] 监测线程启动
2025-05-04 22:10:37.005 +08:00 [INF] 子目录列表：
2025-05-04 22:10:37.005 +08:00 [INF] 冰之打击武僧
2025-05-04 22:10:37.070 +08:00 [INF] 召唤
2025-05-04 22:10:37.070 +08:00 [INF] 召唤弓箭手
2025-05-04 22:10:37.070 +08:00 [INF] 囚神僧作者自己用的BD
2025-05-04 22:10:37.070 +08:00 [INF] 囚神杖武僧
2025-05-04 22:10:37.070 +08:00 [INF] 恶魔受伤释放
2025-05-04 22:10:37.070 +08:00 [INF] 恶魔受伤释放双祈愿
2025-05-04 22:10:37.070 +08:00 [INF] 游侠
2025-05-04 22:10:37.070 +08:00 [INF] 电武僧
2025-05-04 22:10:37.070 +08:00 [INF] 电球
2025-05-04 22:10:37.070 +08:00 [INF] 自己用
2025-05-04 22:10:37.070 +08:00 [INF] 闪电
2025-05-04 22:10:37.070 +08:00 [INF] 闪电箭
2025-05-04 22:10:37.070 +08:00 [INF] 阴抓BD模板
2025-05-04 22:10:37.070 +08:00 [INF] 陰爪BD先取消走A
2025-05-04 22:10:37.070 +08:00 [INF] 骑鸟电矛
2025-05-04 22:10:37.103 +08:00 [INF] DD -> 初始化用时: 78.7146 毫秒.
2025-05-04 22:10:37.103 +08:00 [INF] 加载地图数据耗时：2
2025-05-04 22:10:37.103 +08:00 [INF] 当前地图： (0)  N：-1 T：0
2025-05-04 22:17:00.556 +08:00 [ERR] Core -> Loading SoundController failed.
2025-05-04 22:17:00.607 +08:00 [ERR] Core -> SharpDX.SharpDXException: HRESULT: [0x80070490], Module: [Unknown], ApiCode: [Unknown/Unknown], Message: 找不到元素。

   在 SharpDX.Result.CheckError()
   在 SharpDX.XAudio2.XAudio2.CreateMasteringVoice(MasteringVoice masteringVoiceOut, Int32 inputChannels, Int32 inputSampleRate, Int32 flags, String szDeviceId, Nullable`1 effectChainRef, AudioStreamCategory streamCategory)
   在 SharpDX.XAudio2.MasteringVoice..ctor(XAudio2 device, Int32 inputChannels, Int32 inputSampleRate)
   在 ExileCore.SoundController..ctor(String dir)
   在 ExileCore.Core..ctor(RenderForm form)
2025-05-04 22:17:00.607 +08:00 [INF] 模块: Atlas Helper -> : Time: 36.4171 ms. 地址:[937344] Started searching offset with:0
2025-05-04 22:17:00.607 +08:00 [INF] 模块: Area change -> : Time: 38.1307 ms. 地址:[1037465] Started searching offset with:0
2025-05-04 22:17:00.607 +08:00 [INF] 模块: Game State -> : Time: 40.7417 ms. 地址:[934055] Started searching offset with:0
2025-05-04 22:17:00.608 +08:00 [INF] 模块: BlackBarSize -> : Time: 161.5838 ms. 地址:[4510023] Started searching offset with:0
2025-05-04 22:17:00.608 +08:00 [INF] 模块: Terrain Rotation Selector -> : Time: 1033.8064 ms. 地址:[25114927] Started searching offset with:0
2025-05-04 22:17:00.608 +08:00 [INF] 模块: Terrain Rotator Helper -> : Time: 1037.9859 ms. 地址:[25114927] Started searching offset with:0
2025-05-04 22:17:00.608 +08:00 [INF] 模块: File Root -> : Time: 1208.1088 ms. 地址:[29539156] Started searching offset with:0
2025-05-04 22:17:00.608 +08:00 [INF] 模块: DiagnosticInfoType -> : Time: 2703.5844 ms. 地址:[0] Started searching offset with:600000
2025-05-04 22:17:00.608 +08:00 [INF] 初始化用时 6751.2348 毫秒.
2025-05-04 22:17:00.608 +08:00 [INF] Resize from: {X=0,Y=0,Width=1584,Height=861} to {X=560,Y=251,Width=800,Height=560}
2025-05-04 22:17:00.641 +08:00 [INF] DD loaded in 129.5948 ms.
2025-05-04 22:17:00.656 +08:00 [ERR] 线程1启动
2025-05-04 22:17:00.656 +08:00 [ERR] 线程2启动
2025-05-04 22:17:00.656 +08:00 [INF] 监测线程启动
2025-05-04 22:17:00.656 +08:00 [INF] 子目录列表：
2025-05-04 22:17:00.656 +08:00 [INF] 冰之打击武僧
2025-05-04 22:17:00.731 +08:00 [INF] 召唤
2025-05-04 22:17:00.731 +08:00 [INF] 召唤弓箭手
2025-05-04 22:17:00.731 +08:00 [INF] 囚神僧作者自己用的BD
2025-05-04 22:17:00.731 +08:00 [INF] 囚神杖武僧
2025-05-04 22:17:00.731 +08:00 [INF] 恶魔受伤释放
2025-05-04 22:17:00.731 +08:00 [INF] 恶魔受伤释放双祈愿
2025-05-04 22:17:00.731 +08:00 [INF] 游侠
2025-05-04 22:17:00.731 +08:00 [INF] 电武僧
2025-05-04 22:17:00.731 +08:00 [INF] 电球
2025-05-04 22:17:00.731 +08:00 [INF] 自己用
2025-05-04 22:17:00.731 +08:00 [INF] 闪电
2025-05-04 22:17:00.731 +08:00 [INF] 闪电箭
2025-05-04 22:17:00.731 +08:00 [INF] 阴抓BD模板
2025-05-04 22:17:00.731 +08:00 [INF] 陰爪BD先取消走A
2025-05-04 22:17:00.731 +08:00 [INF] 骑鸟电矛
2025-05-04 22:17:00.764 +08:00 [INF] DD -> 初始化用时: 87.1405 毫秒.
2025-05-04 22:17:00.764 +08:00 [INF] 加载地图数据耗时：2
2025-05-04 22:17:00.764 +08:00 [INF] 当前地图： (0)  N：-1 T：0
2025-05-04 22:21:59.820 +08:00 [ERR] Core -> Loading SoundController failed.
2025-05-04 22:21:59.877 +08:00 [ERR] Core -> SharpDX.SharpDXException: HRESULT: [0x80070490], Module: [Unknown], ApiCode: [Unknown/Unknown], Message: 找不到元素。

   在 SharpDX.Result.CheckError()
   在 SharpDX.XAudio2.XAudio2.CreateMasteringVoice(MasteringVoice masteringVoiceOut, Int32 inputChannels, Int32 inputSampleRate, Int32 flags, String szDeviceId, Nullable`1 effectChainRef, AudioStreamCategory streamCategory)
   在 SharpDX.XAudio2.MasteringVoice..ctor(XAudio2 device, Int32 inputChannels, Int32 inputSampleRate)
   在 ExileCore.SoundController..ctor(String dir)
   在 ExileCore.Core..ctor(RenderForm form)
2025-05-04 22:21:59.877 +08:00 [INF] 模块: Game State -> : Time: 40.2538 ms. 地址:[934055] Started searching offset with:0
2025-05-04 22:21:59.877 +08:00 [INF] 模块: Atlas Helper -> : Time: 44.4665 ms. 地址:[937344] Started searching offset with:0
2025-05-04 22:21:59.877 +08:00 [INF] 模块: Area change -> : Time: 64.3179 ms. 地址:[1037465] Started searching offset with:0
2025-05-04 22:21:59.877 +08:00 [INF] 模块: BlackBarSize -> : Time: 224.9607 ms. 地址:[4510023] Started searching offset with:0
2025-05-04 22:21:59.877 +08:00 [INF] 模块: Terrain Rotator Helper -> : Time: 1004.7619 ms. 地址:[25114927] Started searching offset with:0
2025-05-04 22:21:59.877 +08:00 [INF] 模块: Terrain Rotation Selector -> : Time: 1090.3334 ms. 地址:[25114927] Started searching offset with:0
2025-05-04 22:21:59.877 +08:00 [INF] 模块: File Root -> : Time: 1302.9639 ms. 地址:[29539156] Started searching offset with:0
2025-05-04 22:21:59.877 +08:00 [INF] 模块: DiagnosticInfoType -> : Time: 2615.9848 ms. 地址:[0] Started searching offset with:600000
2025-05-04 22:21:59.877 +08:00 [INF] 区域刷新
2025-05-04 22:21:59.877 +08:00 [INF] 初始化用时 6927.7106 毫秒.
2025-05-04 22:21:59.877 +08:00 [INF] Resize from: {X=0,Y=0,Width=1584,Height=861} to {X=560,Y=251,Width=800,Height=560}
2025-05-04 22:21:59.918 +08:00 [INF] DD loaded in 152.9874 ms.
2025-05-04 22:21:59.919 +08:00 [ERR] 线程1启动
2025-05-04 22:21:59.919 +08:00 [ERR] 线程2启动
2025-05-04 22:21:59.936 +08:00 [INF] 监测线程启动
2025-05-04 22:21:59.936 +08:00 [INF] 子目录列表：
2025-05-04 22:21:59.936 +08:00 [INF] 冰之打击武僧
2025-05-04 22:22:00.062 +08:00 [INF] 召唤
2025-05-04 22:22:00.062 +08:00 [INF] 召唤弓箭手
2025-05-04 22:22:00.062 +08:00 [INF] 囚神僧作者自己用的BD
2025-05-04 22:22:00.062 +08:00 [INF] 囚神杖武僧
2025-05-04 22:22:00.062 +08:00 [INF] 恶魔受伤释放
2025-05-04 22:22:00.062 +08:00 [INF] 恶魔受伤释放双祈愿
2025-05-04 22:22:00.062 +08:00 [INF] 游侠
2025-05-04 22:22:00.062 +08:00 [INF] 电武僧
2025-05-04 22:22:00.062 +08:00 [INF] 电球
2025-05-04 22:22:00.062 +08:00 [INF] 自己用
2025-05-04 22:22:00.062 +08:00 [INF] 闪电
2025-05-04 22:22:00.062 +08:00 [INF] 闪电箭
2025-05-04 22:22:00.062 +08:00 [INF] 阴抓BD模板
2025-05-04 22:22:00.062 +08:00 [INF] 陰爪BD先取消走A
2025-05-04 22:22:00.063 +08:00 [INF] 骑鸟电矛
2025-05-04 22:22:00.063 +08:00 [INF] DD -> 初始化用时: 141.6871 毫秒.
2025-05-04 22:22:00.362 +08:00 [INF] 加载地图数据耗时：245
2025-05-04 22:22:00.362 +08:00 [INF] 当前地图：藏身處：殞落 (65) HideoutFelled N：1 T：1
2025-05-04 22:22:04.129 +08:00 [ERR] 角色卡点
2025-05-04 22:22:04.129 +08:00 [INF] [脱困移动] 移动到随机点: X:222.6691 Y:221.558
2025-05-04 22:22:04.370 +08:00 [ERR] 角色卡点
2025-05-04 22:22:04.395 +08:00 [INF] [脱困移动] 移动到随机点: X:230.7776 Y:228.6181
2025-05-04 22:22:04.595 +08:00 [ERR] 角色卡点
2025-05-04 22:22:04.829 +08:00 [ERR] 角色卡点
2025-05-04 22:22:05.061 +08:00 [ERR] 角色卡点
2025-05-04 22:22:05.295 +08:00 [ERR] 角色卡点
2025-05-04 22:22:05.495 +08:00 [ERR] 角色卡点
2025-05-04 22:22:05.696 +08:00 [ERR] 角色卡点
2025-05-04 22:22:05.929 +08:00 [ERR] 角色卡点
2025-05-04 22:22:06.162 +08:00 [ERR] 角色卡点
2025-05-04 22:22:06.428 +08:00 [ERR] 角色卡点
2025-05-04 22:22:06.629 +08:00 [ERR] 角色卡点
2025-05-04 22:22:06.862 +08:00 [ERR] 角色卡点
2025-05-04 22:22:07.095 +08:00 [ERR] 角色卡点
2025-05-04 22:22:07.329 +08:00 [ERR] 角色卡点
2025-05-04 22:22:07.562 +08:00 [ERR] 角色卡点
2025-05-04 22:22:07.829 +08:00 [ERR] 角色卡点
2025-05-04 22:22:08.062 +08:00 [ERR] 角色卡点
2025-05-04 22:22:08.295 +08:00 [ERR] 角色卡点
2025-05-04 22:22:08.534 +08:00 [ERR] 角色卡点
2025-05-04 22:22:08.761 +08:00 [ERR] 角色卡点
2025-05-04 22:22:08.996 +08:00 [ERR] 角色卡点
2025-05-04 22:22:09.229 +08:00 [ERR] 角色卡点
2025-05-04 22:22:09.462 +08:00 [ERR] 角色卡点
2025-05-04 22:22:09.694 +08:00 [ERR] 角色卡点
2025-05-04 22:22:09.929 +08:00 [ERR] 角色卡点
2025-05-04 22:22:10.162 +08:00 [ERR] 角色卡点
2025-05-04 22:22:10.394 +08:00 [ERR] 角色卡点
2025-05-04 22:22:10.628 +08:00 [ERR] 角色卡点
2025-05-04 22:22:10.828 +08:00 [ERR] 角色卡点
2025-05-04 22:22:11.062 +08:00 [ERR] 角色卡点
2025-05-04 22:22:11.262 +08:00 [ERR] 角色卡点
2025-05-04 22:22:11.462 +08:00 [ERR] 角色卡点
2025-05-04 22:22:11.661 +08:00 [ERR] 角色卡点
2025-05-04 22:22:11.861 +08:00 [ERR] 角色卡点
2025-05-04 22:22:12.129 +08:00 [ERR] 角色卡点
2025-05-04 22:22:12.362 +08:00 [ERR] 角色卡点
2025-05-04 22:22:12.595 +08:00 [ERR] 角色卡点
2025-05-04 22:22:12.829 +08:00 [ERR] 角色卡点
2025-05-04 22:22:13.062 +08:00 [ERR] 角色卡点
2025-05-04 22:22:13.295 +08:00 [ERR] 角色卡点
2025-05-04 22:22:13.495 +08:00 [ERR] 角色卡点
2025-05-04 22:22:13.730 +08:00 [ERR] 角色卡点
2025-05-04 22:22:13.963 +08:00 [ERR] 角色卡点
2025-05-04 22:22:14.228 +08:00 [ERR] 角色卡点
2025-05-04 22:22:14.463 +08:00 [ERR] 角色卡点
2025-05-04 22:22:14.696 +08:00 [ERR] 角色卡点
2025-05-04 22:22:14.929 +08:00 [ERR] 角色卡点
2025-05-04 22:22:15.129 +08:00 [ERR] 角色卡点
2025-05-04 22:22:15.361 +08:00 [ERR] 角色卡点
2025-05-04 22:22:15.595 +08:00 [ERR] 角色卡点
2025-05-04 22:22:15.829 +08:00 [ERR] 角色卡点
2025-05-04 22:22:16.096 +08:00 [ERR] 角色卡点
2025-05-04 22:22:16.329 +08:00 [ERR] 角色卡点
2025-05-04 22:22:16.563 +08:00 [ERR] 角色卡点
2025-05-04 22:22:16.795 +08:00 [ERR] 角色卡点
2025-05-04 22:22:17.029 +08:00 [ERR] 角色卡点
2025-05-04 22:22:17.229 +08:00 [ERR] 角色卡点
2025-05-04 22:22:17.463 +08:00 [ERR] 角色卡点
2025-05-04 22:22:17.696 +08:00 [ERR] 角色卡点
2025-05-04 22:22:17.963 +08:00 [ERR] 角色卡点
2025-05-04 22:22:18.195 +08:00 [ERR] 角色卡点
2025-05-04 22:22:18.428 +08:00 [ERR] 角色卡点
2025-05-04 22:24:24.728 +08:00 [ERR] 角色卡点
2025-05-04 22:24:24.962 +08:00 [ERR] 角色卡点
2025-05-04 22:24:25.196 +08:00 [ERR] 角色卡点
2025-05-04 22:24:25.429 +08:00 [ERR] 角色卡点
2025-05-04 22:24:25.695 +08:00 [ERR] 角色卡点
2025-05-04 22:24:25.929 +08:00 [ERR] 角色卡点
2025-05-04 22:24:26.163 +08:00 [ERR] 角色卡点
2025-05-04 22:24:26.396 +08:00 [ERR] 角色卡点
2025-05-04 22:24:26.630 +08:00 [ERR] 角色卡点
2025-05-04 22:24:26.861 +08:00 [ERR] 角色卡点
2025-05-04 22:24:27.096 +08:00 [ERR] 角色卡点
2025-05-04 22:24:27.328 +08:00 [ERR] 角色卡点
2025-05-04 22:24:27.562 +08:00 [ERR] 角色卡点
2025-05-04 22:24:27.829 +08:00 [ERR] 角色卡点
2025-05-04 22:24:28.061 +08:00 [ERR] 角色卡点
2025-05-04 22:24:28.294 +08:00 [ERR] 角色卡点
2025-05-04 22:24:28.529 +08:00 [ERR] 角色卡点
2025-05-04 22:24:28.762 +08:00 [ERR] 角色卡点
2025-05-04 22:24:28.962 +08:00 [ERR] 角色卡点
2025-05-04 22:24:29.196 +08:00 [ERR] 角色卡点
2025-05-04 22:24:29.428 +08:00 [ERR] 角色卡点
2025-05-04 22:24:29.629 +08:00 [ERR] 角色卡点
2025-05-04 22:24:29.862 +08:00 [ERR] 角色卡点
2025-05-04 22:24:30.095 +08:00 [ERR] 角色卡点
2025-05-04 22:24:30.329 +08:00 [ERR] 角色卡点
2025-05-04 22:24:30.529 +08:00 [ERR] 角色卡点
2025-05-04 22:24:30.762 +08:00 [ERR] 角色卡点
2025-05-04 22:24:30.962 +08:00 [ERR] 角色卡点
2025-05-04 22:24:31.195 +08:00 [ERR] 角色卡点
2025-05-04 22:24:31.429 +08:00 [ERR] 角色卡点
2025-05-04 22:24:31.629 +08:00 [ERR] 角色卡点
2025-05-04 22:24:31.861 +08:00 [ERR] 角色卡点
2025-05-04 22:24:32.129 +08:00 [ERR] 角色卡点
2025-05-04 22:24:32.362 +08:00 [ERR] 角色卡点
2025-05-04 22:24:32.595 +08:00 [ERR] 角色卡点
2025-05-04 22:24:32.828 +08:00 [ERR] 角色卡点
2025-05-04 22:24:33.029 +08:00 [ERR] 角色卡点
2025-05-04 22:24:42.462 +08:00 [INF] Resize from: {X=560,Y=251,Width=800,Height=560} to {X=0,Y=23,Width=1920,Height=1017}
2025-05-04 22:24:51.528 +08:00 [INF] Resize from: {X=0,Y=23,Width=1920,Height=1017} to {X=560,Y=251,Width=800,Height=600}
2025-05-04 22:24:55.162 +08:00 [INF] Resize from: {X=560,Y=251,Width=800,Height=600} to {X=0,Y=23,Width=1920,Height=1017}
2025-05-04 22:24:59.795 +08:00 [ERR] 角色卡点
2025-05-04 22:25:00.062 +08:00 [ERR] 角色卡点
2025-05-04 22:25:00.329 +08:00 [ERR] 角色卡点
2025-05-04 22:25:00.561 +08:00 [ERR] 角色卡点
2025-05-04 22:25:00.829 +08:00 [ERR] 角色卡点
2025-05-04 22:25:01.095 +08:00 [ERR] 角色卡点
2025-05-04 22:25:01.328 +08:00 [ERR] 角色卡点
2025-05-04 22:25:01.597 +08:00 [ERR] 角色卡点
2025-05-04 22:25:01.895 +08:00 [ERR] 角色卡点
2025-05-04 22:25:02.130 +08:00 [ERR] 角色卡点
2025-05-04 22:25:02.361 +08:00 [ERR] 角色卡点
2025-05-04 22:25:02.596 +08:00 [ERR] 角色卡点
2025-05-04 22:25:02.861 +08:00 [ERR] 角色卡点
2025-05-04 22:25:03.095 +08:00 [ERR] 角色卡点
2025-05-04 22:25:03.329 +08:00 [ERR] 角色卡点
2025-05-04 22:25:03.595 +08:00 [ERR] 角色卡点
2025-05-04 22:25:03.829 +08:00 [ERR] 角色卡点
2025-05-04 22:25:04.063 +08:00 [ERR] 角色卡点
2025-05-04 22:25:04.296 +08:00 [ERR] 角色卡点
2025-05-04 22:25:04.563 +08:00 [ERR] 角色卡点
2025-05-04 22:25:04.829 +08:00 [ERR] 角色卡点
2025-05-04 22:25:05.061 +08:00 [ERR] 角色卡点
2025-05-04 22:25:05.295 +08:00 [ERR] 角色卡点
2025-05-04 22:25:05.561 +08:00 [ERR] 角色卡点
2025-05-04 22:25:05.794 +08:00 [ERR] 角色卡点
2025-05-04 22:25:06.061 +08:00 [ERR] 角色卡点
2025-05-04 22:25:06.296 +08:00 [ERR] 角色卡点
2025-05-04 22:25:06.528 +08:00 [ERR] 角色卡点
2025-05-04 22:25:06.829 +08:00 [ERR] 角色卡点
2025-05-04 22:25:07.095 +08:00 [ERR] 角色卡点
2025-05-04 22:25:07.361 +08:00 [ERR] 角色卡点
2025-05-04 22:25:07.562 +08:00 [ERR] 角色卡点
2025-05-04 22:25:07.828 +08:00 [ERR] 角色卡点
2025-05-04 22:25:08.028 +08:00 [ERR] 角色卡点
2025-05-04 22:25:08.294 +08:00 [ERR] 角色卡点
2025-05-04 22:25:08.562 +08:00 [ERR] 角色卡点
2025-05-04 22:25:08.794 +08:00 [ERR] 角色卡点
2025-05-04 22:25:09.062 +08:00 [ERR] 角色卡点
2025-05-04 22:25:09.296 +08:00 [ERR] 角色卡点
2025-05-04 22:25:09.529 +08:00 [ERR] 角色卡点
2025-05-04 22:25:09.796 +08:00 [ERR] 角色卡点
2025-05-04 22:25:10.028 +08:00 [ERR] 角色卡点
2025-05-04 22:25:10.262 +08:00 [ERR] 角色卡点
2025-05-04 22:25:10.496 +08:00 [ERR] 角色卡点
2025-05-04 22:25:10.762 +08:00 [ERR] 角色卡点
2025-05-04 22:25:11.029 +08:00 [ERR] 角色卡点
2025-05-04 22:25:11.296 +08:00 [ERR] 角色卡点
2025-05-04 22:25:11.561 +08:00 [ERR] 角色卡点
2025-05-04 22:25:11.795 +08:00 [ERR] 角色卡点
2025-05-04 22:25:12.029 +08:00 [ERR] 角色卡点
2025-05-04 22:25:12.329 +08:00 [ERR] 角色卡点
2025-05-04 22:25:12.562 +08:00 [ERR] 角色卡点
2025-05-04 22:25:12.828 +08:00 [ERR] 角色卡点
2025-05-04 22:25:13.065 +08:00 [ERR] 角色卡点
2025-05-04 22:25:13.330 +08:00 [ERR] 角色卡点
2025-05-04 22:25:13.599 +08:00 [ERR] 角色卡点
2025-05-04 22:25:13.874 +08:00 [ERR] 角色卡点
2025-05-04 22:25:14.128 +08:00 [ERR] 角色卡点
