# 鼠标键盘录制器

一个功能强大的鼠标和键盘动作录制与回放工具，支持快捷键控制、循环播放等功能。

## 功能特性

- 🎯 **录制功能**: 录制鼠标移动、点击、滚轮和键盘按键
- ⌨️ **快捷键控制**: 全局快捷键控制录制和播放
- 🔄 **循环播放**: 支持设置循环次数或无限循环
- 💾 **保存/加载**: 可以保存录制到文件并重新加载
- 🖥️ **图形界面**: 直观的GUI界面，易于使用
- ⏸️ **暂停/继续**: 录制和播放过程中可以暂停和继续

## 安装依赖

### 方法1: 使用批处理文件（推荐）
双击运行 `install_requirements.bat` 文件

### 方法2: 手动安装
```bash
pip install pynput
```

## 使用方法

### 启动程序
```bash
python mouse_keyboard_recorder.py
```

### 快捷键说明
- **F9**: 开始/暂停录制
- **F10**: 开始播放
- **F11**: 停止播放/录制

### 基本操作流程

1. **开始录制**
   - 点击"开始录制"按钮或按F9键
   - 程序开始录制您的鼠标和键盘操作
   - 状态栏显示"录制中..."

2. **暂停录制**
   - 再次按F9键或点击"暂停录制"按钮
   - 可以继续录制或停止录制

3. **播放录制**
   - 设置循环次数（默认1次）
   - 勾选"无限循环"可以无限重复播放
   - 点击"播放"按钮或按F10键开始播放

4. **停止播放**
   - 按F11键或点击"停止"按钮

5. **保存/加载录制**
   - 点击"保存录制"将录制保存为JSON文件
   - 点击"加载录制"从文件加载之前的录制

## 界面说明

### 主要控件
- **状态显示**: 显示当前程序状态
- **录制控制**: 开始/暂停录制、清除录制
- **播放控制**: 播放、停止播放
- **循环设置**: 设置循环次数或无限循环
- **文件操作**: 保存和加载录制文件
- **事件列表**: 显示最近录制的20个事件

### 循环播放设置
- **循环次数**: 输入数字设置重复播放次数
- **无限循环**: 勾选后将无限重复播放，直到手动停止

## 注意事项

### 权限要求
- 程序需要监听全局鼠标和键盘事件
- 在某些系统上可能需要管理员权限
- macOS用户需要在"系统偏好设置 > 安全性与隐私 > 辅助功能"中授权

### 使用建议
1. **录制前准备**: 确保要操作的应用程序已打开并准备就绪
2. **合理设置循环**: 避免设置过多循环次数导致系统负载过高
3. **及时停止**: 使用F11键可以随时停止播放
4. **保存重要录制**: 重要的操作序列建议保存到文件

### 安全提醒
- 请勿录制包含敏感信息（如密码）的操作
- 播放时请确保不会对系统造成损害
- 建议在测试环境中先验证录制的操作

## 技术特性

### 支持的事件类型
- **鼠标事件**:
  - 鼠标移动
  - 左键/右键/中键点击
  - 鼠标滚轮滚动

- **键盘事件**:
  - 普通按键按下/释放
  - 功能键（F1-F12、Ctrl、Alt等）
  - 组合键

### 文件格式
录制文件采用JSON格式存储，包含以下信息：
- 事件类型
- 时间戳
- 坐标信息（鼠标事件）
- 按键信息（键盘事件）

## 故障排除

### 常见问题

1. **程序无法启动**
   - 检查是否已安装pynput库
   - 确保Python版本兼容（建议Python 3.6+）

2. **录制不工作**
   - 检查程序是否有足够权限
   - 尝试以管理员身份运行

3. **播放不准确**
   - 确保播放环境与录制环境相似
   - 检查屏幕分辨率是否一致

4. **快捷键不响应**
   - 确保程序正在运行
   - 检查是否有其他程序占用了相同快捷键

### 获取帮助
如果遇到问题，请检查：
1. Python和pynput库是否正确安装
2. 系统权限设置是否正确
3. 是否有防病毒软件阻止程序运行

## 版本信息
- 版本: 1.0
- 支持平台: Windows, macOS, Linux
- Python版本要求: 3.6+
- 依赖库: pynput, tkinter
