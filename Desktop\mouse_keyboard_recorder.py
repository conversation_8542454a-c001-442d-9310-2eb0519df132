#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
鼠标键盘录制和回放脚本
支持录制、暂停、循环播放功能
"""

import time
import json
import threading
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from pynput import mouse, keyboard
from pynput.mouse import <PERSON><PERSON>, Listener as MouseListener
from pynput.keyboard import Key, Listener as KeyboardListener
import os

class MouseKeyboardRecorder:
    def __init__(self):
        self.recording = False
        self.playing = False
        self.paused = False
        self.events = []
        self.start_time = None
        
        # 监听器
        self.mouse_listener = None
        self.keyboard_listener = None
        
        # 控制器
        self.mouse_controller = mouse.Controller()
        self.keyboard_controller = keyboard.Controller()
        
        # 快捷键设置
        self.start_pause_key = {Key.f9}  # F9开始/暂停录制
        self.play_key = {Key.f10}        # F10播放
        self.stop_key = {Key.f11}        # F11停止
        
        # 循环设置
        self.loop_count = 1
        self.infinite_loop = False
        
        self.setup_gui()
        self.setup_global_hotkeys()
    
    def setup_gui(self):
        """设置GUI界面"""
        self.root = tk.Tk()
        self.root.title("测试版本 - 鼠标键盘录制器 v1.0")
        self.root.geometry("520x450")
        
        # 版本提示
        version_label = tk.Label(self.root, text="注意：这是测试版本，还未打包成EXE完成品",
                                font=("Arial", 10), fg="red", bg="lightyellow")
        version_label.pack(pady=5, padx=10, fill=tk.X)

        # 状态显示
        self.status_var = tk.StringVar(value="程序就绪")
        status_label = tk.Label(self.root, textvariable=self.status_var,
                               font=("Arial", 12), fg="blue")
        status_label.pack(pady=10)
        
        # 录制控制按钮
        record_frame = tk.Frame(self.root)
        record_frame.pack(pady=10)
        
        self.record_btn = tk.Button(record_frame, text="开始录制 F9",
                                   command=self.toggle_recording, width=15)
        self.record_btn.pack(side=tk.LEFT, padx=5)

        self.clear_btn = tk.Button(record_frame, text="清除录制",
                                  command=self.clear_recording, width=15)
        self.clear_btn.pack(side=tk.LEFT, padx=5)
        
        # 播放控制按钮
        play_frame = tk.Frame(self.root)
        play_frame.pack(pady=10)
        
        self.play_btn = tk.Button(play_frame, text="播放 F10",
                                 command=self.start_playback, width=15)
        self.play_btn.pack(side=tk.LEFT, padx=5)

        self.stop_btn = tk.Button(play_frame, text="停止 F11",
                                 command=self.stop_playback, width=15)
        self.stop_btn.pack(side=tk.LEFT, padx=5)
        
        # 循环设置
        loop_frame = tk.Frame(self.root)
        loop_frame.pack(pady=10)
        
        tk.Label(loop_frame, text="循环次数:").pack(side=tk.LEFT)
        
        self.loop_var = tk.StringVar(value="1")
        loop_entry = tk.Entry(loop_frame, textvariable=self.loop_var, width=10)
        loop_entry.pack(side=tk.LEFT, padx=5)
        
        self.infinite_var = tk.BooleanVar()
        infinite_check = tk.Checkbutton(loop_frame, text="无限循环", 
                                       variable=self.infinite_var)
        infinite_check.pack(side=tk.LEFT, padx=5)
        
        # 文件操作
        file_frame = tk.Frame(self.root)
        file_frame.pack(pady=10)
        
        save_btn = tk.Button(file_frame, text="保存录制", 
                            command=self.save_recording, width=15)
        save_btn.pack(side=tk.LEFT, padx=5)
        
        load_btn = tk.Button(file_frame, text="加载录制", 
                            command=self.load_recording, width=15)
        load_btn.pack(side=tk.LEFT, padx=5)
        
        # 事件列表
        list_frame = tk.Frame(self.root)
        list_frame.pack(pady=10, fill=tk.BOTH, expand=True)
        
        tk.Label(list_frame, text="录制的事件:").pack(anchor=tk.W)
        
        self.event_listbox = tk.Listbox(list_frame, height=10)
        self.event_listbox.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 快捷键说明
        help_text = """快捷键说明:
F9 开始或暂停录制
F10 开始播放录制
F11 停止播放或录制"""
        help_label = tk.Label(self.root, text=help_text, justify=tk.LEFT,
                             font=("Arial", 9), fg="gray")
        help_label.pack(pady=5)
    
    def setup_global_hotkeys(self):
        """设置全局热键监听"""
        def on_hotkey_press(key):
            try:
                if key == Key.f9:
                    self.toggle_recording()
                elif key == Key.f10:
                    self.start_playback()
                elif key == Key.f11:
                    self.stop_playback()
            except:
                pass
        
        # 启动全局键盘监听
        self.global_listener = KeyboardListener(on_press=on_hotkey_press)
        self.global_listener.start()
    
    def toggle_recording(self):
        """切换录制状态"""
        if not self.recording:
            self.start_recording()
        else:
            self.pause_recording()
    
    def start_recording(self):
        """开始录制"""
        if self.playing:
            messagebox.showwarning("警告", "正在播放中，无法开始录制")
            return
            
        self.recording = True
        self.paused = False
        self.events = []
        self.start_time = time.time()
        
        self.status_var.set("正在录制中...")
        self.record_btn.config(text="暂停录制 F9")
        self.update_event_list()
        
        # 启动鼠标和键盘监听
        self.mouse_listener = MouseListener(
            on_move=self.on_mouse_move,
            on_click=self.on_mouse_click,
            on_scroll=self.on_mouse_scroll
        )
        self.keyboard_listener = KeyboardListener(
            on_press=self.on_key_press,
            on_release=self.on_key_release
        )
        
        self.mouse_listener.start()
        self.keyboard_listener.start()
    
    def pause_recording(self):
        """暂停录制"""
        self.recording = False
        self.status_var.set(f"录制暂停 - 已录制 {len(self.events)} 个事件")
        self.record_btn.config(text="继续录制 (F9)")
        
        if self.mouse_listener:
            self.mouse_listener.stop()
        if self.keyboard_listener:
            self.keyboard_listener.stop()
    
    def clear_recording(self):
        """清除录制"""
        if self.recording:
            self.pause_recording()
        
        self.events = []
        self.status_var.set("录制已清除")
        self.record_btn.config(text="开始录制 (F9)")
        self.update_event_list()
    
    def on_mouse_move(self, x, y):
        """鼠标移动事件"""
        if self.recording and not self.paused:
            event = {
                'type': 'mouse_move',
                'x': x,
                'y': y,
                'time': time.time() - self.start_time
            }
            self.events.append(event)
            self.update_event_list()
    
    def on_mouse_click(self, x, y, button, pressed):
        """鼠标点击事件"""
        if self.recording and not self.paused:
            event = {
                'type': 'mouse_click',
                'x': x,
                'y': y,
                'button': str(button),
                'pressed': pressed,
                'time': time.time() - self.start_time
            }
            self.events.append(event)
            self.update_event_list()
    
    def on_mouse_scroll(self, x, y, dx, dy):
        """鼠标滚轮事件"""
        if self.recording and not self.paused:
            event = {
                'type': 'mouse_scroll',
                'x': x,
                'y': y,
                'dx': dx,
                'dy': dy,
                'time': time.time() - self.start_time
            }
            self.events.append(event)
            self.update_event_list()
    
    def on_key_press(self, key):
        """按键按下事件"""
        if self.recording and not self.paused:
            # 忽略热键
            if key in [Key.f9, Key.f10, Key.f11]:
                return
                
            event = {
                'type': 'key_press',
                'key': str(key),
                'time': time.time() - self.start_time
            }
            self.events.append(event)
            self.update_event_list()
    
    def on_key_release(self, key):
        """按键释放事件"""
        if self.recording and not self.paused:
            # 忽略热键
            if key in [Key.f9, Key.f10, Key.f11]:
                return

            event = {
                'type': 'key_release',
                'key': str(key),
                'time': time.time() - self.start_time
            }
            self.events.append(event)
            self.update_event_list()

    def update_event_list(self):
        """更新事件列表显示"""
        self.event_listbox.delete(0, tk.END)
        for i, event in enumerate(self.events[-20:]):  # 只显示最近20个事件
            if event['type'] == 'mouse_move':
                text = f"{i+1}. 鼠标移动到 ({event['x']}, {event['y']})"
            elif event['type'] == 'mouse_click':
                action = "按下" if event['pressed'] else "释放"
                text = f"{i+1}. 鼠标{action} {event['button']} 在 ({event['x']}, {event['y']})"
            elif event['type'] == 'mouse_scroll':
                text = f"{i+1}. 鼠标滚轮 ({event['dx']}, {event['dy']}) 在 ({event['x']}, {event['y']})"
            elif event['type'] == 'key_press':
                text = f"{i+1}. 按键按下 {event['key']}"
            elif event['type'] == 'key_release':
                text = f"{i+1}. 按键释放 {event['key']}"
            else:
                text = f"{i+1}. 未知事件"

            self.event_listbox.insert(tk.END, text)

        # 自动滚动到底部
        self.event_listbox.see(tk.END)

    def start_playback(self):
        """开始播放"""
        if self.recording:
            messagebox.showwarning("警告", "正在录制中，无法开始播放")
            return

        if not self.events:
            messagebox.showwarning("警告", "没有录制的事件可以播放")
            return

        if self.playing:
            messagebox.showinfo("信息", "已经在播放中")
            return

        # 获取循环设置
        try:
            if self.infinite_var.get():
                self.infinite_loop = True
                self.loop_count = float('inf')
            else:
                self.loop_count = int(self.loop_var.get())
                self.infinite_loop = False
        except ValueError:
            messagebox.showerror("错误", "循环次数必须是数字")
            return

        self.playing = True
        self.status_var.set("播放中...")

        # 在新线程中播放
        play_thread = threading.Thread(target=self.playback_events)
        play_thread.daemon = True
        play_thread.start()

    def playback_events(self):
        """播放事件"""
        try:
            current_loop = 0
            while (self.infinite_loop or current_loop < self.loop_count) and self.playing:
                current_loop += 1

                if not self.infinite_loop:
                    self.status_var.set(f"播放中... (第 {current_loop}/{self.loop_count} 次)")
                else:
                    self.status_var.set(f"播放中... (第 {current_loop} 次 - 无限循环)")

                start_time = time.time()
                last_event_time = 0

                for event in self.events:
                    if not self.playing:
                        break

                    # 等待到事件应该发生的时间
                    wait_time = event['time'] - last_event_time
                    if wait_time > 0:
                        time.sleep(wait_time)

                    # 执行事件
                    self.execute_event(event)
                    last_event_time = event['time']

                # 如果不是无限循环且还有下一次循环，稍作停顿
                if not self.infinite_loop and current_loop < self.loop_count and self.playing:
                    time.sleep(0.5)

            if self.playing:  # 正常结束
                self.status_var.set("播放完成")
                self.playing = False

        except Exception as e:
            messagebox.showerror("播放错误", f"播放过程中发生错误: {str(e)}")
            self.playing = False
            self.status_var.set("播放出错")

    def execute_event(self, event):
        """执行单个事件"""
        try:
            if event['type'] == 'mouse_move':
                self.mouse_controller.position = (event['x'], event['y'])

            elif event['type'] == 'mouse_click':
                button_map = {
                    'Button.left': Button.left,
                    'Button.right': Button.right,
                    'Button.middle': Button.middle
                }
                button = button_map.get(event['button'], Button.left)

                if event['pressed']:
                    self.mouse_controller.press(button)
                else:
                    self.mouse_controller.release(button)

            elif event['type'] == 'mouse_scroll':
                self.mouse_controller.scroll(event['dx'], event['dy'])

            elif event['type'] == 'key_press':
                key = self.parse_key(event['key'])
                if key:
                    self.keyboard_controller.press(key)

            elif event['type'] == 'key_release':
                key = self.parse_key(event['key'])
                if key:
                    self.keyboard_controller.release(key)

        except Exception as e:
            print(f"执行事件时出错: {e}")

    def parse_key(self, key_str):
        """解析按键字符串"""
        try:
            # 处理特殊按键
            if key_str.startswith('Key.'):
                key_name = key_str.replace('Key.', '')
                return getattr(Key, key_name, None)

            # 处理普通字符
            if key_str.startswith("'") and key_str.endswith("'"):
                return key_str[1:-1]

            return key_str
        except:
            return None

    def stop_playback(self):
        """停止播放"""
        if self.playing:
            self.playing = False
            self.status_var.set("播放已停止")
        elif self.recording:
            self.pause_recording()

    def save_recording(self):
        """保存录制到文件"""
        if not self.events:
            messagebox.showwarning("警告", "没有录制的事件可以保存")
            return

        filename = filedialog.asksaveasfilename(
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
            title="保存录制文件"
        )

        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(self.events, f, indent=2, ensure_ascii=False)
                messagebox.showinfo("成功", f"录制已保存到 {filename}")
            except Exception as e:
                messagebox.showerror("错误", f"保存失败: {str(e)}")

    def load_recording(self):
        """从文件加载录制"""
        filename = filedialog.askopenfilename(
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
            title="加载录制文件"
        )

        if filename:
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    self.events = json.load(f)
                self.update_event_list()
                self.status_var.set(f"已加载 {len(self.events)} 个事件")
                messagebox.showinfo("成功", f"录制已从 {filename} 加载")
            except Exception as e:
                messagebox.showerror("错误", f"加载失败: {str(e)}")

    def run(self):
        """运行程序"""
        try:
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
            self.root.mainloop()
        except KeyboardInterrupt:
            self.on_closing()

    def on_closing(self):
        """程序关闭时的清理"""
        self.playing = False
        self.recording = False

        if self.mouse_listener:
            self.mouse_listener.stop()
        if self.keyboard_listener:
            self.keyboard_listener.stop()
        if self.global_listener:
            self.global_listener.stop()

        self.root.destroy()

if __name__ == "__main__":
    # 检查依赖
    try:
        import pynput
    except ImportError:
        print("错误: 需要安装 pynput 库")
        print("请运行: pip install pynput")
        exit(1)

    app = MouseKeyboardRecorder()
    app.run()
