鼠标键盘录制器 - 使用说明
================================

🎉 恭喜！您的鼠标键盘录制器已成功打包为EXE文件！

📁 文件说明
-----------
✅ MouseKeyboardRecorder.exe - 主程序（可独立运行）
✅ mouse_keyboard_recorder.py - 源代码文件
✅ build_exe.bat - 打包脚本
✅ install_requirements.bat - 依赖安装脚本
✅ README.md - 详细说明文档

🚀 快速启动
-----------
直接双击运行：MouseKeyboardRecorder.exe

⌨️ 快捷键操作
-----------
F9  - 开始/暂停录制
F10 - 开始播放
F11 - 停止播放/录制

🎮 主要功能
-----------
✓ 录制鼠标移动、点击、滚轮操作
✓ 录制键盘按键（支持组合键）
✓ 设置循环次数（1次、多次、无限循环）
✓ 暂停/继续录制功能
✓ 保存/加载录制文件
✓ 简洁的图形界面

📋 使用流程
-----------
1. 双击运行 MouseKeyboardRecorder.exe
2. 按F9开始录制您的操作
3. 执行需要录制的鼠标键盘动作
4. 再按F9停止录制
5. 设置循环次数（或勾选无限循环）
6. 按F10开始播放录制的动作
7. 按F11可随时停止播放

💾 保存功能
-----------
- 点击"保存录制"可将操作保存为文件
- 点击"加载录制"可重新使用之前的录制
- 支持JSON格式，便于分享和备份

⚠️ 注意事项
-----------
- 程序标题显示"测试版本"，这是正常的
- 某些系统可能需要管理员权限运行
- 请勿录制包含敏感信息的操作
- 建议先在安全环境中测试

🔧 故障排除
-----------
如果程序无法启动：
1. 确保Windows系统版本兼容
2. 尝试以管理员身份运行
3. 检查防病毒软件是否阻止运行

📞 技术支持
-----------
如遇问题，请检查：
- 系统权限设置
- 防病毒软件设置
- Windows版本兼容性

版本信息：v1.0 测试版
支持系统：Windows 7/8/10/11
文件大小：约16MB（包含所有依赖）

祝您使用愉快！
